/**
 * <PERSON>ript to disable usage tracking in Augment extension
 * This will patch the extension to prevent sending usage data while keeping local credit display
 *
 * ⚠️ WARNING: This modifies the Augment extension files. Backup originals first!
 * ⚠️ This may violate Augment's terms of service. Use at your own risk.
 *
 * Run with: node disable-augment-tracking.js
 */

// Check if running in Node.js
if (typeof require === 'undefined') {
  WScript.Echo("This script must be run with Node.js, not Windows Script Host");
  WScript.Echo("Please run: node disable-augment-tracking.js");
  WScript.Quit(1);
}

const fs = require('fs');
const path = require('path');
const os = require('os');

// Path to Augment extension
const AUGMENT_PATH = path.join(
  os.homedir(),
  '.vscode',
  'extensions',
  'augment.vscode-augment-0.464.0'
);

const EXTENSION_JS_PATH = path.join(AUGMENT_PATH, 'out', 'extension.js');
const BACKUP_PATH = path.join(AUGMENT_PATH, 'out', 'extension.js.backup');

console.log('🔧 Augment Usage Tracking Disabler');
console.log('==================================');

// Check if Augment extension exists
if (!fs.existsSync(AUGMENT_PATH)) {
  console.error('❌ Augment extension not found at:', AUGMENT_PATH);
  console.log('Please make sure Augment extension is installed.');
  process.exit(1);
}

if (!fs.existsSync(EXTENSION_JS_PATH)) {
  console.error('❌ Extension file not found at:', EXTENSION_JS_PATH);
  process.exit(1);
}

// Create backup
console.log('📦 Creating backup...');
if (!fs.existsSync(BACKUP_PATH)) {
  fs.copyFileSync(EXTENSION_JS_PATH, BACKUP_PATH);
  console.log('✅ Backup created at:', BACKUP_PATH);
} else {
  console.log('ℹ️  Backup already exists');
}

// Read the extension file
console.log('📖 Reading extension file...');
let extensionCode = fs.readFileSync(EXTENSION_JS_PATH, 'utf8');

// Apply patches to disable tracking
console.log('🔨 Applying patches...');

// 1. Disable telemetry/analytics calls
const telemetryPatches = [
  // Common telemetry patterns
  {
    pattern: /\.track\s*\(/g,
    replacement: '.trackDisabled('
  },
  {
    pattern: /\.sendTelemetry\s*\(/g,
    replacement: '.sendTelemetryDisabled('
  },
  {
    pattern: /\.analytics\s*\(/g,
    replacement: '.analyticsDisabled('
  },
  {
    pattern: /\.reportUsage\s*\(/g,
    replacement: '.reportUsageDisabled('
  },
  // HTTP requests to analytics endpoints
  {
    pattern: /fetch\s*\(\s*["`']https:\/\/[^"`']*analytics[^"`']*["`']/g,
    replacement: 'fetch("http://localhost:0" // DISABLED'
  },
  {
    pattern: /fetch\s*\(\s*["`']https:\/\/[^"`']*telemetry[^"`']*["`']/g,
    replacement: 'fetch("http://localhost:0" // DISABLED'
  },
  {
    pattern: /fetch\s*\(\s*["`']https:\/\/[^"`']*usage[^"`']*["`']/g,
    replacement: 'fetch("http://localhost:0" // DISABLED'
  }
];

// 2. Add mock functions to prevent errors
const mockFunctions = `
// PATCHED: Mock functions to disable tracking
function trackDisabled() { console.log('[AUGMENT] Tracking disabled'); return Promise.resolve(); }
function sendTelemetryDisabled() { console.log('[AUGMENT] Telemetry disabled'); return Promise.resolve(); }
function analyticsDisabled() { console.log('[AUGMENT] Analytics disabled'); return Promise.resolve(); }
function reportUsageDisabled() { console.log('[AUGMENT] Usage reporting disabled'); return Promise.resolve(); }
`;

// Apply patches
let patchCount = 0;
telemetryPatches.forEach(patch => {
  const matches = extensionCode.match(patch.pattern);
  if (matches) {
    extensionCode = extensionCode.replace(patch.pattern, patch.replacement);
    patchCount += matches.length;
    console.log(`✅ Applied patch: ${patch.pattern} (${matches.length} replacements)`);
  }
});

// Add mock functions at the beginning
extensionCode = mockFunctions + '\n' + extensionCode;

// 3. Patch network requests to usage/billing endpoints
const networkPatches = [
  // Block requests to Augment's backend for usage tracking
  {
    pattern: /(https:\/\/[^"'`\s]*augmentcode\.com[^"'`\s]*\/api\/[^"'`\s]*usage[^"'`\s]*)/g,
    replacement: '"http://localhost:0" // BLOCKED USAGE ENDPOINT'
  },
  {
    pattern: /(https:\/\/[^"'`\s]*augmentcode\.com[^"'`\s]*\/api\/[^"'`\s]*billing[^"'`\s]*)/g,
    replacement: '"http://localhost:0" // BLOCKED BILLING ENDPOINT'
  },
  {
    pattern: /(https:\/\/[^"'`\s]*augmentcode\.com[^"'`\s]*\/api\/[^"'`\s]*analytics[^"'`\s]*)/g,
    replacement: '"http://localhost:0" // BLOCKED ANALYTICS ENDPOINT'
  }
];

networkPatches.forEach(patch => {
  const matches = extensionCode.match(patch.pattern);
  if (matches) {
    extensionCode = extensionCode.replace(patch.pattern, patch.replacement);
    patchCount += matches.length;
    console.log(`✅ Blocked network endpoint: ${matches.length} URLs`);
  }
});

// 4. Add local credit system override
const localCreditSystem = `
// PATCHED: Local credit system override
const LOCAL_CREDIT_OVERRIDE = {
  enabled: true,
  credits: {
    total: 10000,
    used: 0,
    remaining: 10000
  },

  // Mock credit check that always returns true
  checkCredits: function() {
    return Promise.resolve(true);
  },

  // Mock credit deduction that does nothing
  deductCredits: function(amount) {
    console.log('[AUGMENT] Would deduct', amount, 'credits (disabled)');
    return Promise.resolve();
  },

  // Mock credit balance that returns fake data
  getCreditBalance: function() {
    return Promise.resolve(this.credits);
  }
};

// Override credit-related functions
if (typeof window !== 'undefined') {
  window.LOCAL_CREDIT_OVERRIDE = LOCAL_CREDIT_OVERRIDE;
}
`;

extensionCode = localCreditSystem + '\n' + extensionCode;

// 5. Patch credit balance display to use local data
const creditDisplayPatches = [
  {
    pattern: /\.getCreditBalance\s*\(\s*\)/g,
    replacement: '.getCreditBalance() || LOCAL_CREDIT_OVERRIDE.getCreditBalance()'
  },
  {
    pattern: /\.checkCredits\s*\(/g,
    replacement: '.checkCredits() || LOCAL_CREDIT_OVERRIDE.checkCredits('
  }
];

creditDisplayPatches.forEach(patch => {
  const matches = extensionCode.match(patch.pattern);
  if (matches) {
    extensionCode = extensionCode.replace(patch.pattern, patch.replacement);
    patchCount += matches.length;
    console.log(`✅ Patched credit system: ${matches.length} replacements`);
  }
});

// Write the patched file
console.log('💾 Writing patched extension...');
fs.writeFileSync(EXTENSION_JS_PATH, extensionCode);

console.log('');
console.log('🎉 Patching complete!');
console.log(`📊 Applied ${patchCount} patches total`);
console.log('');
console.log('📋 What was changed:');
console.log('  ✅ Disabled telemetry and analytics calls');
console.log('  ✅ Blocked network requests to usage tracking endpoints');
console.log('  ✅ Added local credit system override');
console.log('  ✅ Kept credit display functionality');
console.log('');
console.log('⚠️  Important notes:');
console.log('  • Restart VS Code for changes to take effect');
console.log('  • Extension updates will overwrite these changes');
console.log('  • Backup saved at:', BACKUP_PATH);
console.log('  • This may violate Augment\'s terms of service');
console.log('');
console.log('🔄 To restore original extension:');
console.log(`  node restore-augment.js`);
