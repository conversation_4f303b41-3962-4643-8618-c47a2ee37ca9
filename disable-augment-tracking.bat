@echo off
echo.
echo ========================================
echo   Augment Usage Tracking Disabler
echo ========================================
echo.
echo WARNING: This modifies Augment extension files
echo Make sure to backup originals first!
echo.

REM Check if Node.js is available
node --version >nul 2>&1
if errorlevel 1 (
    echo ERROR: Node.js not found!
    echo Please install Node.js from https://nodejs.org/
    pause
    exit /b 1
)

REM Set paths
set "AUGMENT_PATH=%USERPROFILE%\.vscode\extensions\augment.vscode-augment-0.464.0"
set "EXTENSION_FILE=%AUGMENT_PATH%\out\extension.js"
set "BACKUP_FILE=%AUGMENT_PATH%\out\extension.js.backup"

echo Checking Augment extension...
if not exist "%AUGMENT_PATH%" (
    echo ERROR: Augment extension not found!
    echo Expected location: %AUGMENT_PATH%
    echo Please make sure Augment extension is installed.
    pause
    exit /b 1
)

if not exist "%EXTENSION_FILE%" (
    echo ERROR: Extension file not found!
    echo Expected file: %EXTENSION_FILE%
    pause
    exit /b 1
)

echo.
echo Found Augment extension at: %AUGMENT_PATH%
echo.

REM Create backup
echo Creating backup...
if not exist "%BACKUP_FILE%" (
    copy "%EXTENSION_FILE%" "%BACKUP_FILE%" >nul
    echo ✓ Backup created: extension.js.backup
) else (
    echo ✓ Backup already exists
)

echo.
echo Running Node.js patcher...
node disable-augment-tracking.js

if errorlevel 1 (
    echo.
    echo ERROR: Patching failed!
    pause
    exit /b 1
)

echo.
echo ========================================
echo   Patching Complete!
echo ========================================
echo.
echo Next steps:
echo 1. Restart VS Code completely
echo 2. Check console for "[AUGMENT] Tracking disabled" messages
echo 3. Verify Augment features still work
echo.
echo To restore original: run restore-augment.bat
echo.
pause
