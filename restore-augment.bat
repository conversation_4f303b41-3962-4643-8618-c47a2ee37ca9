@echo off
echo.
echo ========================================
echo   Augment Extension Restorer
echo ========================================
echo.

REM Set paths
set "AUGMENT_PATH=%USERPROFILE%\.vscode\extensions\augment.vscode-augment-0.464.0"
set "EXTENSION_FILE=%AUGMENT_PATH%\out\extension.js"
set "BACKUP_FILE=%AUGMENT_PATH%\out\extension.js.backup"

echo Checking for backup file...
if not exist "%BACKUP_FILE%" (
    echo ERROR: Backup file not found!
    echo Expected: %BACKUP_FILE%
    echo Cannot restore original extension.
    pause
    exit /b 1
)

echo Found backup file: %BACKUP_FILE%
echo.

echo Restoring original extension...
copy "%BACKUP_FILE%" "%EXTENSION_FILE%" >nul

if errorlevel 1 (
    echo ERROR: Failed to restore extension!
    pause
    exit /b 1
)

echo.
echo ========================================
echo   Restoration Complete!
echo ========================================
echo.
echo ✓ Original Augment extension restored
echo ✓ Please restart VS Code for changes to take effect
echo.
pause
