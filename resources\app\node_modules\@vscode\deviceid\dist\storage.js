"use strict";
/*---------------------------------------------------------------------------------------------
 *  Copyright (c) Microsoft Corporation. All rights reserved.
 *  Licensed under the MIT License. See LICENSE in the project root for license information.
 *--------------------------------------------------------------------------------------------*/
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || function (mod) {
    if (mod && mod.__esModule) return mod;
    var result = {};
    if (mod != null) for (var k in mod) if (k !== "default" && Object.prototype.hasOwnProperty.call(mod, k)) __createBinding(result, mod, k);
    __setModuleDefault(result, mod);
    return result;
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.setDeviceId = exports.getDeviceId = void 0;
const path = __importStar(require("path"));
const fs = __importStar(require("fs-extra"));
const windowRegistry = process.platform === "win32"
    ? require("../build/Release/windows.node")
    : null;
const deviceIdFileName = "deviceid";
function getDirectory() {
    let folder;
    if (!process.env.HOME) {
        throw new Error("Home directory not found");
    }
    if (process.platform === "darwin") {
        folder = path.join(process.env.HOME, "Library", "Application Support");
    }
    else if (process.platform === "linux") {
        folder =
            process.env.XDG_CACHE_HOME ?? path.join(process.env.HOME, ".cache");
    }
    else {
        throw new Error("Unsupported platform");
    }
    return path.join(folder, "Microsoft", "DeveloperTools");
}
function getDeviceIdFilePath() {
    return path.join(getDirectory(), deviceIdFileName);
}
async function getDeviceId() {
    if (process.platform === "win32") {
        return windowRegistry?.GetDeviceId();
    }
    else {
        if (!fs.existsSync(getDeviceIdFilePath())) {
            return undefined;
        }
        else {
            return fs.readFile(getDeviceIdFilePath(), "utf8");
        }
    }
}
exports.getDeviceId = getDeviceId;
async function setDeviceId(deviceId) {
    if (process.platform === "win32") {
        windowRegistry?.SetDeviceId(deviceId);
    }
    else {
        await fs.ensureDir(getDirectory());
        await fs.writeFile(getDeviceIdFilePath(), deviceId, "utf8");
    }
}
exports.setDeviceId = setDeviceId;//# sourceMappingURL=https://main.vscode-cdn.net/sourcemaps/17baf841131aa23349f217ca7c570c76ee87b957/node_modules/@vscode/deviceid/dist/storage.js.map