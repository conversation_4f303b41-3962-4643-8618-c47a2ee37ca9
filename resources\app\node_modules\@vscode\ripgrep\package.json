{"name": "@vscode/ripgrep", "version": "1.15.11", "description": "A module for using ripgrep in a Node project", "main": "lib/index.js", "typings": "lib/index.d.ts", "repository": {"type": "git", "url": "https://github.com/microsoft/vscode-ripgrep"}, "scripts": {"postinstall": "node ./lib/postinstall.js"}, "author": "<PERSON>", "license": "MIT", "dependencies": {"https-proxy-agent": "^7.0.2", "yauzl": "^2.9.2", "proxy-from-env": "^1.1.0"}, "devDependencies": {"@types/node": "^20.8.4"}}