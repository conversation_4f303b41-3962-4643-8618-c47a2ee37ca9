{"name": "@xterm/headless", "description": "A headless terminal component that runs in Node.js", "version": "5.6.0-beta.99", "main": "lib-headless/xterm-headless.js", "module": "lib/xterm.mjs", "types": "typings/xterm-headless.d.ts", "repository": "https://github.com/xtermjs/xterm.js", "license": "MIT", "keywords": ["cli", "command-line", "console", "pty", "shell", "ssh", "styles", "terminal-emulator", "terminal", "tty", "vt100", "webgl", "xterm"], "commit": "a260f7d2889142d6566a66cb9856a07050dea611"}