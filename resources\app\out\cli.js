/*!--------------------------------------------------------
 * Copyright (C) Microsoft Corporation. All rights reserved.
 *--------------------------------------------------------*/var n1=function(e,t){return n1=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(s,r){s.__proto__=r}||function(s,r){for(var i in r)Object.prototype.hasOwnProperty.call(r,i)&&(s[i]=r[i])},n1(e,t)};export function __extends(e,t){if(typeof t!="function"&&t!==null)throw new TypeError("Class extends value "+String(t)+" is not a constructor or null");n1(e,t);function s(){this.constructor=e}e.prototype=t===null?Object.create(t):(s.prototype=t.prototype,new s)}export var __assign=function(){return __assign=Object.assign||function(t){for(var s,r=1,i=arguments.length;r<i;r++){s=arguments[r];for(var n in s)Object.prototype.hasOwnProperty.call(s,n)&&(t[n]=s[n])}return t},__assign.apply(this,arguments)};export function __rest(e,t){var s={};for(var r in e)Object.prototype.hasOwnProperty.call(e,r)&&t.indexOf(r)<0&&(s[r]=e[r]);if(e!=null&&typeof Object.getOwnPropertySymbols=="function")for(var i=0,r=Object.getOwnPropertySymbols(e);i<r.length;i++)t.indexOf(r[i])<0&&Object.prototype.propertyIsEnumerable.call(e,r[i])&&(s[r[i]]=e[r[i]]);return s}export function __decorate(e,t,s,r){var i=arguments.length,n=i<3?t:r===null?r=Object.getOwnPropertyDescriptor(t,s):r,a;if(typeof Reflect=="object"&&typeof Reflect.decorate=="function")n=Reflect.decorate(e,t,s,r);else for(var o=e.length-1;o>=0;o--)(a=e[o])&&(n=(i<3?a(n):i>3?a(t,s,n):a(t,s))||n);return i>3&&n&&Object.defineProperty(t,s,n),n}export function __param(e,t){return function(s,r){t(s,r,e)}}export function __esDecorate(e,t,s,r,i,n){function a(C){if(C!==void 0&&typeof C!="function")throw new TypeError("Function expected");return C}for(var o=r.kind,l=o==="getter"?"get":o==="setter"?"set":"value",c=!t&&e?r.static?e:e.prototype:null,f=t||(c?Object.getOwnPropertyDescriptor(c,r.name):{}),u,h=!1,d=s.length-1;d>=0;d--){var p={};for(var y in r)p[y]=y==="access"?{}:r[y];for(var y in r.access)p.access[y]=r.access[y];p.addInitializer=function(C){if(h)throw new TypeError("Cannot add initializers after decoration has completed");n.push(a(C||null))};var E=(0,s[d])(o==="accessor"?{get:f.get,set:f.set}:f[l],p);if(o==="accessor"){if(E===void 0)continue;if(E===null||typeof E!="object")throw new TypeError("Object expected");(u=a(E.get))&&(f.get=u),(u=a(E.set))&&(f.set=u),(u=a(E.init))&&i.unshift(u)}else(u=a(E))&&(o==="field"?i.unshift(u):f[l]=u)}c&&Object.defineProperty(c,r.name,f),h=!0}export function __runInitializers(e,t,s){for(var r=arguments.length>2,i=0;i<t.length;i++)s=r?t[i].call(e,s):t[i].call(e);return r?s:void 0}export function __propKey(e){return typeof e=="symbol"?e:"".concat(e)}export function __setFunctionName(e,t,s){return typeof t=="symbol"&&(t=t.description?"[".concat(t.description,"]"):""),Object.defineProperty(e,"name",{configurable:!0,value:s?"".concat(s," ",t):t})}export function __metadata(e,t){if(typeof Reflect=="object"&&typeof Reflect.metadata=="function")return Reflect.metadata(e,t)}export function __awaiter(e,t,s,r){function i(n){return n instanceof s?n:new s(function(a){a(n)})}return new(s||(s=Promise))(function(n,a){function o(f){try{c(r.next(f))}catch(u){a(u)}}function l(f){try{c(r.throw(f))}catch(u){a(u)}}function c(f){f.done?n(f.value):i(f.value).then(o,l)}c((r=r.apply(e,t||[])).next())})}export function __generator(e,t){var s={label:0,sent:function(){if(n[0]&1)throw n[1];return n[1]},trys:[],ops:[]},r,i,n,a;return a={next:o(0),throw:o(1),return:o(2)},typeof Symbol=="function"&&(a[Symbol.iterator]=function(){return this}),a;function o(c){return function(f){return l([c,f])}}function l(c){if(r)throw new TypeError("Generator is already executing.");for(;a&&(a=0,c[0]&&(s=0)),s;)try{if(r=1,i&&(n=c[0]&2?i.return:c[0]?i.throw||((n=i.return)&&n.call(i),0):i.next)&&!(n=n.call(i,c[1])).done)return n;switch(i=0,n&&(c=[c[0]&2,n.value]),c[0]){case 0:case 1:n=c;break;case 4:return s.label++,{value:c[1],done:!1};case 5:s.label++,i=c[1],c=[0];continue;case 7:c=s.ops.pop(),s.trys.pop();continue;default:if(n=s.trys,!(n=n.length>0&&n[n.length-1])&&(c[0]===6||c[0]===2)){s=0;continue}if(c[0]===3&&(!n||c[1]>n[0]&&c[1]<n[3])){s.label=c[1];break}if(c[0]===6&&s.label<n[1]){s.label=n[1],n=c;break}if(n&&s.label<n[2]){s.label=n[2],s.ops.push(c);break}n[2]&&s.ops.pop(),s.trys.pop();continue}c=t.call(e,s)}catch(f){c=[6,f],i=0}finally{r=n=0}if(c[0]&5)throw c[1];return{value:c[0]?c[1]:void 0,done:!0}}}export var __createBinding=Object.create?function(e,t,s,r){r===void 0&&(r=s);var i=Object.getOwnPropertyDescriptor(t,s);(!i||("get"in i?!t.__esModule:i.writable||i.configurable))&&(i={enumerable:!0,get:function(){return t[s]}}),Object.defineProperty(e,r,i)}:function(e,t,s,r){r===void 0&&(r=s),e[r]=t[s]};export function __exportStar(e,t){for(var s in e)s!=="default"&&!Object.prototype.hasOwnProperty.call(t,s)&&__createBinding(t,e,s)}export function __values(e){var t=typeof Symbol=="function"&&Symbol.iterator,s=t&&e[t],r=0;if(s)return s.call(e);if(e&&typeof e.length=="number")return{next:function(){return e&&r>=e.length&&(e=void 0),{value:e&&e[r++],done:!e}}};throw new TypeError(t?"Object is not iterable.":"Symbol.iterator is not defined.")}export function __read(e,t){var s=typeof Symbol=="function"&&e[Symbol.iterator];if(!s)return e;var r=s.call(e),i,n=[],a;try{for(;(t===void 0||t-- >0)&&!(i=r.next()).done;)n.push(i.value)}catch(o){a={error:o}}finally{try{i&&!i.done&&(s=r.return)&&s.call(r)}finally{if(a)throw a.error}}return n}export function __spread(){for(var e=[],t=0;t<arguments.length;t++)e=e.concat(__read(arguments[t]));return e}export function __spreadArrays(){for(var e=0,t=0,s=arguments.length;t<s;t++)e+=arguments[t].length;for(var r=Array(e),i=0,t=0;t<s;t++)for(var n=arguments[t],a=0,o=n.length;a<o;a++,i++)r[i]=n[a];return r}export function __spreadArray(e,t,s){if(s||arguments.length===2)for(var r=0,i=t.length,n;r<i;r++)(n||!(r in t))&&(n||(n=Array.prototype.slice.call(t,0,r)),n[r]=t[r]);return e.concat(n||Array.prototype.slice.call(t))}export function __await(e){return this instanceof __await?(this.v=e,this):new __await(e)}export function __asyncGenerator(e,t,s){if(!Symbol.asyncIterator)throw new TypeError("Symbol.asyncIterator is not defined.");var r=s.apply(e,t||[]),i,n=[];return i={},o("next"),o("throw"),o("return",a),i[Symbol.asyncIterator]=function(){return this},i;function a(d){return function(p){return Promise.resolve(p).then(d,u)}}function o(d,p){r[d]&&(i[d]=function(y){return new Promise(function(E,C){n.push([d,y,E,C])>1||l(d,y)})},p&&(i[d]=p(i[d])))}function l(d,p){try{c(r[d](p))}catch(y){h(n[0][3],y)}}function c(d){d.value instanceof __await?Promise.resolve(d.value.v).then(f,u):h(n[0][2],d)}function f(d){l("next",d)}function u(d){l("throw",d)}function h(d,p){d(p),n.shift(),n.length&&l(n[0][0],n[0][1])}}export function __asyncDelegator(e){var t,s;return t={},r("next"),r("throw",function(i){throw i}),r("return"),t[Symbol.iterator]=function(){return this},t;function r(i,n){t[i]=e[i]?function(a){return(s=!s)?{value:__await(e[i](a)),done:!1}:n?n(a):a}:n}}export function __asyncValues(e){if(!Symbol.asyncIterator)throw new TypeError("Symbol.asyncIterator is not defined.");var t=e[Symbol.asyncIterator],s;return t?t.call(e):(e=typeof __values=="function"?__values(e):e[Symbol.iterator](),s={},r("next"),r("throw"),r("return"),s[Symbol.asyncIterator]=function(){return this},s);function r(n){s[n]=e[n]&&function(a){return new Promise(function(o,l){a=e[n](a),i(o,l,a.done,a.value)})}}function i(n,a,o,l){Promise.resolve(l).then(function(c){n({value:c,done:o})},a)}}export function __makeTemplateObject(e,t){return Object.defineProperty?Object.defineProperty(e,"raw",{value:t}):e.raw=t,e}var kn=Object.create?function(e,t){Object.defineProperty(e,"default",{enumerable:!0,value:t})}:function(e,t){e.default=t};export function __importStar(e){if(e&&e.__esModule)return e;var t={};if(e!=null)for(var s in e)s!=="default"&&Object.prototype.hasOwnProperty.call(e,s)&&__createBinding(t,e,s);return kn(t,e),t}export function __importDefault(e){return e&&e.__esModule?e:{default:e}}export function __classPrivateFieldGet(e,t,s,r){if(s==="a"&&!r)throw new TypeError("Private accessor was defined without a getter");if(typeof t=="function"?e!==t||!r:!t.has(e))throw new TypeError("Cannot read private member from an object whose class did not declare it");return s==="m"?r:s==="a"?r.call(e):r?r.value:t.get(e)}export function __classPrivateFieldSet(e,t,s,r,i){if(r==="m")throw new TypeError("Private method is not writable");if(r==="a"&&!i)throw new TypeError("Private accessor was defined without a setter");if(typeof t=="function"?e!==t||!i:!t.has(e))throw new TypeError("Cannot write private member to an object whose class did not declare it");return r==="a"?i.call(e,s):i?i.value=s:t.set(e,s),s}export function __classPrivateFieldIn(e,t){if(t===null||typeof t!="object"&&typeof t!="function")throw new TypeError("Cannot use 'in' operator on non-object");return typeof e=="function"?t===e:e.has(t)}export function __addDisposableResource(e,t,s){if(t!=null){if(typeof t!="object"&&typeof t!="function")throw new TypeError("Object expected.");var r,i;if(s){if(!Symbol.asyncDispose)throw new TypeError("Symbol.asyncDispose is not defined.");r=t[Symbol.asyncDispose]}if(r===void 0){if(!Symbol.dispose)throw new TypeError("Symbol.dispose is not defined.");r=t[Symbol.dispose],s&&(i=r)}if(typeof r!="function")throw new TypeError("Object not disposable.");i&&(r=function(){try{i.call(this)}catch(n){return Promise.reject(n)}}),e.stack.push({value:t,dispose:r,async:s})}else s&&e.stack.push({async:!0});return t}var Nn=typeof SuppressedError=="function"?SuppressedError:function(e,t,s){var r=new Error(s);return r.name="SuppressedError",r.error=e,r.suppressed=t,r};export function __disposeResources(e){function t(r){e.error=e.hasError?new Nn(r,e.error,"An error was suppressed during disposal."):r,e.hasError=!0}function s(){for(;e.stack.length;){var r=e.stack.pop();try{var i=r.dispose&&r.dispose.call(r.value);if(r.async)return Promise.resolve(i).then(s,function(n){return t(n),s()})}catch(n){t(n)}}if(e.hasError)throw e.error}return s()}export default{__extends,__assign,__rest,__decorate,__param,__metadata,__awaiter,__generator,__createBinding,__exportStar,__values,__read,__spread,__spreadArrays,__spreadArray,__await,__asyncGenerator,__asyncDelegator,__asyncValues,__makeTemplateObject,__importStar,__importDefault,__classPrivateFieldGet,__classPrivateFieldSet,__classPrivateFieldIn,__addDisposableResource,__disposeResources};var Rn=Object.create,o1=Object.defineProperty,Tn=Object.getOwnPropertyDescriptor,a1=Object.getOwnPropertyNames,In=Object.getPrototypeOf,jn=Object.prototype.hasOwnProperty,O=(e,t)=>function(){return e&&(t=(0,e[a1(e)[0]])(e=0)),t},Fn=(e,t)=>function(){return t||(0,e[a1(e)[0]])((t={exports:{}}).exports,t),t.exports},Mn=(e,t)=>{for(var s in t)o1(e,s,{get:t[s],enumerable:!0})},Un=(e,t,s,r)=>{if(t&&typeof t=="object"||typeof t=="function")for(let i of a1(t))!jn.call(e,i)&&i!==s&&o1(e,i,{get:()=>t[i],enumerable:!(r=Tn(t,i))||r.enumerable});return e},Vn=(e,t,s)=>(s=e!=null?Rn(In(e)):{},Un(t||!e||!e.__esModule?o1(s,"default",{value:e,enumerable:!0}):s,e));function qn(e,t){const s=Object.create(null);for(const r of e){const i=t(r);let n=s[i];n||(n=s[i]=[]),n.push(r)}return s}var vs,Wn,Es=O({"out-build/vs/base/common/collections.js"(){"use strict";Wn=class{static{vs=Symbol.toStringTag}constructor(e,t){this.b=t,this.a=new Map,this[vs]="SetWithKey";for(const s of e)this.add(s)}get size(){return this.a.size}add(e){const t=this.b(e);return this.a.set(t,e),this}delete(e){return this.a.delete(this.b(e))}has(e){return this.a.has(this.b(e))}*entries(){for(const e of this.a.values())yield[e,e]}keys(){return this.values()}*values(){for(const e of this.a.values())yield e}clear(){this.a.clear()}forEach(e,t){this.a.forEach(s=>e.call(t,s,s,this))}[Symbol.iterator](){return this.values()}}}});function xt(e){Bn(e)||$s.onUnexpectedError(e)}function Bn(e){return e instanceof Ze?!0:e instanceof Error&&e.name===Dt&&e.message===Dt}var _s,$s,Dt,Ze,l1,Xe=O({"out-build/vs/base/common/errors.js"(){"use strict";_s=class{constructor(){this.b=[],this.a=function(e){setTimeout(()=>{throw e.stack?l1.isErrorNoTelemetry(e)?new l1(e.message+`

`+e.stack):new Error(e.message+`

`+e.stack):e},0)}}addListener(e){return this.b.push(e),()=>{this.d(e)}}c(e){this.b.forEach(t=>{t(e)})}d(e){this.b.splice(this.b.indexOf(e),1)}setUnexpectedErrorHandler(e){this.a=e}getUnexpectedErrorHandler(){return this.a}onUnexpectedError(e){this.a(e),this.c(e)}onUnexpectedExternalError(e){this.a(e)}},$s=new _s,Dt="Canceled",Ze=class extends Error{constructor(){super(Dt),this.name=this.message}},l1=class ms extends Error{constructor(t){super(t),this.name="CodeExpectedError"}static fromError(t){if(t instanceof ms)return t;const s=new ms;return s.message=t.message,s.stack=t.stack,s}static isErrorNoTelemetry(t){return t.name==="CodeExpectedError"}}}});function zn(e,t){const s=this;let r=!1,i;return function(){if(r)return i;if(r=!0,t)try{i=e.apply(s,arguments)}finally{t()}else i=e.apply(s,arguments);return i}}var Cs=O({"out-build/vs/base/common/functional.js"(){"use strict"}});function Hn(e,t,s=0,r=e.length){let i=s,n=r;for(;i<n;){const a=Math.floor((i+n)/2);t(e[a])?i=a+1:n=a}return i-1}var Gn,Kn=O({"out-build/vs/base/common/arraysFind.js"(){"use strict";Gn=class $n{static{this.assertInvariants=!1}constructor(t){this.e=t,this.c=0}findLastMonotonous(t){if($n.assertInvariants){if(this.d){for(const r of this.e)if(this.d(r)&&!t(r))throw new Error("MonotonousArray: current predicate must be weaker than (or equal to) the previous predicate.")}this.d=t}const s=Hn(this.e,t,this.c);return this.c=s+1,s===-1?void 0:this.e[s]}}}});function As(e,t){let s;if(typeof t=="number"){let r=t;s=()=>{const i=Math.sin(r++)*179426549;return i-Math.floor(i)}}else s=Math.random;for(let r=e.length-1;r>0;r-=1){const i=Math.floor(s()*(r+1)),n=e[r];e[r]=e[i],e[i]=n}}function Jn(e,t){return(s,r)=>t(e(s),e(r))}var c1,Os,Qn,u1=O({"out-build/vs/base/common/arrays.js"(){"use strict";Kn(),Xe(),function(e){function t(n){return n<0}e.isLessThan=t;function s(n){return n<=0}e.isLessThanOrEqual=s;function r(n){return n>0}e.isGreaterThan=r;function i(n){return n===0}e.isNeitherLessOrGreaterThan=i,e.greaterThan=1,e.lessThan=-1,e.neitherLessOrGreaterThan=0}(c1||(c1={})),Os=(e,t)=>e-t,Qn=class Xt{static{this.empty=new Xt(t=>{})}constructor(t){this.iterate=t}forEach(t){this.iterate(s=>(t(s),!0))}toArray(){const t=[];return this.iterate(s=>(t.push(s),!0)),t}filter(t){return new Xt(s=>this.iterate(r=>t(r)?s(r):!0))}map(t){return new Xt(s=>this.iterate(r=>s(t(r))))}some(t){let s=!1;return this.iterate(r=>(s=t(r),!s)),s}findFirst(t){let s;return this.iterate(r=>t(r)?(s=r,!1):!0),s}findLast(t){let s;return this.iterate(r=>(t(r)&&(s=r),!0)),s}findLastMaxBy(t){let s,r=!0;return this.iterate(i=>((r||c1.isGreaterThan(t(i,s)))&&(r=!1,s=i),!0)),s}}}});function Yn(e){return Array.isArray(e)}var Ss,xs,Ds,Ps,f1,Zn,Ls,ks,Ns,Pt,Rs,h1=O({"out-build/vs/base/common/map.js"(){"use strict";Ps=class{constructor(e,t){this.uri=e,this.value=t}},f1=class Et{static{this.c=t=>t.toString()}constructor(t,s){if(this[Ss]="ResourceMap",t instanceof Et)this.d=new Map(t.d),this.e=s??Et.c;else if(Yn(t)){this.d=new Map,this.e=s??Et.c;for(const[r,i]of t)this.set(r,i)}else this.d=new Map,this.e=t??Et.c}set(t,s){return this.d.set(this.e(t),new Ps(t,s)),this}get(t){return this.d.get(this.e(t))?.value}has(t){return this.d.has(this.e(t))}get size(){return this.d.size}clear(){this.d.clear()}delete(t){return this.d.delete(this.e(t))}forEach(t,s){typeof s<"u"&&(t=t.bind(s));for(const[r,i]of this.d)t(i.value,i.uri,this)}*values(){for(const t of this.d.values())yield t.value}*keys(){for(const t of this.d.values())yield t.uri}*entries(){for(const t of this.d.values())yield[t.uri,t.value]}*[(Ss=Symbol.toStringTag,Symbol.iterator)](){for(const[,t]of this.d)yield[t.uri,t.value]}},Zn=class{constructor(e,t){this[xs]="ResourceSet",!e||typeof e=="function"?this.c=new f1(e):(this.c=new f1(t),e.forEach(this.add,this))}get size(){return this.c.size}add(e){return this.c.set(e,e),this}clear(){this.c.clear()}delete(e){return this.c.delete(e)}forEach(e,t){this.c.forEach((s,r)=>e.call(t,r,r,this))}has(e){return this.c.has(e)}entries(){return this.c.entries()}keys(){return this.c.keys()}values(){return this.c.keys()}[(xs=Symbol.toStringTag,Symbol.iterator)](){return this.keys()}},function(e){e[e.None=0]="None",e[e.AsOld=1]="AsOld",e[e.AsNew=2]="AsNew"}(Ls||(Ls={})),ks=class{constructor(){this[Ds]="LinkedMap",this.c=new Map,this.d=void 0,this.e=void 0,this.f=0,this.g=0}clear(){this.c.clear(),this.d=void 0,this.e=void 0,this.f=0,this.g++}isEmpty(){return!this.d&&!this.e}get size(){return this.f}get first(){return this.d?.value}get last(){return this.e?.value}has(e){return this.c.has(e)}get(e,t=0){const s=this.c.get(e);if(s)return t!==0&&this.n(s,t),s.value}set(e,t,s=0){let r=this.c.get(e);if(r)r.value=t,s!==0&&this.n(r,s);else{switch(r={key:e,value:t,next:void 0,previous:void 0},s){case 0:this.l(r);break;case 1:this.k(r);break;case 2:this.l(r);break;default:this.l(r);break}this.c.set(e,r),this.f++}return this}delete(e){return!!this.remove(e)}remove(e){const t=this.c.get(e);if(t)return this.c.delete(e),this.m(t),this.f--,t.value}shift(){if(!this.d&&!this.e)return;if(!this.d||!this.e)throw new Error("Invalid list");const e=this.d;return this.c.delete(e.key),this.m(e),this.f--,e.value}forEach(e,t){const s=this.g;let r=this.d;for(;r;){if(t?e.bind(t)(r.value,r.key,this):e(r.value,r.key,this),this.g!==s)throw new Error("LinkedMap got modified during iteration.");r=r.next}}keys(){const e=this,t=this.g;let s=this.d;const r={[Symbol.iterator](){return r},next(){if(e.g!==t)throw new Error("LinkedMap got modified during iteration.");if(s){const i={value:s.key,done:!1};return s=s.next,i}else return{value:void 0,done:!0}}};return r}values(){const e=this,t=this.g;let s=this.d;const r={[Symbol.iterator](){return r},next(){if(e.g!==t)throw new Error("LinkedMap got modified during iteration.");if(s){const i={value:s.value,done:!1};return s=s.next,i}else return{value:void 0,done:!0}}};return r}entries(){const e=this,t=this.g;let s=this.d;const r={[Symbol.iterator](){return r},next(){if(e.g!==t)throw new Error("LinkedMap got modified during iteration.");if(s){const i={value:[s.key,s.value],done:!1};return s=s.next,i}else return{value:void 0,done:!0}}};return r}[(Ds=Symbol.toStringTag,Symbol.iterator)](){return this.entries()}h(e){if(e>=this.size)return;if(e===0){this.clear();return}let t=this.d,s=this.size;for(;t&&s>e;)this.c.delete(t.key),t=t.next,s--;this.d=t,this.f=s,t&&(t.previous=void 0),this.g++}j(e){if(e>=this.size)return;if(e===0){this.clear();return}let t=this.e,s=this.size;for(;t&&s>e;)this.c.delete(t.key),t=t.previous,s--;this.e=t,this.f=s,t&&(t.next=void 0),this.g++}k(e){if(!this.d&&!this.e)this.e=e;else if(this.d)e.next=this.d,this.d.previous=e;else throw new Error("Invalid list");this.d=e,this.g++}l(e){if(!this.d&&!this.e)this.d=e;else if(this.e)e.previous=this.e,this.e.next=e;else throw new Error("Invalid list");this.e=e,this.g++}m(e){if(e===this.d&&e===this.e)this.d=void 0,this.e=void 0;else if(e===this.d){if(!e.next)throw new Error("Invalid list");e.next.previous=void 0,this.d=e.next}else if(e===this.e){if(!e.previous)throw new Error("Invalid list");e.previous.next=void 0,this.e=e.previous}else{const t=e.next,s=e.previous;if(!t||!s)throw new Error("Invalid list");t.previous=s,s.next=t}e.next=void 0,e.previous=void 0,this.g++}n(e,t){if(!this.d||!this.e)throw new Error("Invalid list");if(!(t!==1&&t!==2)){if(t===1){if(e===this.d)return;const s=e.next,r=e.previous;e===this.e?(r.next=void 0,this.e=r):(s.previous=r,r.next=s),e.previous=void 0,e.next=this.d,this.d.previous=e,this.d=e,this.g++}else if(t===2){if(e===this.e)return;const s=e.next,r=e.previous;e===this.d?(s.previous=void 0,this.d=s):(s.previous=r,r.next=s),e.next=void 0,e.previous=this.e,this.e.next=e,this.e=e,this.g++}}}toJSON(){const e=[];return this.forEach((t,s)=>{e.push([s,t])}),e}fromJSON(e){this.clear();for(const[t,s]of e)this.set(t,s)}},Ns=class extends ks{constructor(e,t=1){super(),this.o=e,this.p=Math.min(Math.max(0,t),1)}get limit(){return this.o}set limit(e){this.o=e,this.q()}get ratio(){return this.p}set ratio(e){this.p=Math.min(Math.max(0,e),1),this.q()}get(e,t=2){return super.get(e,t)}peek(e){return super.get(e,0)}set(e,t){return super.set(e,t,2),this}q(){this.size>this.o&&this.r(Math.round(this.o*this.p))}},Pt=class extends Ns{constructor(e,t=1){super(e,t)}r(e){this.h(e)}set(e,t){return super.set(e,t),this.q(),this}},Rs=class{constructor(){this.c=new Map}add(e,t){let s=this.c.get(e);s||(s=new Set,this.c.set(e,s)),s.add(t)}delete(e,t){const s=this.c.get(e);s&&(s.delete(t),s.size===0&&this.c.delete(e))}forEach(e,t){const s=this.c.get(e);s&&s.forEach(t)}get(e){const t=this.c.get(e);return t||new Set}}}}),Xn=O({"out-build/vs/base/common/assert.js"(){"use strict";Xe()}});function eo(e){return typeof e=="number"&&!isNaN(e)}function to(e){return!!e&&typeof e[Symbol.iterator]=="function"}var d1=O({"out-build/vs/base/common/types.js"(){"use strict";Xn()}}),p1,so=O({"out-build/vs/base/common/iterator.js"(){"use strict";d1(),function(e){function t(_){return _&&typeof _=="object"&&typeof _[Symbol.iterator]=="function"}e.is=t;const s=Object.freeze([]);function r(){return s}e.empty=r;function*i(_){yield _}e.single=i;function n(_){return t(_)?_:i(_)}e.wrap=n;function a(_){return _||s}e.from=a;function*o(_){for(let $=_.length-1;$>=0;$--)yield _[$]}e.reverse=o;function l(_){return!_||_[Symbol.iterator]().next().done===!0}e.isEmpty=l;function c(_){return _[Symbol.iterator]().next().value}e.first=c;function f(_,$){let x=0;for(const P of _)if($(P,x++))return!0;return!1}e.some=f;function u(_,$){for(const x of _)if($(x))return x}e.find=u;function*h(_,$){for(const x of _)$(x)&&(yield x)}e.filter=h;function*d(_,$){let x=0;for(const P of _)yield $(P,x++)}e.map=d;function*p(_,$){let x=0;for(const P of _)yield*$(P,x++)}e.flatMap=p;function*y(..._){for(const $ of _)to($)?yield*$:yield $}e.concat=y;function E(_,$,x){let P=x;for(const S of _)P=$(P,S);return P}e.reduce=E;function C(_){let $=0;for(const x of _)$++;return $}e.length=C;function*N(_,$,x=_.length){for($<-_.length&&($=0),$<0&&($+=_.length),x<0?x+=_.length:x>_.length&&(x=_.length);$<x;$++)yield _[$]}e.slice=N;function k(_,$=Number.POSITIVE_INFINITY){const x=[];if($===0)return[x,_];const P=_[Symbol.iterator]();for(let S=0;S<$;S++){const U=P.next();if(U.done)return[x,e.empty()];x.push(U.value)}return[x,{[Symbol.iterator](){return P}}]}e.consume=k;async function X(_){const $=[];for await(const x of _)$.push(x);return Promise.resolve($)}e.asyncToArray=X}(p1||(p1={}))}});function ro(e){ke=e}function et(e){return ke?.trackDisposable(e),e}function tt(e){ke?.markAsDisposed(e)}function st(e,t){ke?.setParent(e,t)}function io(e,t){if(ke)for(const s of e)ke.setParent(s,t)}function g1(e){if(p1.is(e)){const t=[];for(const s of e)if(s)try{s.dispose()}catch(r){t.push(r)}if(t.length===1)throw t[0];if(t.length>1)throw new AggregateError(t,"Encountered errors while disposing of store");return Array.isArray(e)?[]:e}else if(e)return e.dispose(),e}function no(...e){const t=Le(()=>g1(e));return io(e,t),t}function Le(e){const t=et({dispose:zn(()=>{tt(t),e()})});return t}var Ts,ke,oo,Ne,ve,m1,Is,Lt=O({"out-build/vs/base/common/lifecycle.js"(){"use strict";if(u1(),Es(),h1(),Cs(),so(),Ts=!1,ke=null,oo=class Cn{constructor(){this.b=new Map}static{this.a=0}c(t){let s=this.b.get(t);return s||(s={parent:null,source:null,isSingleton:!1,value:t,idx:Cn.a++},this.b.set(t,s)),s}trackDisposable(t){const s=this.c(t);s.source||(s.source=new Error().stack)}setParent(t,s){const r=this.c(t);r.parent=s}markAsDisposed(t){this.b.delete(t)}markAsSingleton(t){this.c(t).isSingleton=!0}f(t,s){const r=s.get(t);if(r)return r;const i=t.parent?this.f(this.c(t.parent),s):t;return s.set(t,i),i}getTrackedDisposables(){const t=new Map;return[...this.b.entries()].filter(([,r])=>r.source!==null&&!this.f(r,t).isSingleton).flatMap(([r])=>r)}computeLeakingDisposables(t=10,s){let r;if(s)r=s;else{const l=new Map,c=[...this.b.values()].filter(u=>u.source!==null&&!this.f(u,l).isSingleton);if(c.length===0)return;const f=new Set(c.map(u=>u.value));if(r=c.filter(u=>!(u.parent&&f.has(u.parent))),r.length===0)throw new Error("There are cyclic diposable chains!")}if(!r)return;function i(l){function c(u,h){for(;u.length>0&&h.some(d=>typeof d=="string"?d===u[0]:u[0].match(d));)u.shift()}const f=l.source.split(`
`).map(u=>u.trim().replace("at ","")).filter(u=>u!=="");return c(f,["Error",/^trackDisposable \(.*\)$/,/^DisposableTracker.trackDisposable \(.*\)$/]),f.reverse()}const n=new Rs;for(const l of r){const c=i(l);for(let f=0;f<=c.length;f++)n.add(c.slice(0,f).join(`
`),l)}r.sort(Jn(l=>l.idx,Os));let a="",o=0;for(const l of r.slice(0,t)){o++;const c=i(l),f=[];for(let u=0;u<c.length;u++){let h=c[u];h=`(shared with ${n.get(c.slice(0,u+1).join(`
`)).size}/${r.length} leaks) at ${h}`;const p=n.get(c.slice(0,u).join(`
`)),y=qn([...p].map(E=>i(E)[u]),E=>E);delete y[c[u]];for(const[E,C]of Object.entries(y))f.unshift(`    - stacktraces of ${C.length} other leaks continue with ${E}`);f.unshift(h)}a+=`


==================== Leaking disposable ${o}/${r.length}: ${l.value.constructor.name} ====================
${f.join(`
`)}
============================================================

`}return r.length>t&&(a+=`


... and ${r.length-t} more leaking disposables

`),{leaks:r,details:a}}},Ts){const e="__is_disposable_tracked__";ro(new class{trackDisposable(t){const s=new Error("Potentially leaked disposable").stack;setTimeout(()=>{t[e]||console.log(s)},3e3)}setParent(t,s){if(t&&t!==ve.None)try{t[e]=!0}catch{}}markAsDisposed(t){if(t&&t!==ve.None)try{t[e]=!0}catch{}}markAsSingleton(t){}})}Ne=class An{static{this.DISABLE_DISPOSED_WARNING=!1}constructor(){this.f=new Set,this.g=!1,et(this)}dispose(){this.g||(tt(this),this.g=!0,this.clear())}get isDisposed(){return this.g}clear(){if(this.f.size!==0)try{g1(this.f)}finally{this.f.clear()}}add(t){if(!t)return t;if(t===this)throw new Error("Cannot register a disposable on itself!");return st(t,this),this.g?An.DISABLE_DISPOSED_WARNING||console.warn(new Error("Trying to add a disposable to a DisposableStore that has already been disposed of. The added object will be leaked!").stack):this.f.add(t),t}delete(t){if(t){if(t===this)throw new Error("Cannot dispose a disposable on itself!");this.f.delete(t),t.dispose()}}deleteAndLeak(t){t&&this.f.has(t)&&(this.f.delete(t),st(t,null))}},ve=class{static{this.None=Object.freeze({dispose(){}})}constructor(){this.q=new Ne,et(this),st(this.q,this)}dispose(){tt(this),this.q.dispose()}B(e){if(e===this)throw new Error("Cannot register a disposable on itself!");return this.q.add(e)}},m1=class{constructor(){this.b=!1,et(this)}get value(){return this.b?void 0:this.a}set value(e){this.b||e===this.a||(this.a?.dispose(),e&&st(e,this),this.a=e)}clear(){this.value=void 0}dispose(){this.b=!0,tt(this),this.a?.dispose(),this.a=void 0}clearAndLeak(){const e=this.a;return this.a=void 0,e&&st(e,null),e}},Is=class{constructor(){this.a=new Map,this.b=!1,et(this)}dispose(){tt(this),this.b=!0,this.clearAndDisposeAll()}clearAndDisposeAll(){if(this.a.size)try{g1(this.a.values())}finally{this.a.clear()}}has(e){return this.a.has(e)}get size(){return this.a.size}get(e){return this.a.get(e)}set(e,t,s=!1){this.b&&console.warn(new Error("Trying to add a disposable to a DisposableMap that has already been disposed of. The added object will be leaked!").stack),s||this.a.get(e)?.dispose(),this.a.set(e,t)}deleteAndDispose(e){this.a.get(e)?.dispose(),this.a.delete(e)}deleteAndLeak(e){const t=this.a.get(e);return this.a.delete(e),t}keys(){return this.a.keys()}values(){return this.a.values()}[Symbol.iterator](){return this.a[Symbol.iterator]()}}}}),ao,lo=O({"out-build/vs/base/common/linkedList.js"(){"use strict";ao=class e1{static{this.Undefined=new e1(void 0)}constructor(t){this.element=t,this.next=e1.Undefined,this.prev=e1.Undefined}}}}),js,Fs,co=O({"out-build/vs/base/common/stopwatch.js"(){"use strict";js=globalThis.performance&&typeof globalThis.performance.now=="function",Fs=class On{static create(t){return new On(t)}constructor(t){this.c=js&&t===!1?Date.now:globalThis.performance.now.bind(globalThis.performance),this.a=this.c(),this.b=-1}stop(){this.b=this.c()}reset(){this.a=this.c(),this.b=-1}elapsed(){return this.b!==-1?this.b-this.a:this.c()-this.a}}}}),w1,Ms,ne,Us,b1,Vs,kt,qs,Ws,Bs,rt,zs,Hs,fe,Gs,y1=O({"out-build/vs/base/common/event.js"(){"use strict";Es(),Xe(),Cs(),Lt(),lo(),co(),w1=!1,Ms=!1,function(e){e.None=()=>ve.None;function t(m){if(Ms){const{onDidAddListener:g}=m,b=kt.create();let w=0;m.onDidAddListener=()=>{++w===2&&(console.warn("snapshotted emitter LIKELY used public and SHOULD HAVE BEEN created with DisposableStore. snapshotted here"),b.print()),g?.()}}}function s(m,g){return d(m,()=>{},0,void 0,!0,void 0,g)}e.defer=s;function r(m){return(g,b=null,w)=>{let A=!1,L;return L=m(F=>{if(!A)return L?L.dispose():A=!0,g.call(b,F)},null,w),A&&L.dispose(),L}}e.once=r;function i(m,g){return e.once(e.filter(m,g))}e.onceIf=i;function n(m,g,b){return u((w,A=null,L)=>m(F=>w.call(A,g(F)),null,L),b)}e.map=n;function a(m,g,b){return u((w,A=null,L)=>m(F=>{g(F),w.call(A,F)},null,L),b)}e.forEach=a;function o(m,g,b){return u((w,A=null,L)=>m(F=>g(F)&&w.call(A,F),null,L),b)}e.filter=o;function l(m){return m}e.signal=l;function c(...m){return(g,b=null,w)=>{const A=no(...m.map(L=>L(F=>g.call(b,F))));return h(A,w)}}e.any=c;function f(m,g,b,w){let A=b;return n(m,L=>(A=g(A,L),A),w)}e.reduce=f;function u(m,g){let b;const w={onWillAddFirstListener(){b=m(A.fire,A)},onDidRemoveLastListener(){b?.dispose()}};g||t(w);const A=new fe(w);return g?.add(A),A.event}function h(m,g){return g instanceof Array?g.push(m):g&&g.add(m),m}function d(m,g,b=100,w=!1,A=!1,L,F){let z,G,We,Ot=0,Ye;const ys={leakWarningThreshold:L,onWillAddFirstListener(){z=m(Pn=>{Ot++,G=g(G,Pn),w&&!We&&(St.fire(G),G=void 0),Ye=()=>{const Ln=G;G=void 0,We=void 0,(!w||Ot>1)&&St.fire(Ln),Ot=0},typeof b=="number"?(clearTimeout(We),We=setTimeout(Ye,b)):We===void 0&&(We=0,queueMicrotask(Ye))})},onWillRemoveListener(){A&&Ot>0&&Ye?.()},onDidRemoveLastListener(){Ye=void 0,z.dispose()}};F||t(ys);const St=new fe(ys);return F?.add(St),St.event}e.debounce=d;function p(m,g=0,b){return e.debounce(m,(w,A)=>w?(w.push(A),w):[A],g,void 0,!0,void 0,b)}e.accumulate=p;function y(m,g=(w,A)=>w===A,b){let w=!0,A;return o(m,L=>{const F=w||!g(L,A);return w=!1,A=L,F},b)}e.latch=y;function E(m,g,b){return[e.filter(m,g,b),e.filter(m,w=>!g(w),b)]}e.split=E;function C(m,g=!1,b=[],w){let A=b.slice(),L=m(G=>{A?A.push(G):z.fire(G)});w&&w.add(L);const F=()=>{A?.forEach(G=>z.fire(G)),A=null},z=new fe({onWillAddFirstListener(){L||(L=m(G=>z.fire(G)),w&&w.add(L))},onDidAddFirstListener(){A&&(g?setTimeout(F):F())},onDidRemoveLastListener(){L&&L.dispose(),L=null}});return w&&w.add(z),z.event}e.buffer=C;function N(m,g){return(w,A,L)=>{const F=g(new X);return m(function(z){const G=F.evaluate(z);G!==k&&w.call(A,G)},void 0,L)}}e.chain=N;const k=Symbol("HaltChainable");class X{constructor(){this.f=[]}map(g){return this.f.push(g),this}forEach(g){return this.f.push(b=>(g(b),b)),this}filter(g){return this.f.push(b=>g(b)?b:k),this}reduce(g,b){let w=b;return this.f.push(A=>(w=g(w,A),w)),this}latch(g=(b,w)=>b===w){let b=!0,w;return this.f.push(A=>{const L=b||!g(A,w);return b=!1,w=A,L?A:k}),this}evaluate(g){for(const b of this.f)if(g=b(g),g===k)break;return g}}function _(m,g,b=w=>w){const w=(...z)=>F.fire(b(...z)),A=()=>m.on(g,w),L=()=>m.removeListener(g,w),F=new fe({onWillAddFirstListener:A,onDidRemoveLastListener:L});return F.event}e.fromNodeEventEmitter=_;function $(m,g,b=w=>w){const w=(...z)=>F.fire(b(...z)),A=()=>m.addEventListener(g,w),L=()=>m.removeEventListener(g,w),F=new fe({onWillAddFirstListener:A,onDidRemoveLastListener:L});return F.event}e.fromDOMEventEmitter=$;function x(m,g){return new Promise(b=>r(m)(b,null,g))}e.toPromise=x;function P(m){const g=new fe;return m.then(b=>{g.fire(b)},()=>{g.fire(void 0)}).finally(()=>{g.dispose()}),g.event}e.fromPromise=P;function S(m,g){return m(b=>g.fire(b))}e.forward=S;function U(m,g,b){return g(b),m(w=>g(w))}e.runAndSubscribe=U;class ue{constructor(g,b){this._observable=g,this.f=0,this.g=!1;const w={onWillAddFirstListener:()=>{g.addObserver(this),this._observable.reportChanges()},onDidRemoveLastListener:()=>{g.removeObserver(this)}};b||t(w),this.emitter=new fe(w),b&&b.add(this.emitter)}beginUpdate(g){this.f++}handlePossibleChange(g){}handleChange(g,b){this.g=!0}endUpdate(g){this.f--,this.f===0&&(this._observable.reportChanges(),this.g&&(this.g=!1,this.emitter.fire(this._observable.get())))}}function j(m,g){return new ue(m,g).emitter.event}e.fromObservable=j;function Pe(m){return(g,b,w)=>{let A=0,L=!1;const F={beginUpdate(){A++},endUpdate(){A--,A===0&&(m.reportChanges(),L&&(L=!1,g.call(b)))},handlePossibleChange(){},handleChange(){L=!0}};m.addObserver(F),m.reportChanges();const z={dispose(){m.removeObserver(F)}};return w instanceof Ne?w.add(z):Array.isArray(w)&&w.push(z),z}}e.fromObservableLight=Pe}(ne||(ne={})),Us=class ws{static{this.all=new Set}static{this.f=0}constructor(t){this.listenerCount=0,this.invocationCount=0,this.elapsedOverall=0,this.durations=[],this.name=`${t}_${ws.f++}`,ws.all.add(this)}start(t){this.g=new Fs,this.listenerCount=t}stop(){if(this.g){const t=this.g.elapsed();this.durations.push(t),this.elapsedOverall+=t,this.invocationCount+=1,this.g=void 0}}},b1=-1,Vs=class Sn{static{this.f=1}constructor(t,s,r=(Sn.f++).toString(16).padStart(3,"0")){this.j=t,this.threshold=s,this.name=r,this.h=0}dispose(){this.g?.clear()}check(t,s){const r=this.threshold;if(r<=0||s<r)return;this.g||(this.g=new Map);const i=this.g.get(t.value)||0;if(this.g.set(t.value,i+1),this.h-=1,this.h<=0){this.h=r*.5;const[n,a]=this.getMostFrequentStack(),o=`[${this.name}] potential listener LEAK detected, having ${s} listeners already. MOST frequent listener (${a}):`;console.warn(o),console.warn(n);const l=new qs(o,n);this.j(l)}return()=>{const n=this.g.get(t.value)||0;this.g.set(t.value,n-1)}}getMostFrequentStack(){if(!this.g)return;let t,s=0;for(const[r,i]of this.g)(!t||s<i)&&(t=[r,i],s=i);return t}},kt=class xn{static create(){const t=new Error;return new xn(t.stack??"")}constructor(t){this.value=t}print(){console.warn(this.value.split(`
`).slice(2).join(`
`))}},qs=class extends Error{constructor(e,t){super(e),this.name="ListenerLeakError",this.stack=t}},Ws=class extends Error{constructor(e,t){super(e),this.name="ListenerRefusalError",this.stack=t}},Bs=0,rt=class{constructor(e){this.value=e,this.id=Bs++}},zs=2,Hs=(e,t)=>{if(e instanceof rt)t(e);else for(let s=0;s<e.length;s++){const r=e[s];r&&t(r)}},fe=class{constructor(e){this.z=0,this.f=e,this.g=b1>0||this.f?.leakWarningThreshold?new Vs(e?.onListenerError??xt,this.f?.leakWarningThreshold??b1):void 0,this.j=this.f?._profName?new Us(this.f._profName):void 0,this.w=this.f?.deliveryQueue}dispose(){if(!this.m){if(this.m=!0,this.w?.current===this&&this.w.reset(),this.u){if(w1){const e=this.u;queueMicrotask(()=>{Hs(e,t=>t.stack?.print())})}this.u=void 0,this.z=0}this.f?.onDidRemoveLastListener?.(),this.g?.dispose()}}get event(){return this.q??=(e,t,s)=>{if(this.g&&this.z>this.g.threshold**2){const o=`[${this.g.name}] REFUSES to accept new listeners because it exceeded its threshold by far (${this.z} vs ${this.g.threshold})`;console.warn(o);const l=this.g.getMostFrequentStack()??["UNKNOWN stack",-1],c=new Ws(`${o}. HINT: Stack shows most frequent listener (${l[1]}-times)`,l[0]);return(this.f?.onListenerError||xt)(c),ve.None}if(this.m)return ve.None;t&&(e=e.bind(t));const r=new rt(e);let i,n;this.g&&this.z>=Math.ceil(this.g.threshold*.2)&&(r.stack=kt.create(),i=this.g.check(r.stack,this.z+1)),w1&&(r.stack=n??kt.create()),this.u?this.u instanceof rt?(this.w??=new Gs,this.u=[this.u,r]):this.u.push(r):(this.f?.onWillAddFirstListener?.(this),this.u=r,this.f?.onDidAddFirstListener?.(this)),this.f?.onDidAddListener?.(this),this.z++;const a=Le(()=>{i?.(),this.A(r)});return s instanceof Ne?s.add(a):Array.isArray(s)&&s.push(a),a},this.q}A(e){if(this.f?.onWillRemoveListener?.(this),!this.u)return;if(this.z===1){this.u=void 0,this.f?.onDidRemoveLastListener?.(this),this.z=0;return}const t=this.u,s=t.indexOf(e);if(s===-1)throw console.log("disposed?",this.m),console.log("size?",this.z),console.log("arr?",JSON.stringify(this.u)),new Error("Attempted to dispose unknown listener");this.z--,t[s]=void 0;const r=this.w.current===this;if(this.z*zs<=t.length){let i=0;for(let n=0;n<t.length;n++)t[n]?t[i++]=t[n]:r&&i<this.w.end&&(this.w.end--,i<this.w.i&&this.w.i--);t.length=i}}B(e,t){if(!e)return;const s=this.f?.onListenerError||xt;if(!s){e.value(t);return}try{e.value(t)}catch(r){s(r)}}C(e){const t=e.current.u;for(;e.i<e.end;)this.B(t[e.i++],e.value);e.reset()}fire(e){if(this.w?.current&&(this.C(this.w),this.j?.stop()),this.j?.start(this.z),this.u)if(this.u instanceof rt)this.B(this.u,e);else{const t=this.w;t.enqueue(this,e,this.u.length),this.C(t)}this.j?.stop()}hasListeners(){return this.z>0}},Gs=class{constructor(){this.i=-1,this.end=0}enqueue(e,t,s){this.i=0,this.end=s,this.current=e,this.value=t}reset(){this.i=this.end,this.current=void 0,this.value=void 0}}}});function uo(){return globalThis._VSCODE_NLS_MESSAGES}function Ks(){return globalThis._VSCODE_NLS_LANGUAGE}var Js=O({"out-build/vs/nls.messages.js"(){"use strict"}});function Qs(e,t){let s;return t.length===0?s=e:s=e.replace(/\{(\d+)\}/g,(r,i)=>{const n=i[0],a=t[n];let o=r;return typeof a=="string"?o=a:(typeof a=="number"||typeof a=="boolean"||a===void 0||a===null)&&(o=String(a)),o}),Ys&&(s="\uFF3B"+s.replace(/[aouei]/g,"$&$&")+"\uFF3D"),s}function v(e,t,...s){return Qs(typeof e=="number"?fo(e,t):t,s)}function fo(e,t){const s=uo()?.[e];if(typeof s!="string"){if(typeof t=="string")return t;throw new Error(`!!! NLS MISSING: ${e} !!!`)}return s}var Ys,Nt=O({"out-build/vs/nls.js"(){"use strict";Js(),Js(),Ys=Ks()==="pseudo"||typeof document<"u"&&document.location&&typeof document.location.hash=="string"&&document.location.hash.indexOf("pseudo=true")>=0}}),Re,it,nt,Be,Zs,v1,Rt,Xs,er,tr,sr,ot,at,E1,rr,pe,ge,ee,_1,ir,nr,Tt,W,Te,Ee,or,ar,lr,cr,he,_e,ur,fr,hr,dr,pr,ho,po,go,mo,J=O({"out-build/vs/base/common/platform.js"(){"use strict";if(Nt(),Re="en",it=!1,nt=!1,Be=!1,Zs=!1,v1=!1,Rt=!1,Xs=!1,er=!1,tr=!1,sr=!1,ot=void 0,at=Re,E1=Re,rr=void 0,pe=void 0,ge=globalThis,ee=void 0,typeof ge.vscode<"u"&&typeof ge.vscode.process<"u"?ee=ge.vscode.process:typeof process<"u"&&typeof process?.versions?.node=="string"&&(ee=process),_1=typeof ee?.versions?.electron=="string",ir=_1&&ee?.type==="renderer",typeof ee=="object"){it=ee.platform==="win32",nt=ee.platform==="darwin",Be=ee.platform==="linux",Zs=Be&&!!ee.env.SNAP&&!!ee.env.SNAP_REVISION,Xs=_1,tr=!!ee.env.CI||!!ee.env.BUILD_ARTIFACTSTAGINGDIRECTORY,ot=Re,at=Re;const e=ee.env.VSCODE_NLS_CONFIG;if(e)try{const t=JSON.parse(e);ot=t.userLocale,E1=t.osLocale,at=t.resolvedLanguage||Re,rr=t.languagePack?.translationsConfigFile}catch{}v1=!0}else typeof navigator=="object"&&!ir?(pe=navigator.userAgent,it=pe.indexOf("Windows")>=0,nt=pe.indexOf("Macintosh")>=0,er=(pe.indexOf("Macintosh")>=0||pe.indexOf("iPad")>=0||pe.indexOf("iPhone")>=0)&&!!navigator.maxTouchPoints&&navigator.maxTouchPoints>0,Be=pe.indexOf("Linux")>=0,sr=pe?.indexOf("Mobi")>=0,Rt=!0,at=Ks()||Re,ot=navigator.language.toLowerCase(),E1=ot):console.error("Unable to resolve platform.");(function(e){e[e.Web=0]="Web",e[e.Mac=1]="Mac",e[e.Linux=2]="Linux",e[e.Windows=3]="Windows"})(nr||(nr={})),Tt=0,nt?Tt=1:it?Tt=3:Be&&(Tt=2),W=it,Te=nt,Ee=Be,or=v1,ar=Rt,lr=Rt&&typeof ge.importScripts=="function",cr=lr?ge.origin:void 0,he=pe,_e=at,function(e){function t(){return _e}e.value=t;function s(){return _e.length===2?_e==="en":_e.length>=3?_e[0]==="e"&&_e[1]==="n"&&_e[2]==="-":!1}e.isDefaultVariant=s;function r(){return _e==="en"}e.isDefault=r}(ur||(ur={})),fr=typeof ge.postMessage=="function"&&!ge.importScripts,hr=(()=>{if(fr){const e=[];ge.addEventListener("message",s=>{if(s.data&&s.data.vscodeScheduleAsyncWork)for(let r=0,i=e.length;r<i;r++){const n=e[r];if(n.id===s.data.vscodeScheduleAsyncWork){e.splice(r,1),n.callback();return}}});let t=0;return s=>{const r=++t;e.push({id:r,callback:s}),ge.postMessage({vscodeScheduleAsyncWork:r},"*")}}return e=>setTimeout(e)})(),function(e){e[e.Windows=1]="Windows",e[e.Macintosh=2]="Macintosh",e[e.Linux=3]="Linux"}(dr||(dr={})),pr=!!(he&&he.indexOf("Chrome")>=0),ho=!!(he&&he.indexOf("Firefox")>=0),po=!!(!pr&&he&&he.indexOf("Safari")>=0),go=!!(he&&he.indexOf("Edg/")>=0),mo=!!(he&&he.indexOf("Android")>=0)}}),Ie,It,ze,$1,gr,wo,C1=O({"out-build/vs/base/common/process.js"(){"use strict";if(J(),It=globalThis.vscode,typeof It<"u"&&typeof It.process<"u"){const e=It.process;Ie={get platform(){return e.platform},get arch(){return e.arch},get env(){return e.env},cwd(){return e.cwd()}}}else typeof process<"u"&&typeof process?.versions?.node=="string"?Ie={get platform(){return process.platform},get arch(){return process.arch},get env(){return process.env},cwd(){return process.env.VSCODE_CWD||process.cwd()}}:Ie={get platform(){return W?"win32":Te?"darwin":"linux"},get arch(){},get env(){return{}},cwd(){return"/"}};ze=Ie.cwd,$1=Ie.env,gr=Ie.platform,wo=Ie.arch}});function bo(e,t){if(e===null||typeof e!="object")throw new O1(t,"Object",e)}function V(e,t){if(typeof e!="string")throw new O1(t,"string",e)}function D(e){return e===B||e===Q}function A1(e){return e===B}function $e(e){return e>=wr&&e<=yr||e>=br&&e<=vr}function jt(e,t,s,r){let i="",n=0,a=-1,o=0,l=0;for(let c=0;c<=e.length;++c){if(c<e.length)l=e.charCodeAt(c);else{if(r(l))break;l=B}if(r(l)){if(!(a===c-1||o===1))if(o===2){if(i.length<2||n!==2||i.charCodeAt(i.length-1)!==Oe||i.charCodeAt(i.length-2)!==Oe){if(i.length>2){const f=i.lastIndexOf(s);f===-1?(i="",n=0):(i=i.slice(0,f),n=i.length-1-i.lastIndexOf(s)),a=c,o=0;continue}else if(i.length!==0){i="",n=0,a=c,o=0;continue}}t&&(i+=i.length>0?`${s}..`:"..",n=2)}else i.length>0?i+=`${s}${e.slice(a+1,c)}`:i=e.slice(a+1,c),n=c-a-1;a=c,o=0}else l===Oe&&o!==-1?++o:o=-1}return i}function yo(e){return e?`${e[0]==="."?"":"."}${e}`:""}function mr(e,t){bo(t,"pathObject");const s=t.dir||t.root,r=t.base||`${t.name||""}${yo(t.ext)}`;return s?s===t.root?`${s}${r}`:`${s}${e}${r}`:r}var wr,br,yr,vr,Oe,B,Q,me,Er,O1,Y,q,_r,I,Ft,He,te,S1,$r,je,lt,Cr,vo,Eo,_o,oe,$o,ae=O({"out-build/vs/base/common/path.js"(){"use strict";C1(),wr=65,br=97,yr=90,vr=122,Oe=46,B=47,Q=92,me=58,Er=63,O1=class extends Error{constructor(e,t,s){let r;typeof t=="string"&&t.indexOf("not ")===0?(r="must not be",t=t.replace(/^not /,"")):r="must be";const i=e.indexOf(".")!==-1?"property":"argument";let n=`The "${e}" ${i} ${r} of type ${t}`;n+=`. Received type ${typeof s}`,super(n),this.code="ERR_INVALID_ARG_TYPE"}},Y=gr==="win32",q={resolve(...e){let t="",s="",r=!1;for(let i=e.length-1;i>=-1;i--){let n;if(i>=0){if(n=e[i],V(n,`paths[${i}]`),n.length===0)continue}else t.length===0?n=ze():(n=$1[`=${t}`]||ze(),(n===void 0||n.slice(0,2).toLowerCase()!==t.toLowerCase()&&n.charCodeAt(2)===Q)&&(n=`${t}\\`));const a=n.length;let o=0,l="",c=!1;const f=n.charCodeAt(0);if(a===1)D(f)&&(o=1,c=!0);else if(D(f))if(c=!0,D(n.charCodeAt(1))){let u=2,h=u;for(;u<a&&!D(n.charCodeAt(u));)u++;if(u<a&&u!==h){const d=n.slice(h,u);for(h=u;u<a&&D(n.charCodeAt(u));)u++;if(u<a&&u!==h){for(h=u;u<a&&!D(n.charCodeAt(u));)u++;(u===a||u!==h)&&(l=`\\\\${d}\\${n.slice(h,u)}`,o=u)}}}else o=1;else $e(f)&&n.charCodeAt(1)===me&&(l=n.slice(0,2),o=2,a>2&&D(n.charCodeAt(2))&&(c=!0,o=3));if(l.length>0)if(t.length>0){if(l.toLowerCase()!==t.toLowerCase())continue}else t=l;if(r){if(t.length>0)break}else if(s=`${n.slice(o)}\\${s}`,r=c,c&&t.length>0)break}return s=jt(s,!r,"\\",D),r?`${t}\\${s}`:`${t}${s}`||"."},normalize(e){V(e,"path");const t=e.length;if(t===0)return".";let s=0,r,i=!1;const n=e.charCodeAt(0);if(t===1)return A1(n)?"\\":e;if(D(n))if(i=!0,D(e.charCodeAt(1))){let o=2,l=o;for(;o<t&&!D(e.charCodeAt(o));)o++;if(o<t&&o!==l){const c=e.slice(l,o);for(l=o;o<t&&D(e.charCodeAt(o));)o++;if(o<t&&o!==l){for(l=o;o<t&&!D(e.charCodeAt(o));)o++;if(o===t)return`\\\\${c}\\${e.slice(l)}\\`;o!==l&&(r=`\\\\${c}\\${e.slice(l,o)}`,s=o)}}}else s=1;else $e(n)&&e.charCodeAt(1)===me&&(r=e.slice(0,2),s=2,t>2&&D(e.charCodeAt(2))&&(i=!0,s=3));let a=s<t?jt(e.slice(s),!i,"\\",D):"";if(a.length===0&&!i&&(a="."),a.length>0&&D(e.charCodeAt(t-1))&&(a+="\\"),!i&&r===void 0&&e.includes(":")){if(a.length>=2&&$e(a.charCodeAt(0))&&a.charCodeAt(1)===me)return`.\\${a}`;let o=e.indexOf(":");do if(o===t-1||D(e.charCodeAt(o+1)))return`.\\${a}`;while((o=e.indexOf(":",o+1))!==-1)}return r===void 0?i?`\\${a}`:a:i?`${r}\\${a}`:`${r}${a}`},isAbsolute(e){V(e,"path");const t=e.length;if(t===0)return!1;const s=e.charCodeAt(0);return D(s)||t>2&&$e(s)&&e.charCodeAt(1)===me&&D(e.charCodeAt(2))},join(...e){if(e.length===0)return".";let t,s;for(let n=0;n<e.length;++n){const a=e[n];V(a,"path"),a.length>0&&(t===void 0?t=s=a:t+=`\\${a}`)}if(t===void 0)return".";let r=!0,i=0;if(typeof s=="string"&&D(s.charCodeAt(0))){++i;const n=s.length;n>1&&D(s.charCodeAt(1))&&(++i,n>2&&(D(s.charCodeAt(2))?++i:r=!1))}if(r){for(;i<t.length&&D(t.charCodeAt(i));)i++;i>=2&&(t=`\\${t.slice(i)}`)}return q.normalize(t)},relative(e,t){if(V(e,"from"),V(t,"to"),e===t)return"";const s=q.resolve(e),r=q.resolve(t);if(s===r||(e=s.toLowerCase(),t=r.toLowerCase(),e===t))return"";if(s.length!==e.length||r.length!==t.length){const p=s.split("\\"),y=r.split("\\");p[p.length-1]===""&&p.pop(),y[y.length-1]===""&&y.pop();const E=p.length,C=y.length,N=E<C?E:C;let k;for(k=0;k<N&&p[k].toLowerCase()===y[k].toLowerCase();k++);return k===0?r:k===N?C>N?y.slice(k).join("\\"):E>N?"..\\".repeat(E-1-k)+"..":"":"..\\".repeat(E-k)+y.slice(k).join("\\")}let i=0;for(;i<e.length&&e.charCodeAt(i)===Q;)i++;let n=e.length;for(;n-1>i&&e.charCodeAt(n-1)===Q;)n--;const a=n-i;let o=0;for(;o<t.length&&t.charCodeAt(o)===Q;)o++;let l=t.length;for(;l-1>o&&t.charCodeAt(l-1)===Q;)l--;const c=l-o,f=a<c?a:c;let u=-1,h=0;for(;h<f;h++){const p=e.charCodeAt(i+h);if(p!==t.charCodeAt(o+h))break;p===Q&&(u=h)}if(h!==f){if(u===-1)return r}else{if(c>f){if(t.charCodeAt(o+h)===Q)return r.slice(o+h+1);if(h===2)return r.slice(o+h)}a>f&&(e.charCodeAt(i+h)===Q?u=h:h===2&&(u=3)),u===-1&&(u=0)}let d="";for(h=i+u+1;h<=n;++h)(h===n||e.charCodeAt(h)===Q)&&(d+=d.length===0?"..":"\\..");return o+=u,d.length>0?`${d}${r.slice(o,l)}`:(r.charCodeAt(o)===Q&&++o,r.slice(o,l))},toNamespacedPath(e){if(typeof e!="string"||e.length===0)return e;const t=q.resolve(e);if(t.length<=2)return e;if(t.charCodeAt(0)===Q){if(t.charCodeAt(1)===Q){const s=t.charCodeAt(2);if(s!==Er&&s!==Oe)return`\\\\?\\UNC\\${t.slice(2)}`}}else if($e(t.charCodeAt(0))&&t.charCodeAt(1)===me&&t.charCodeAt(2)===Q)return`\\\\?\\${t}`;return t},dirname(e){V(e,"path");const t=e.length;if(t===0)return".";let s=-1,r=0;const i=e.charCodeAt(0);if(t===1)return D(i)?e:".";if(D(i)){if(s=r=1,D(e.charCodeAt(1))){let o=2,l=o;for(;o<t&&!D(e.charCodeAt(o));)o++;if(o<t&&o!==l){for(l=o;o<t&&D(e.charCodeAt(o));)o++;if(o<t&&o!==l){for(l=o;o<t&&!D(e.charCodeAt(o));)o++;if(o===t)return e;o!==l&&(s=r=o+1)}}}}else $e(i)&&e.charCodeAt(1)===me&&(s=t>2&&D(e.charCodeAt(2))?3:2,r=s);let n=-1,a=!0;for(let o=t-1;o>=r;--o)if(D(e.charCodeAt(o))){if(!a){n=o;break}}else a=!1;if(n===-1){if(s===-1)return".";n=s}return e.slice(0,n)},basename(e,t){t!==void 0&&V(t,"suffix"),V(e,"path");let s=0,r=-1,i=!0,n;if(e.length>=2&&$e(e.charCodeAt(0))&&e.charCodeAt(1)===me&&(s=2),t!==void 0&&t.length>0&&t.length<=e.length){if(t===e)return"";let a=t.length-1,o=-1;for(n=e.length-1;n>=s;--n){const l=e.charCodeAt(n);if(D(l)){if(!i){s=n+1;break}}else o===-1&&(i=!1,o=n+1),a>=0&&(l===t.charCodeAt(a)?--a===-1&&(r=n):(a=-1,r=o))}return s===r?r=o:r===-1&&(r=e.length),e.slice(s,r)}for(n=e.length-1;n>=s;--n)if(D(e.charCodeAt(n))){if(!i){s=n+1;break}}else r===-1&&(i=!1,r=n+1);return r===-1?"":e.slice(s,r)},extname(e){V(e,"path");let t=0,s=-1,r=0,i=-1,n=!0,a=0;e.length>=2&&e.charCodeAt(1)===me&&$e(e.charCodeAt(0))&&(t=r=2);for(let o=e.length-1;o>=t;--o){const l=e.charCodeAt(o);if(D(l)){if(!n){r=o+1;break}continue}i===-1&&(n=!1,i=o+1),l===Oe?s===-1?s=o:a!==1&&(a=1):s!==-1&&(a=-1)}return s===-1||i===-1||a===0||a===1&&s===i-1&&s===r+1?"":e.slice(s,i)},format:mr.bind(null,"\\"),parse(e){V(e,"path");const t={root:"",dir:"",base:"",ext:"",name:""};if(e.length===0)return t;const s=e.length;let r=0,i=e.charCodeAt(0);if(s===1)return D(i)?(t.root=t.dir=e,t):(t.base=t.name=e,t);if(D(i)){if(r=1,D(e.charCodeAt(1))){let u=2,h=u;for(;u<s&&!D(e.charCodeAt(u));)u++;if(u<s&&u!==h){for(h=u;u<s&&D(e.charCodeAt(u));)u++;if(u<s&&u!==h){for(h=u;u<s&&!D(e.charCodeAt(u));)u++;u===s?r=u:u!==h&&(r=u+1)}}}}else if($e(i)&&e.charCodeAt(1)===me){if(s<=2)return t.root=t.dir=e,t;if(r=2,D(e.charCodeAt(2))){if(s===3)return t.root=t.dir=e,t;r=3}}r>0&&(t.root=e.slice(0,r));let n=-1,a=r,o=-1,l=!0,c=e.length-1,f=0;for(;c>=r;--c){if(i=e.charCodeAt(c),D(i)){if(!l){a=c+1;break}continue}o===-1&&(l=!1,o=c+1),i===Oe?n===-1?n=c:f!==1&&(f=1):n!==-1&&(f=-1)}return o!==-1&&(n===-1||f===0||f===1&&n===o-1&&n===a+1?t.base=t.name=e.slice(a,o):(t.name=e.slice(a,n),t.base=e.slice(a,o),t.ext=e.slice(n,o))),a>0&&a!==r?t.dir=e.slice(0,a-1):t.dir=t.root,t},sep:"\\",delimiter:";",win32:null,posix:null},_r=(()=>{if(Y){const e=/\\/g;return()=>{const t=ze().replace(e,"/");return t.slice(t.indexOf("/"))}}return()=>ze()})(),I={resolve(...e){let t="",s=!1;for(let r=e.length-1;r>=0&&!s;r--){const i=e[r];V(i,`paths[${r}]`),i.length!==0&&(t=`${i}/${t}`,s=i.charCodeAt(0)===B)}if(!s){const r=_r();t=`${r}/${t}`,s=r.charCodeAt(0)===B}return t=jt(t,!s,"/",A1),s?`/${t}`:t.length>0?t:"."},normalize(e){if(V(e,"path"),e.length===0)return".";const t=e.charCodeAt(0)===B,s=e.charCodeAt(e.length-1)===B;return e=jt(e,!t,"/",A1),e.length===0?t?"/":s?"./":".":(s&&(e+="/"),t?`/${e}`:e)},isAbsolute(e){return V(e,"path"),e.length>0&&e.charCodeAt(0)===B},join(...e){if(e.length===0)return".";const t=[];for(let s=0;s<e.length;++s){const r=e[s];V(r,"path"),r.length>0&&t.push(r)}return t.length===0?".":I.normalize(t.join("/"))},relative(e,t){if(V(e,"from"),V(t,"to"),e===t||(e=I.resolve(e),t=I.resolve(t),e===t))return"";const s=1,r=e.length,i=r-s,n=1,a=t.length-n,o=i<a?i:a;let l=-1,c=0;for(;c<o;c++){const u=e.charCodeAt(s+c);if(u!==t.charCodeAt(n+c))break;u===B&&(l=c)}if(c===o)if(a>o){if(t.charCodeAt(n+c)===B)return t.slice(n+c+1);if(c===0)return t.slice(n+c)}else i>o&&(e.charCodeAt(s+c)===B?l=c:c===0&&(l=0));let f="";for(c=s+l+1;c<=r;++c)(c===r||e.charCodeAt(c)===B)&&(f+=f.length===0?"..":"/..");return`${f}${t.slice(n+l)}`},toNamespacedPath(e){return e},dirname(e){if(V(e,"path"),e.length===0)return".";const t=e.charCodeAt(0)===B;let s=-1,r=!0;for(let i=e.length-1;i>=1;--i)if(e.charCodeAt(i)===B){if(!r){s=i;break}}else r=!1;return s===-1?t?"/":".":t&&s===1?"//":e.slice(0,s)},basename(e,t){t!==void 0&&V(t,"suffix"),V(e,"path");let s=0,r=-1,i=!0,n;if(t!==void 0&&t.length>0&&t.length<=e.length){if(t===e)return"";let a=t.length-1,o=-1;for(n=e.length-1;n>=0;--n){const l=e.charCodeAt(n);if(l===B){if(!i){s=n+1;break}}else o===-1&&(i=!1,o=n+1),a>=0&&(l===t.charCodeAt(a)?--a===-1&&(r=n):(a=-1,r=o))}return s===r?r=o:r===-1&&(r=e.length),e.slice(s,r)}for(n=e.length-1;n>=0;--n)if(e.charCodeAt(n)===B){if(!i){s=n+1;break}}else r===-1&&(i=!1,r=n+1);return r===-1?"":e.slice(s,r)},extname(e){V(e,"path");let t=-1,s=0,r=-1,i=!0,n=0;for(let a=e.length-1;a>=0;--a){const o=e[a];if(o==="/"){if(!i){s=a+1;break}continue}r===-1&&(i=!1,r=a+1),o==="."?t===-1?t=a:n!==1&&(n=1):t!==-1&&(n=-1)}return t===-1||r===-1||n===0||n===1&&t===r-1&&t===s+1?"":e.slice(t,r)},format:mr.bind(null,"/"),parse(e){V(e,"path");const t={root:"",dir:"",base:"",ext:"",name:""};if(e.length===0)return t;const s=e.charCodeAt(0)===B;let r;s?(t.root="/",r=1):r=0;let i=-1,n=0,a=-1,o=!0,l=e.length-1,c=0;for(;l>=r;--l){const f=e.charCodeAt(l);if(f===B){if(!o){n=l+1;break}continue}a===-1&&(o=!1,a=l+1),f===Oe?i===-1?i=l:c!==1&&(c=1):i!==-1&&(c=-1)}if(a!==-1){const f=n===0&&s?1:n;i===-1||c===0||c===1&&i===a-1&&i===n+1?t.base=t.name=e.slice(f,a):(t.name=e.slice(f,i),t.base=e.slice(f,a),t.ext=e.slice(i,a))}return n>0?t.dir=e.slice(0,n-1):s&&(t.dir="/"),t},sep:"/",delimiter:":",win32:null,posix:null},I.win32=q.win32=q,I.posix=q.posix=I,Ft=Y?q.normalize:I.normalize,He=Y?q.isAbsolute:I.isAbsolute,te=Y?q.join:I.join,S1=Y?q.resolve:I.resolve,$r=Y?q.relative:I.relative,je=Y?q.dirname:I.dirname,lt=Y?q.basename:I.basename,Cr=Y?q.extname:I.extname,vo=Y?q.format:I.format,Eo=Y?q.parse:I.parse,_o=Y?q.toNamespacedPath:I.toNamespacedPath,oe=Y?q.sep:I.sep,$o=Y?q.delimiter:I.delimiter}});function Co(){return 1025+Math.floor(64510*Math.random())}var Ao=O({"out-build/vs/base/common/ports.js"(){"use strict"}}),x1,Mt,ct,Ge,Ut=O({"out-build/vs/base/common/cancellation.js"(){"use strict";y1(),x1=Object.freeze(function(e,t){const s=setTimeout(e.bind(t),0);return{dispose(){clearTimeout(s)}}}),function(e){function t(s){return s===e.None||s===e.Cancelled||s instanceof ct?!0:!s||typeof s!="object"?!1:typeof s.isCancellationRequested=="boolean"&&typeof s.onCancellationRequested=="function"}e.isCancellationToken=t,e.None=Object.freeze({isCancellationRequested:!1,onCancellationRequested:ne.None}),e.Cancelled=Object.freeze({isCancellationRequested:!0,onCancellationRequested:x1})}(Mt||(Mt={})),ct=class{constructor(){this.a=!1,this.b=null}cancel(){this.a||(this.a=!0,this.b&&(this.b.fire(void 0),this.dispose()))}get isCancellationRequested(){return this.a}get onCancellationRequested(){return this.a?x1:(this.b||(this.b=new fe),this.b.event)}dispose(){this.b&&(this.b.dispose(),this.b=null)}},Ge=class{constructor(e){this.f=void 0,this.g=void 0,this.g=e&&e.onCancellationRequested(this.cancel,this)}get token(){return this.f||(this.f=new ct),this.f}cancel(){this.f?this.f instanceof ct&&this.f.cancel():this.f=Mt.Cancelled}dispose(e=!1){e&&this.cancel(),this.g?.dispose(),this.f?this.f instanceof ct&&this.f.dispose():this.f=Mt.None}}}});function Oo(e){return e}var Ar,So=O({"out-build/vs/base/common/cache.js"(){"use strict";Ut(),Ar=class{constructor(e,t){this.a=void 0,this.b=void 0,typeof e=="function"?(this.c=e,this.d=Oo):(this.c=t,this.d=e.getCacheKey)}get(e){const t=this.d(e);return this.b!==t&&(this.b=t,this.a=this.c(e)),this.a}}}}),Fe,Vt=O({"out-build/vs/base/common/lazy.js"(){"use strict";Fe=class{constructor(e){this.d=e,this.a=!1}get hasValue(){return this.a}get value(){if(!this.a)try{this.b=this.d()}catch(e){this.c=e}finally{this.a=!0}if(this.c)throw this.c;return this.b}get rawValue(){return this.b}}}});function Or(e){return e.replace(/[\\\{\}\*\+\?\|\^\$\.\[\]\(\)]/g,"\\$&")}function xo(e,t){if(!e||!t)return e;const s=t.length;if(s===0||e.length===0)return e;let r=0;for(;e.indexOf(t,r)===r;)r=r+s;return e.substring(r)}function Do(e,t){if(!e||!t)return e;const s=t.length,r=e.length;if(s===0||r===0)return e;let i=r,n=-1;for(;n=e.lastIndexOf(t,i-1),!(n===-1||n+s!==i);){if(n===0)return"";i=n}return e.substring(0,i)}function D1(e,t){return e<t?-1:e>t?1:0}function P1(e,t,s=0,r=e.length,i=0,n=t.length){for(;s<r&&i<n;s++,i++){const l=e.charCodeAt(s),c=t.charCodeAt(i);if(l<c)return-1;if(l>c)return 1}const a=r-s,o=n-i;return a<o?-1:a>o?1:0}function Sr(e,t){return ut(e,t,0,e.length,0,t.length)}function ut(e,t,s=0,r=e.length,i=0,n=t.length){for(;s<r&&i<n;s++,i++){let l=e.charCodeAt(s),c=t.charCodeAt(i);if(l===c)continue;if(l>=128||c>=128)return P1(e.toLowerCase(),t.toLowerCase(),s,r,i,n);xr(l)&&(l-=32),xr(c)&&(c-=32);const f=l-c;if(f!==0)return f}const a=r-s,o=n-i;return a<o?-1:a>o?1:0}function xr(e){return e>=97&&e<=122}function Dr(e,t){return e.length===t.length&&ut(e,t)===0}function Pr(e,t){const s=t.length;return t.length>e.length?!1:ut(e,t,0,s)===0}function Po(){return JSON.parse("[0,0,0,51229,51255,12,44061,44087,12,127462,127487,6,7083,7085,5,47645,47671,12,54813,54839,12,128678,128678,14,3270,3270,5,9919,9923,14,45853,45879,12,49437,49463,12,53021,53047,12,71216,71218,7,128398,128399,14,129360,129374,14,2519,2519,5,4448,4519,9,9742,9742,14,12336,12336,14,44957,44983,12,46749,46775,12,48541,48567,12,50333,50359,12,52125,52151,12,53917,53943,12,69888,69890,5,73018,73018,5,127990,127990,14,128558,128559,14,128759,128760,14,129653,129655,14,2027,2035,5,2891,2892,7,3761,3761,5,6683,6683,5,8293,8293,4,9825,9826,14,9999,9999,14,43452,43453,5,44509,44535,12,45405,45431,12,46301,46327,12,47197,47223,12,48093,48119,12,48989,49015,12,49885,49911,12,50781,50807,12,51677,51703,12,52573,52599,12,53469,53495,12,54365,54391,12,65279,65279,4,70471,70472,7,72145,72147,7,119173,119179,5,127799,127818,14,128240,128244,14,128512,128512,14,128652,128652,14,128721,128722,14,129292,129292,14,129445,129450,14,129734,129743,14,1476,1477,5,2366,2368,7,2750,2752,7,3076,3076,5,3415,3415,5,4141,4144,5,6109,6109,5,6964,6964,5,7394,7400,5,9197,9198,14,9770,9770,14,9877,9877,14,9968,9969,14,10084,10084,14,43052,43052,5,43713,43713,5,44285,44311,12,44733,44759,12,45181,45207,12,45629,45655,12,46077,46103,12,46525,46551,12,46973,46999,12,47421,47447,12,47869,47895,12,48317,48343,12,48765,48791,12,49213,49239,12,49661,49687,12,50109,50135,12,50557,50583,12,51005,51031,12,51453,51479,12,51901,51927,12,52349,52375,12,52797,52823,12,53245,53271,12,53693,53719,12,54141,54167,12,54589,54615,12,55037,55063,12,69506,69509,5,70191,70193,5,70841,70841,7,71463,71467,5,72330,72342,5,94031,94031,5,123628,123631,5,127763,127765,14,127941,127941,14,128043,128062,14,128302,128317,14,128465,128467,14,128539,128539,14,128640,128640,14,128662,128662,14,128703,128703,14,128745,128745,14,129004,129007,14,129329,129330,14,129402,129402,14,129483,129483,14,129686,129704,14,130048,131069,14,173,173,4,1757,1757,1,2200,2207,5,2434,2435,7,2631,2632,5,2817,2817,5,3008,3008,5,3201,3201,5,3387,3388,5,3542,3542,5,3902,3903,7,4190,4192,5,6002,6003,5,6439,6440,5,6765,6770,7,7019,7027,5,7154,7155,7,8205,8205,13,8505,8505,14,9654,9654,14,9757,9757,14,9792,9792,14,9852,9853,14,9890,9894,14,9937,9937,14,9981,9981,14,10035,10036,14,11035,11036,14,42654,42655,5,43346,43347,7,43587,43587,5,44006,44007,7,44173,44199,12,44397,44423,12,44621,44647,12,44845,44871,12,45069,45095,12,45293,45319,12,45517,45543,12,45741,45767,12,45965,45991,12,46189,46215,12,46413,46439,12,46637,46663,12,46861,46887,12,47085,47111,12,47309,47335,12,47533,47559,12,47757,47783,12,47981,48007,12,48205,48231,12,48429,48455,12,48653,48679,12,48877,48903,12,49101,49127,12,49325,49351,12,49549,49575,12,49773,49799,12,49997,50023,12,50221,50247,12,50445,50471,12,50669,50695,12,50893,50919,12,51117,51143,12,51341,51367,12,51565,51591,12,51789,51815,12,52013,52039,12,52237,52263,12,52461,52487,12,52685,52711,12,52909,52935,12,53133,53159,12,53357,53383,12,53581,53607,12,53805,53831,12,54029,54055,12,54253,54279,12,54477,54503,12,54701,54727,12,54925,54951,12,55149,55175,12,68101,68102,5,69762,69762,7,70067,70069,7,70371,70378,5,70720,70721,7,71087,71087,5,71341,71341,5,71995,71996,5,72249,72249,7,72850,72871,5,73109,73109,5,118576,118598,5,121505,121519,5,127245,127247,14,127568,127569,14,127777,127777,14,127872,127891,14,127956,127967,14,128015,128016,14,128110,128172,14,128259,128259,14,128367,128368,14,128424,128424,14,128488,128488,14,128530,128532,14,128550,128551,14,128566,128566,14,128647,128647,14,128656,128656,14,128667,128673,14,128691,128693,14,128715,128715,14,128728,128732,14,128752,128752,14,128765,128767,14,129096,129103,14,129311,129311,14,129344,129349,14,129394,129394,14,129413,129425,14,129466,129471,14,129511,129535,14,129664,129666,14,129719,129722,14,129760,129767,14,917536,917631,5,13,13,2,1160,1161,5,1564,1564,4,1807,1807,1,2085,2087,5,2307,2307,7,2382,2383,7,2497,2500,5,2563,2563,7,2677,2677,5,2763,2764,7,2879,2879,5,2914,2915,5,3021,3021,5,3142,3144,5,3263,3263,5,3285,3286,5,3398,3400,7,3530,3530,5,3633,3633,5,3864,3865,5,3974,3975,5,4155,4156,7,4229,4230,5,5909,5909,7,6078,6085,7,6277,6278,5,6451,6456,7,6744,6750,5,6846,6846,5,6972,6972,5,7074,7077,5,7146,7148,7,7222,7223,5,7416,7417,5,8234,8238,4,8417,8417,5,9000,9000,14,9203,9203,14,9730,9731,14,9748,9749,14,9762,9763,14,9776,9783,14,9800,9811,14,9831,9831,14,9872,9873,14,9882,9882,14,9900,9903,14,9929,9933,14,9941,9960,14,9974,9974,14,9989,9989,14,10006,10006,14,10062,10062,14,10160,10160,14,11647,11647,5,12953,12953,14,43019,43019,5,43232,43249,5,43443,43443,5,43567,43568,7,43696,43696,5,43765,43765,7,44013,44013,5,44117,44143,12,44229,44255,12,44341,44367,12,44453,44479,12,44565,44591,12,44677,44703,12,44789,44815,12,44901,44927,12,45013,45039,12,45125,45151,12,45237,45263,12,45349,45375,12,45461,45487,12,45573,45599,12,45685,45711,12,45797,45823,12,45909,45935,12,46021,46047,12,46133,46159,12,46245,46271,12,46357,46383,12,46469,46495,12,46581,46607,12,46693,46719,12,46805,46831,12,46917,46943,12,47029,47055,12,47141,47167,12,47253,47279,12,47365,47391,12,47477,47503,12,47589,47615,12,47701,47727,12,47813,47839,12,47925,47951,12,48037,48063,12,48149,48175,12,48261,48287,12,48373,48399,12,48485,48511,12,48597,48623,12,48709,48735,12,48821,48847,12,48933,48959,12,49045,49071,12,49157,49183,12,49269,49295,12,49381,49407,12,49493,49519,12,49605,49631,12,49717,49743,12,49829,49855,12,49941,49967,12,50053,50079,12,50165,50191,12,50277,50303,12,50389,50415,12,50501,50527,12,50613,50639,12,50725,50751,12,50837,50863,12,50949,50975,12,51061,51087,12,51173,51199,12,51285,51311,12,51397,51423,12,51509,51535,12,51621,51647,12,51733,51759,12,51845,51871,12,51957,51983,12,52069,52095,12,52181,52207,12,52293,52319,12,52405,52431,12,52517,52543,12,52629,52655,12,52741,52767,12,52853,52879,12,52965,52991,12,53077,53103,12,53189,53215,12,53301,53327,12,53413,53439,12,53525,53551,12,53637,53663,12,53749,53775,12,53861,53887,12,53973,53999,12,54085,54111,12,54197,54223,12,54309,54335,12,54421,54447,12,54533,54559,12,54645,54671,12,54757,54783,12,54869,54895,12,54981,55007,12,55093,55119,12,55243,55291,10,66045,66045,5,68325,68326,5,69688,69702,5,69817,69818,5,69957,69958,7,70089,70092,5,70198,70199,5,70462,70462,5,70502,70508,5,70750,70750,5,70846,70846,7,71100,71101,5,71230,71230,7,71351,71351,5,71737,71738,5,72000,72000,7,72160,72160,5,72273,72278,5,72752,72758,5,72882,72883,5,73031,73031,5,73461,73462,7,94192,94193,7,119149,119149,7,121403,121452,5,122915,122916,5,126980,126980,14,127358,127359,14,127535,127535,14,127759,127759,14,127771,127771,14,127792,127793,14,127825,127867,14,127897,127899,14,127945,127945,14,127985,127986,14,128000,128007,14,128021,128021,14,128066,128100,14,128184,128235,14,128249,128252,14,128266,128276,14,128335,128335,14,128379,128390,14,128407,128419,14,128444,128444,14,128481,128481,14,128499,128499,14,128526,128526,14,128536,128536,14,128543,128543,14,128556,128556,14,128564,128564,14,128577,128580,14,128643,128645,14,128649,128649,14,128654,128654,14,128660,128660,14,128664,128664,14,128675,128675,14,128686,128689,14,128695,128696,14,128705,128709,14,128717,128719,14,128725,128725,14,128736,128741,14,128747,128748,14,128755,128755,14,128762,128762,14,128981,128991,14,129009,129023,14,129160,129167,14,129296,129304,14,129320,129327,14,129340,129342,14,129356,129356,14,129388,129392,14,129399,129400,14,129404,129407,14,129432,129442,14,129454,129455,14,129473,129474,14,129485,129487,14,129648,129651,14,129659,129660,14,129671,129679,14,129709,129711,14,129728,129730,14,129751,129753,14,129776,129782,14,917505,917505,4,917760,917999,5,10,10,3,127,159,4,768,879,5,1471,1471,5,1536,1541,1,1648,1648,5,1767,1768,5,1840,1866,5,2070,2073,5,2137,2139,5,2274,2274,1,2363,2363,7,2377,2380,7,2402,2403,5,2494,2494,5,2507,2508,7,2558,2558,5,2622,2624,7,2641,2641,5,2691,2691,7,2759,2760,5,2786,2787,5,2876,2876,5,2881,2884,5,2901,2902,5,3006,3006,5,3014,3016,7,3072,3072,5,3134,3136,5,3157,3158,5,3260,3260,5,3266,3266,5,3274,3275,7,3328,3329,5,3391,3392,7,3405,3405,5,3457,3457,5,3536,3537,7,3551,3551,5,3636,3642,5,3764,3772,5,3895,3895,5,3967,3967,7,3993,4028,5,4146,4151,5,4182,4183,7,4226,4226,5,4253,4253,5,4957,4959,5,5940,5940,7,6070,6070,7,6087,6088,7,6158,6158,4,6432,6434,5,6448,6449,7,6679,6680,5,6742,6742,5,6754,6754,5,6783,6783,5,6912,6915,5,6966,6970,5,6978,6978,5,7042,7042,7,7080,7081,5,7143,7143,7,7150,7150,7,7212,7219,5,7380,7392,5,7412,7412,5,8203,8203,4,8232,8232,4,8265,8265,14,8400,8412,5,8421,8432,5,8617,8618,14,9167,9167,14,9200,9200,14,9410,9410,14,9723,9726,14,9733,9733,14,9745,9745,14,9752,9752,14,9760,9760,14,9766,9766,14,9774,9774,14,9786,9786,14,9794,9794,14,9823,9823,14,9828,9828,14,9833,9850,14,9855,9855,14,9875,9875,14,9880,9880,14,9885,9887,14,9896,9897,14,9906,9916,14,9926,9927,14,9935,9935,14,9939,9939,14,9962,9962,14,9972,9972,14,9978,9978,14,9986,9986,14,9997,9997,14,10002,10002,14,10017,10017,14,10055,10055,14,10071,10071,14,10133,10135,14,10548,10549,14,11093,11093,14,12330,12333,5,12441,12442,5,42608,42610,5,43010,43010,5,43045,43046,5,43188,43203,7,43302,43309,5,43392,43394,5,43446,43449,5,43493,43493,5,43571,43572,7,43597,43597,7,43703,43704,5,43756,43757,5,44003,44004,7,44009,44010,7,44033,44059,12,44089,44115,12,44145,44171,12,44201,44227,12,44257,44283,12,44313,44339,12,44369,44395,12,44425,44451,12,44481,44507,12,44537,44563,12,44593,44619,12,44649,44675,12,44705,44731,12,44761,44787,12,44817,44843,12,44873,44899,12,44929,44955,12,44985,45011,12,45041,45067,12,45097,45123,12,45153,45179,12,45209,45235,12,45265,45291,12,45321,45347,12,45377,45403,12,45433,45459,12,45489,45515,12,45545,45571,12,45601,45627,12,45657,45683,12,45713,45739,12,45769,45795,12,45825,45851,12,45881,45907,12,45937,45963,12,45993,46019,12,46049,46075,12,46105,46131,12,46161,46187,12,46217,46243,12,46273,46299,12,46329,46355,12,46385,46411,12,46441,46467,12,46497,46523,12,46553,46579,12,46609,46635,12,46665,46691,12,46721,46747,12,46777,46803,12,46833,46859,12,46889,46915,12,46945,46971,12,47001,47027,12,47057,47083,12,47113,47139,12,47169,47195,12,47225,47251,12,47281,47307,12,47337,47363,12,47393,47419,12,47449,47475,12,47505,47531,12,47561,47587,12,47617,47643,12,47673,47699,12,47729,47755,12,47785,47811,12,47841,47867,12,47897,47923,12,47953,47979,12,48009,48035,12,48065,48091,12,48121,48147,12,48177,48203,12,48233,48259,12,48289,48315,12,48345,48371,12,48401,48427,12,48457,48483,12,48513,48539,12,48569,48595,12,48625,48651,12,48681,48707,12,48737,48763,12,48793,48819,12,48849,48875,12,48905,48931,12,48961,48987,12,49017,49043,12,49073,49099,12,49129,49155,12,49185,49211,12,49241,49267,12,49297,49323,12,49353,49379,12,49409,49435,12,49465,49491,12,49521,49547,12,49577,49603,12,49633,49659,12,49689,49715,12,49745,49771,12,49801,49827,12,49857,49883,12,49913,49939,12,49969,49995,12,50025,50051,12,50081,50107,12,50137,50163,12,50193,50219,12,50249,50275,12,50305,50331,12,50361,50387,12,50417,50443,12,50473,50499,12,50529,50555,12,50585,50611,12,50641,50667,12,50697,50723,12,50753,50779,12,50809,50835,12,50865,50891,12,50921,50947,12,50977,51003,12,51033,51059,12,51089,51115,12,51145,51171,12,51201,51227,12,51257,51283,12,51313,51339,12,51369,51395,12,51425,51451,12,51481,51507,12,51537,51563,12,51593,51619,12,51649,51675,12,51705,51731,12,51761,51787,12,51817,51843,12,51873,51899,12,51929,51955,12,51985,52011,12,52041,52067,12,52097,52123,12,52153,52179,12,52209,52235,12,52265,52291,12,52321,52347,12,52377,52403,12,52433,52459,12,52489,52515,12,52545,52571,12,52601,52627,12,52657,52683,12,52713,52739,12,52769,52795,12,52825,52851,12,52881,52907,12,52937,52963,12,52993,53019,12,53049,53075,12,53105,53131,12,53161,53187,12,53217,53243,12,53273,53299,12,53329,53355,12,53385,53411,12,53441,53467,12,53497,53523,12,53553,53579,12,53609,53635,12,53665,53691,12,53721,53747,12,53777,53803,12,53833,53859,12,53889,53915,12,53945,53971,12,54001,54027,12,54057,54083,12,54113,54139,12,54169,54195,12,54225,54251,12,54281,54307,12,54337,54363,12,54393,54419,12,54449,54475,12,54505,54531,12,54561,54587,12,54617,54643,12,54673,54699,12,54729,54755,12,54785,54811,12,54841,54867,12,54897,54923,12,54953,54979,12,55009,55035,12,55065,55091,12,55121,55147,12,55177,55203,12,65024,65039,5,65520,65528,4,66422,66426,5,68152,68154,5,69291,69292,5,69633,69633,5,69747,69748,5,69811,69814,5,69826,69826,5,69932,69932,7,70016,70017,5,70079,70080,7,70095,70095,5,70196,70196,5,70367,70367,5,70402,70403,7,70464,70464,5,70487,70487,5,70709,70711,7,70725,70725,7,70833,70834,7,70843,70844,7,70849,70849,7,71090,71093,5,71103,71104,5,71227,71228,7,71339,71339,5,71344,71349,5,71458,71461,5,71727,71735,5,71985,71989,7,71998,71998,5,72002,72002,7,72154,72155,5,72193,72202,5,72251,72254,5,72281,72283,5,72344,72345,5,72766,72766,7,72874,72880,5,72885,72886,5,73023,73029,5,73104,73105,5,73111,73111,5,92912,92916,5,94095,94098,5,113824,113827,4,119142,119142,7,119155,119162,4,119362,119364,5,121476,121476,5,122888,122904,5,123184,123190,5,125252,125258,5,127183,127183,14,127340,127343,14,127377,127386,14,127491,127503,14,127548,127551,14,127744,127756,14,127761,127761,14,127769,127769,14,127773,127774,14,127780,127788,14,127796,127797,14,127820,127823,14,127869,127869,14,127894,127895,14,127902,127903,14,127943,127943,14,127947,127950,14,127972,127972,14,127988,127988,14,127992,127994,14,128009,128011,14,128019,128019,14,128023,128041,14,128064,128064,14,128102,128107,14,128174,128181,14,128238,128238,14,128246,128247,14,128254,128254,14,128264,128264,14,128278,128299,14,128329,128330,14,128348,128359,14,128371,128377,14,128392,128393,14,128401,128404,14,128421,128421,14,128433,128434,14,128450,128452,14,128476,128478,14,128483,128483,14,128495,128495,14,128506,128506,14,128519,128520,14,128528,128528,14,128534,128534,14,128538,128538,14,128540,128542,14,128544,128549,14,128552,128555,14,128557,128557,14,128560,128563,14,128565,128565,14,128567,128576,14,128581,128591,14,128641,128642,14,128646,128646,14,128648,128648,14,128650,128651,14,128653,128653,14,128655,128655,14,128657,128659,14,128661,128661,14,128663,128663,14,128665,128666,14,128674,128674,14,128676,128677,14,128679,128685,14,128690,128690,14,128694,128694,14,128697,128702,14,128704,128704,14,128710,128714,14,128716,128716,14,128720,128720,14,128723,128724,14,128726,128727,14,128733,128735,14,128742,128744,14,128746,128746,14,128749,128751,14,128753,128754,14,128756,128758,14,128761,128761,14,128763,128764,14,128884,128895,14,128992,129003,14,129008,129008,14,129036,129039,14,129114,129119,14,129198,129279,14,129293,129295,14,129305,129310,14,129312,129319,14,129328,129328,14,129331,129338,14,129343,129343,14,129351,129355,14,129357,129359,14,129375,129387,14,129393,129393,14,129395,129398,14,129401,129401,14,129403,129403,14,129408,129412,14,129426,129431,14,129443,129444,14,129451,129453,14,129456,129465,14,129472,129472,14,129475,129482,14,129484,129484,14,129488,129510,14,129536,129647,14,129652,129652,14,129656,129658,14,129661,129663,14,129667,129670,14,129680,129685,14,129705,129708,14,129712,129718,14,129723,129727,14,129731,129733,14,129744,129750,14,129754,129759,14,129768,129775,14,129783,129791,14,917504,917504,4,917506,917535,4,917632,917759,4,918000,921599,4,0,9,4,11,12,4,14,31,4,169,169,14,174,174,14,1155,1159,5,1425,1469,5,1473,1474,5,1479,1479,5,1552,1562,5,1611,1631,5,1750,1756,5,1759,1764,5,1770,1773,5,1809,1809,5,1958,1968,5,2045,2045,5,2075,2083,5,2089,2093,5,2192,2193,1,2250,2273,5,2275,2306,5,2362,2362,5,2364,2364,5,2369,2376,5,2381,2381,5,2385,2391,5,2433,2433,5,2492,2492,5,2495,2496,7,2503,2504,7,2509,2509,5,2530,2531,5,2561,2562,5,2620,2620,5,2625,2626,5,2635,2637,5,2672,2673,5,2689,2690,5,2748,2748,5,2753,2757,5,2761,2761,7,2765,2765,5,2810,2815,5,2818,2819,7,2878,2878,5,2880,2880,7,2887,2888,7,2893,2893,5,2903,2903,5,2946,2946,5,3007,3007,7,3009,3010,7,3018,3020,7,3031,3031,5,3073,3075,7,3132,3132,5,3137,3140,7,3146,3149,5,3170,3171,5,3202,3203,7,3262,3262,7,3264,3265,7,3267,3268,7,3271,3272,7,3276,3277,5,3298,3299,5,3330,3331,7,3390,3390,5,3393,3396,5,3402,3404,7,3406,3406,1,3426,3427,5,3458,3459,7,3535,3535,5,3538,3540,5,3544,3550,7,3570,3571,7,3635,3635,7,3655,3662,5,3763,3763,7,3784,3789,5,3893,3893,5,3897,3897,5,3953,3966,5,3968,3972,5,3981,3991,5,4038,4038,5,4145,4145,7,4153,4154,5,4157,4158,5,4184,4185,5,4209,4212,5,4228,4228,7,4237,4237,5,4352,4447,8,4520,4607,10,5906,5908,5,5938,5939,5,5970,5971,5,6068,6069,5,6071,6077,5,6086,6086,5,6089,6099,5,6155,6157,5,6159,6159,5,6313,6313,5,6435,6438,7,6441,6443,7,6450,6450,5,6457,6459,5,6681,6682,7,6741,6741,7,6743,6743,7,6752,6752,5,6757,6764,5,6771,6780,5,6832,6845,5,6847,6862,5,6916,6916,7,6965,6965,5,6971,6971,7,6973,6977,7,6979,6980,7,7040,7041,5,7073,7073,7,7078,7079,7,7082,7082,7,7142,7142,5,7144,7145,5,7149,7149,5,7151,7153,5,7204,7211,7,7220,7221,7,7376,7378,5,7393,7393,7,7405,7405,5,7415,7415,7,7616,7679,5,8204,8204,5,8206,8207,4,8233,8233,4,8252,8252,14,8288,8292,4,8294,8303,4,8413,8416,5,8418,8420,5,8482,8482,14,8596,8601,14,8986,8987,14,9096,9096,14,9193,9196,14,9199,9199,14,9201,9202,14,9208,9210,14,9642,9643,14,9664,9664,14,9728,9729,14,9732,9732,14,9735,9741,14,9743,9744,14,9746,9746,14,9750,9751,14,9753,9756,14,9758,9759,14,9761,9761,14,9764,9765,14,9767,9769,14,9771,9773,14,9775,9775,14,9784,9785,14,9787,9791,14,9793,9793,14,9795,9799,14,9812,9822,14,9824,9824,14,9827,9827,14,9829,9830,14,9832,9832,14,9851,9851,14,9854,9854,14,9856,9861,14,9874,9874,14,9876,9876,14,9878,9879,14,9881,9881,14,9883,9884,14,9888,9889,14,9895,9895,14,9898,9899,14,9904,9905,14,9917,9918,14,9924,9925,14,9928,9928,14,9934,9934,14,9936,9936,14,9938,9938,14,9940,9940,14,9961,9961,14,9963,9967,14,9970,9971,14,9973,9973,14,9975,9977,14,9979,9980,14,9982,9985,14,9987,9988,14,9992,9996,14,9998,9998,14,10000,10001,14,10004,10004,14,10013,10013,14,10024,10024,14,10052,10052,14,10060,10060,14,10067,10069,14,10083,10083,14,10085,10087,14,10145,10145,14,10175,10175,14,11013,11015,14,11088,11088,14,11503,11505,5,11744,11775,5,12334,12335,5,12349,12349,14,12951,12951,14,42607,42607,5,42612,42621,5,42736,42737,5,43014,43014,5,43043,43044,7,43047,43047,7,43136,43137,7,43204,43205,5,43263,43263,5,43335,43345,5,43360,43388,8,43395,43395,7,43444,43445,7,43450,43451,7,43454,43456,7,43561,43566,5,43569,43570,5,43573,43574,5,43596,43596,5,43644,43644,5,43698,43700,5,43710,43711,5,43755,43755,7,43758,43759,7,43766,43766,5,44005,44005,5,44008,44008,5,44012,44012,7,44032,44032,11,44060,44060,11,44088,44088,11,44116,44116,11,44144,44144,11,44172,44172,11,44200,44200,11,44228,44228,11,44256,44256,11,44284,44284,11,44312,44312,11,44340,44340,11,44368,44368,11,44396,44396,11,44424,44424,11,44452,44452,11,44480,44480,11,44508,44508,11,44536,44536,11,44564,44564,11,44592,44592,11,44620,44620,11,44648,44648,11,44676,44676,11,44704,44704,11,44732,44732,11,44760,44760,11,44788,44788,11,44816,44816,11,44844,44844,11,44872,44872,11,44900,44900,11,44928,44928,11,44956,44956,11,44984,44984,11,45012,45012,11,45040,45040,11,45068,45068,11,45096,45096,11,45124,45124,11,45152,45152,11,45180,45180,11,45208,45208,11,45236,45236,11,45264,45264,11,45292,45292,11,45320,45320,11,45348,45348,11,45376,45376,11,45404,45404,11,45432,45432,11,45460,45460,11,45488,45488,11,45516,45516,11,45544,45544,11,45572,45572,11,45600,45600,11,45628,45628,11,45656,45656,11,45684,45684,11,45712,45712,11,45740,45740,11,45768,45768,11,45796,45796,11,45824,45824,11,45852,45852,11,45880,45880,11,45908,45908,11,45936,45936,11,45964,45964,11,45992,45992,11,46020,46020,11,46048,46048,11,46076,46076,11,46104,46104,11,46132,46132,11,46160,46160,11,46188,46188,11,46216,46216,11,46244,46244,11,46272,46272,11,46300,46300,11,46328,46328,11,46356,46356,11,46384,46384,11,46412,46412,11,46440,46440,11,46468,46468,11,46496,46496,11,46524,46524,11,46552,46552,11,46580,46580,11,46608,46608,11,46636,46636,11,46664,46664,11,46692,46692,11,46720,46720,11,46748,46748,11,46776,46776,11,46804,46804,11,46832,46832,11,46860,46860,11,46888,46888,11,46916,46916,11,46944,46944,11,46972,46972,11,47000,47000,11,47028,47028,11,47056,47056,11,47084,47084,11,47112,47112,11,47140,47140,11,47168,47168,11,47196,47196,11,47224,47224,11,47252,47252,11,47280,47280,11,47308,47308,11,47336,47336,11,47364,47364,11,47392,47392,11,47420,47420,11,47448,47448,11,47476,47476,11,47504,47504,11,47532,47532,11,47560,47560,11,47588,47588,11,47616,47616,11,47644,47644,11,47672,47672,11,47700,47700,11,47728,47728,11,47756,47756,11,47784,47784,11,47812,47812,11,47840,47840,11,47868,47868,11,47896,47896,11,47924,47924,11,47952,47952,11,47980,47980,11,48008,48008,11,48036,48036,11,48064,48064,11,48092,48092,11,48120,48120,11,48148,48148,11,48176,48176,11,48204,48204,11,48232,48232,11,48260,48260,11,48288,48288,11,48316,48316,11,48344,48344,11,48372,48372,11,48400,48400,11,48428,48428,11,48456,48456,11,48484,48484,11,48512,48512,11,48540,48540,11,48568,48568,11,48596,48596,11,48624,48624,11,48652,48652,11,48680,48680,11,48708,48708,11,48736,48736,11,48764,48764,11,48792,48792,11,48820,48820,11,48848,48848,11,48876,48876,11,48904,48904,11,48932,48932,11,48960,48960,11,48988,48988,11,49016,49016,11,49044,49044,11,49072,49072,11,49100,49100,11,49128,49128,11,49156,49156,11,49184,49184,11,49212,49212,11,49240,49240,11,49268,49268,11,49296,49296,11,49324,49324,11,49352,49352,11,49380,49380,11,49408,49408,11,49436,49436,11,49464,49464,11,49492,49492,11,49520,49520,11,49548,49548,11,49576,49576,11,49604,49604,11,49632,49632,11,49660,49660,11,49688,49688,11,49716,49716,11,49744,49744,11,49772,49772,11,49800,49800,11,49828,49828,11,49856,49856,11,49884,49884,11,49912,49912,11,49940,49940,11,49968,49968,11,49996,49996,11,50024,50024,11,50052,50052,11,50080,50080,11,50108,50108,11,50136,50136,11,50164,50164,11,50192,50192,11,50220,50220,11,50248,50248,11,50276,50276,11,50304,50304,11,50332,50332,11,50360,50360,11,50388,50388,11,50416,50416,11,50444,50444,11,50472,50472,11,50500,50500,11,50528,50528,11,50556,50556,11,50584,50584,11,50612,50612,11,50640,50640,11,50668,50668,11,50696,50696,11,50724,50724,11,50752,50752,11,50780,50780,11,50808,50808,11,50836,50836,11,50864,50864,11,50892,50892,11,50920,50920,11,50948,50948,11,50976,50976,11,51004,51004,11,51032,51032,11,51060,51060,11,51088,51088,11,51116,51116,11,51144,51144,11,51172,51172,11,51200,51200,11,51228,51228,11,51256,51256,11,51284,51284,11,51312,51312,11,51340,51340,11,51368,51368,11,51396,51396,11,51424,51424,11,51452,51452,11,51480,51480,11,51508,51508,11,51536,51536,11,51564,51564,11,51592,51592,11,51620,51620,11,51648,51648,11,51676,51676,11,51704,51704,11,51732,51732,11,51760,51760,11,51788,51788,11,51816,51816,11,51844,51844,11,51872,51872,11,51900,51900,11,51928,51928,11,51956,51956,11,51984,51984,11,52012,52012,11,52040,52040,11,52068,52068,11,52096,52096,11,52124,52124,11,52152,52152,11,52180,52180,11,52208,52208,11,52236,52236,11,52264,52264,11,52292,52292,11,52320,52320,11,52348,52348,11,52376,52376,11,52404,52404,11,52432,52432,11,52460,52460,11,52488,52488,11,52516,52516,11,52544,52544,11,52572,52572,11,52600,52600,11,52628,52628,11,52656,52656,11,52684,52684,11,52712,52712,11,52740,52740,11,52768,52768,11,52796,52796,11,52824,52824,11,52852,52852,11,52880,52880,11,52908,52908,11,52936,52936,11,52964,52964,11,52992,52992,11,53020,53020,11,53048,53048,11,53076,53076,11,53104,53104,11,53132,53132,11,53160,53160,11,53188,53188,11,53216,53216,11,53244,53244,11,53272,53272,11,53300,53300,11,53328,53328,11,53356,53356,11,53384,53384,11,53412,53412,11,53440,53440,11,53468,53468,11,53496,53496,11,53524,53524,11,53552,53552,11,53580,53580,11,53608,53608,11,53636,53636,11,53664,53664,11,53692,53692,11,53720,53720,11,53748,53748,11,53776,53776,11,53804,53804,11,53832,53832,11,53860,53860,11,53888,53888,11,53916,53916,11,53944,53944,11,53972,53972,11,54000,54000,11,54028,54028,11,54056,54056,11,54084,54084,11,54112,54112,11,54140,54140,11,54168,54168,11,54196,54196,11,54224,54224,11,54252,54252,11,54280,54280,11,54308,54308,11,54336,54336,11,54364,54364,11,54392,54392,11,54420,54420,11,54448,54448,11,54476,54476,11,54504,54504,11,54532,54532,11,54560,54560,11,54588,54588,11,54616,54616,11,54644,54644,11,54672,54672,11,54700,54700,11,54728,54728,11,54756,54756,11,54784,54784,11,54812,54812,11,54840,54840,11,54868,54868,11,54896,54896,11,54924,54924,11,54952,54952,11,54980,54980,11,55008,55008,11,55036,55036,11,55064,55064,11,55092,55092,11,55120,55120,11,55148,55148,11,55176,55176,11,55216,55238,9,64286,64286,5,65056,65071,5,65438,65439,5,65529,65531,4,66272,66272,5,68097,68099,5,68108,68111,5,68159,68159,5,68900,68903,5,69446,69456,5,69632,69632,7,69634,69634,7,69744,69744,5,69759,69761,5,69808,69810,7,69815,69816,7,69821,69821,1,69837,69837,1,69927,69931,5,69933,69940,5,70003,70003,5,70018,70018,7,70070,70078,5,70082,70083,1,70094,70094,7,70188,70190,7,70194,70195,7,70197,70197,7,70206,70206,5,70368,70370,7,70400,70401,5,70459,70460,5,70463,70463,7,70465,70468,7,70475,70477,7,70498,70499,7,70512,70516,5,70712,70719,5,70722,70724,5,70726,70726,5,70832,70832,5,70835,70840,5,70842,70842,5,70845,70845,5,70847,70848,5,70850,70851,5,71088,71089,7,71096,71099,7,71102,71102,7,71132,71133,5,71219,71226,5,71229,71229,5,71231,71232,5,71340,71340,7,71342,71343,7,71350,71350,7,71453,71455,5,71462,71462,7,71724,71726,7,71736,71736,7,71984,71984,5,71991,71992,7,71997,71997,7,71999,71999,1,72001,72001,1,72003,72003,5,72148,72151,5,72156,72159,7,72164,72164,7,72243,72248,5,72250,72250,1,72263,72263,5,72279,72280,7,72324,72329,1,72343,72343,7,72751,72751,7,72760,72765,5,72767,72767,5,72873,72873,7,72881,72881,7,72884,72884,7,73009,73014,5,73020,73021,5,73030,73030,1,73098,73102,7,73107,73108,7,73110,73110,7,73459,73460,5,78896,78904,4,92976,92982,5,94033,94087,7,94180,94180,5,113821,113822,5,118528,118573,5,119141,119141,5,119143,119145,5,119150,119154,5,119163,119170,5,119210,119213,5,121344,121398,5,121461,121461,5,121499,121503,5,122880,122886,5,122907,122913,5,122918,122922,5,123566,123566,5,125136,125142,5,126976,126979,14,126981,127182,14,127184,127231,14,127279,127279,14,127344,127345,14,127374,127374,14,127405,127461,14,127489,127490,14,127514,127514,14,127538,127546,14,127561,127567,14,127570,127743,14,127757,127758,14,127760,127760,14,127762,127762,14,127766,127768,14,127770,127770,14,127772,127772,14,127775,127776,14,127778,127779,14,127789,127791,14,127794,127795,14,127798,127798,14,127819,127819,14,127824,127824,14,127868,127868,14,127870,127871,14,127892,127893,14,127896,127896,14,127900,127901,14,127904,127940,14,127942,127942,14,127944,127944,14,127946,127946,14,127951,127955,14,127968,127971,14,127973,127984,14,127987,127987,14,127989,127989,14,127991,127991,14,127995,127999,5,128008,128008,14,128012,128014,14,128017,128018,14,128020,128020,14,128022,128022,14,128042,128042,14,128063,128063,14,128065,128065,14,128101,128101,14,128108,128109,14,128173,128173,14,128182,128183,14,128236,128237,14,128239,128239,14,128245,128245,14,128248,128248,14,128253,128253,14,128255,128258,14,128260,128263,14,128265,128265,14,128277,128277,14,128300,128301,14,128326,128328,14,128331,128334,14,128336,128347,14,128360,128366,14,128369,128370,14,128378,128378,14,128391,128391,14,128394,128397,14,128400,128400,14,128405,128406,14,128420,128420,14,128422,128423,14,128425,128432,14,128435,128443,14,128445,128449,14,128453,128464,14,128468,128475,14,128479,128480,14,128482,128482,14,128484,128487,14,128489,128494,14,128496,128498,14,128500,128505,14,128507,128511,14,128513,128518,14,128521,128525,14,128527,128527,14,128529,128529,14,128533,128533,14,128535,128535,14,128537,128537,14]")}var Lr,kr,Nr,Lo,ko,Rr,No,Tr,Ro,To,Me=O({"out-build/vs/base/common/strings.js"(){"use strict";So(),Vt(),Lr=/(?:\x1b\[|\x9b)[=?>!]?[\d;:]*["$#'* ]?[a-zA-Z@^`{}|~]/,kr=/(?:\x1b\]|\x9d).*?(?:\x1b\\|\x07|\x9c)/,Nr=/\x1b(?:[ #%\(\)\*\+\-\.\/]?[a-zA-Z0-9\|}~@])/,Lo=new RegExp("(?:"+[Lr.source,kr.source,Nr.source].join("|")+")","g"),ko="\uFEFF",function(e){e[e.Other=0]="Other",e[e.Prepend=1]="Prepend",e[e.CR=2]="CR",e[e.LF=3]="LF",e[e.Control=4]="Control",e[e.Extend=5]="Extend",e[e.Regional_Indicator=6]="Regional_Indicator",e[e.SpacingMark=7]="SpacingMark",e[e.L=8]="L",e[e.V=9]="V",e[e.T=10]="T",e[e.LV=11]="LV",e[e.LVT=12]="LVT",e[e.ZWJ=13]="ZWJ",e[e.Extended_Pictographic=14]="Extended_Pictographic"}(Rr||(Rr={})),No=class _t{static{this.c=null}static getInstance(){return _t.c||(_t.c=new _t),_t.c}constructor(){this.d=Po()}getGraphemeBreakType(t){if(t<32)return t===10?3:t===13?2:4;if(t<127)return 0;const s=this.d,r=s.length/3;let i=1;for(;i<=r;)if(t<s[3*i])i=2*i;else if(t>s[3*i+1])i=2*i+1;else return s[3*i+2];return 0}},function(e){e[e.zwj=8205]="zwj",e[e.emojiVariantSelector=65039]="emojiVariantSelector",e[e.enclosingKeyCap=8419]="enclosingKeyCap",e[e.space=32]="space"}(Tr||(Tr={})),Ro=class $t{static{this.c=new Fe(()=>JSON.parse('{"_common":[8232,32,8233,32,5760,32,8192,32,8193,32,8194,32,8195,32,8196,32,8197,32,8198,32,8200,32,8201,32,8202,32,8287,32,8199,32,8239,32,2042,95,65101,95,65102,95,65103,95,8208,45,8209,45,8210,45,65112,45,1748,45,8259,45,727,45,8722,45,10134,45,11450,45,1549,44,1643,44,184,44,42233,44,894,59,2307,58,2691,58,1417,58,1795,58,1796,58,5868,58,65072,58,6147,58,6153,58,8282,58,1475,58,760,58,42889,58,8758,58,720,58,42237,58,451,33,11601,33,660,63,577,63,2429,63,5038,63,42731,63,119149,46,8228,46,1793,46,1794,46,42510,46,68176,46,1632,46,1776,46,42232,46,1373,96,65287,96,8219,96,1523,96,8242,96,1370,96,8175,96,65344,96,900,96,8189,96,8125,96,8127,96,8190,96,697,96,884,96,712,96,714,96,715,96,756,96,699,96,701,96,700,96,702,96,42892,96,1497,96,2036,96,2037,96,5194,96,5836,96,94033,96,94034,96,65339,91,10088,40,10098,40,12308,40,64830,40,65341,93,10089,41,10099,41,12309,41,64831,41,10100,123,119060,123,10101,125,65342,94,8270,42,1645,42,8727,42,66335,42,5941,47,8257,47,8725,47,8260,47,9585,47,10187,47,10744,47,119354,47,12755,47,12339,47,11462,47,20031,47,12035,47,65340,92,65128,92,8726,92,10189,92,10741,92,10745,92,119311,92,119355,92,12756,92,20022,92,12034,92,42872,38,708,94,710,94,5869,43,10133,43,66203,43,8249,60,10094,60,706,60,119350,60,5176,60,5810,60,5120,61,11840,61,12448,61,42239,61,8250,62,10095,62,707,62,119351,62,5171,62,94015,62,8275,126,732,126,8128,126,8764,126,65372,124,65293,45,118002,50,120784,50,120794,50,120804,50,120814,50,120824,50,130034,50,42842,50,423,50,1000,50,42564,50,5311,50,42735,50,119302,51,118003,51,120785,51,120795,51,120805,51,120815,51,120825,51,130035,51,42923,51,540,51,439,51,42858,51,11468,51,1248,51,94011,51,71882,51,118004,52,120786,52,120796,52,120806,52,120816,52,120826,52,130036,52,5070,52,71855,52,118005,53,120787,53,120797,53,120807,53,120817,53,120827,53,130037,53,444,53,71867,53,118006,54,120788,54,120798,54,120808,54,120818,54,120828,54,130038,54,11474,54,5102,54,71893,54,119314,55,118007,55,120789,55,120799,55,120809,55,120819,55,120829,55,130039,55,66770,55,71878,55,2819,56,2538,56,2666,56,125131,56,118008,56,120790,56,120800,56,120810,56,120820,56,120830,56,130040,56,547,56,546,56,66330,56,2663,57,2920,57,2541,57,3437,57,118009,57,120791,57,120801,57,120811,57,120821,57,120831,57,130041,57,42862,57,11466,57,71884,57,71852,57,71894,57,9082,97,65345,97,119834,97,119886,97,119938,97,119990,97,120042,97,120094,97,120146,97,120198,97,120250,97,120302,97,120354,97,120406,97,120458,97,593,97,945,97,120514,97,120572,97,120630,97,120688,97,120746,97,65313,65,117974,65,119808,65,119860,65,119912,65,119964,65,120016,65,120068,65,120120,65,120172,65,120224,65,120276,65,120328,65,120380,65,120432,65,913,65,120488,65,120546,65,120604,65,120662,65,120720,65,5034,65,5573,65,42222,65,94016,65,66208,65,119835,98,119887,98,119939,98,119991,98,120043,98,120095,98,120147,98,120199,98,120251,98,120303,98,120355,98,120407,98,120459,98,388,98,5071,98,5234,98,5551,98,65314,66,8492,66,117975,66,119809,66,119861,66,119913,66,120017,66,120069,66,120121,66,120173,66,120225,66,120277,66,120329,66,120381,66,120433,66,42932,66,914,66,120489,66,120547,66,120605,66,120663,66,120721,66,5108,66,5623,66,42192,66,66178,66,66209,66,66305,66,65347,99,8573,99,119836,99,119888,99,119940,99,119992,99,120044,99,120096,99,120148,99,120200,99,120252,99,120304,99,120356,99,120408,99,120460,99,7428,99,1010,99,11429,99,43951,99,66621,99,128844,67,71913,67,71922,67,65315,67,8557,67,8450,67,8493,67,117976,67,119810,67,119862,67,119914,67,119966,67,120018,67,120174,67,120226,67,120278,67,120330,67,120382,67,120434,67,1017,67,11428,67,5087,67,42202,67,66210,67,66306,67,66581,67,66844,67,8574,100,8518,100,119837,100,119889,100,119941,100,119993,100,120045,100,120097,100,120149,100,120201,100,120253,100,120305,100,120357,100,120409,100,120461,100,1281,100,5095,100,5231,100,42194,100,8558,68,8517,68,117977,68,119811,68,119863,68,119915,68,119967,68,120019,68,120071,68,120123,68,120175,68,120227,68,120279,68,120331,68,120383,68,120435,68,5024,68,5598,68,5610,68,42195,68,8494,101,65349,101,8495,101,8519,101,119838,101,119890,101,119942,101,120046,101,120098,101,120150,101,120202,101,120254,101,120306,101,120358,101,120410,101,120462,101,43826,101,1213,101,8959,69,65317,69,8496,69,117978,69,119812,69,119864,69,119916,69,120020,69,120072,69,120124,69,120176,69,120228,69,120280,69,120332,69,120384,69,120436,69,917,69,120492,69,120550,69,120608,69,120666,69,120724,69,11577,69,5036,69,42224,69,71846,69,71854,69,66182,69,119839,102,119891,102,119943,102,119995,102,120047,102,120099,102,120151,102,120203,102,120255,102,120307,102,120359,102,120411,102,120463,102,43829,102,42905,102,383,102,7837,102,1412,102,119315,70,8497,70,117979,70,119813,70,119865,70,119917,70,120021,70,120073,70,120125,70,120177,70,120229,70,120281,70,120333,70,120385,70,120437,70,42904,70,988,70,120778,70,5556,70,42205,70,71874,70,71842,70,66183,70,66213,70,66853,70,65351,103,8458,103,119840,103,119892,103,119944,103,120048,103,120100,103,120152,103,120204,103,120256,103,120308,103,120360,103,120412,103,120464,103,609,103,7555,103,397,103,1409,103,117980,71,119814,71,119866,71,119918,71,119970,71,120022,71,120074,71,120126,71,120178,71,120230,71,120282,71,120334,71,120386,71,120438,71,1292,71,5056,71,5107,71,42198,71,65352,104,8462,104,119841,104,119945,104,119997,104,120049,104,120101,104,120153,104,120205,104,120257,104,120309,104,120361,104,120413,104,120465,104,1211,104,1392,104,5058,104,65320,72,8459,72,8460,72,8461,72,117981,72,119815,72,119867,72,119919,72,120023,72,120179,72,120231,72,120283,72,120335,72,120387,72,120439,72,919,72,120494,72,120552,72,120610,72,120668,72,120726,72,11406,72,5051,72,5500,72,42215,72,66255,72,731,105,9075,105,65353,105,8560,105,8505,105,8520,105,119842,105,119894,105,119946,105,119998,105,120050,105,120102,105,120154,105,120206,105,120258,105,120310,105,120362,105,120414,105,120466,105,120484,105,618,105,617,105,953,105,8126,105,890,105,120522,105,120580,105,120638,105,120696,105,120754,105,1110,105,42567,105,1231,105,43893,105,5029,105,71875,105,65354,106,8521,106,119843,106,119895,106,119947,106,119999,106,120051,106,120103,106,120155,106,120207,106,120259,106,120311,106,120363,106,120415,106,120467,106,1011,106,1112,106,65322,74,117983,74,119817,74,119869,74,119921,74,119973,74,120025,74,120077,74,120129,74,120181,74,120233,74,120285,74,120337,74,120389,74,120441,74,42930,74,895,74,1032,74,5035,74,5261,74,42201,74,119844,107,119896,107,119948,107,120000,107,120052,107,120104,107,120156,107,120208,107,120260,107,120312,107,120364,107,120416,107,120468,107,8490,75,65323,75,117984,75,119818,75,119870,75,119922,75,119974,75,120026,75,120078,75,120130,75,120182,75,120234,75,120286,75,120338,75,120390,75,120442,75,922,75,120497,75,120555,75,120613,75,120671,75,120729,75,11412,75,5094,75,5845,75,42199,75,66840,75,1472,108,8739,73,9213,73,65512,73,1633,108,1777,73,66336,108,125127,108,118001,108,120783,73,120793,73,120803,73,120813,73,120823,73,130033,73,65321,73,8544,73,8464,73,8465,73,117982,108,119816,73,119868,73,119920,73,120024,73,120128,73,120180,73,120232,73,120284,73,120336,73,120388,73,120440,73,65356,108,8572,73,8467,108,119845,108,119897,108,119949,108,120001,108,120053,108,120105,73,120157,73,120209,73,120261,73,120313,73,120365,73,120417,73,120469,73,448,73,120496,73,120554,73,120612,73,120670,73,120728,73,11410,73,1030,73,1216,73,1493,108,1503,108,1575,108,126464,108,126592,108,65166,108,65165,108,1994,108,11599,73,5825,73,42226,73,93992,73,66186,124,66313,124,119338,76,8556,76,8466,76,117985,76,119819,76,119871,76,119923,76,120027,76,120079,76,120131,76,120183,76,120235,76,120287,76,120339,76,120391,76,120443,76,11472,76,5086,76,5290,76,42209,76,93974,76,71843,76,71858,76,66587,76,66854,76,65325,77,8559,77,8499,77,117986,77,119820,77,119872,77,119924,77,120028,77,120080,77,120132,77,120184,77,120236,77,120288,77,120340,77,120392,77,120444,77,924,77,120499,77,120557,77,120615,77,120673,77,120731,77,1018,77,11416,77,5047,77,5616,77,5846,77,42207,77,66224,77,66321,77,119847,110,119899,110,119951,110,120003,110,120055,110,120107,110,120159,110,120211,110,120263,110,120315,110,120367,110,120419,110,120471,110,1400,110,1404,110,65326,78,8469,78,117987,78,119821,78,119873,78,119925,78,119977,78,120029,78,120081,78,120185,78,120237,78,120289,78,120341,78,120393,78,120445,78,925,78,120500,78,120558,78,120616,78,120674,78,120732,78,11418,78,42208,78,66835,78,3074,111,3202,111,3330,111,3458,111,2406,111,2662,111,2790,111,3046,111,3174,111,3302,111,3430,111,3664,111,3792,111,4160,111,1637,111,1781,111,65359,111,8500,111,119848,111,119900,111,119952,111,120056,111,120108,111,120160,111,120212,111,120264,111,120316,111,120368,111,120420,111,120472,111,7439,111,7441,111,43837,111,959,111,120528,111,120586,111,120644,111,120702,111,120760,111,963,111,120532,111,120590,111,120648,111,120706,111,120764,111,11423,111,4351,111,1413,111,1505,111,1607,111,126500,111,126564,111,126596,111,65259,111,65260,111,65258,111,65257,111,1726,111,64428,111,64429,111,64427,111,64426,111,1729,111,64424,111,64425,111,64423,111,64422,111,1749,111,3360,111,4125,111,66794,111,71880,111,71895,111,66604,111,1984,79,2534,79,2918,79,12295,79,70864,79,71904,79,118000,79,120782,79,120792,79,120802,79,120812,79,120822,79,130032,79,65327,79,117988,79,119822,79,119874,79,119926,79,119978,79,120030,79,120082,79,120134,79,120186,79,120238,79,120290,79,120342,79,120394,79,120446,79,927,79,120502,79,120560,79,120618,79,120676,79,120734,79,11422,79,1365,79,11604,79,4816,79,2848,79,66754,79,42227,79,71861,79,66194,79,66219,79,66564,79,66838,79,9076,112,65360,112,119849,112,119901,112,119953,112,120005,112,120057,112,120109,112,120161,112,120213,112,120265,112,120317,112,120369,112,120421,112,120473,112,961,112,120530,112,120544,112,120588,112,120602,112,120646,112,120660,112,120704,112,120718,112,120762,112,120776,112,11427,112,65328,80,8473,80,117989,80,119823,80,119875,80,119927,80,119979,80,120031,80,120083,80,120187,80,120239,80,120291,80,120343,80,120395,80,120447,80,929,80,120504,80,120562,80,120620,80,120678,80,120736,80,11426,80,5090,80,5229,80,42193,80,66197,80,119850,113,119902,113,119954,113,120006,113,120058,113,120110,113,120162,113,120214,113,120266,113,120318,113,120370,113,120422,113,120474,113,1307,113,1379,113,1382,113,8474,81,117990,81,119824,81,119876,81,119928,81,119980,81,120032,81,120084,81,120188,81,120240,81,120292,81,120344,81,120396,81,120448,81,11605,81,119851,114,119903,114,119955,114,120007,114,120059,114,120111,114,120163,114,120215,114,120267,114,120319,114,120371,114,120423,114,120475,114,43847,114,43848,114,7462,114,11397,114,43905,114,119318,82,8475,82,8476,82,8477,82,117991,82,119825,82,119877,82,119929,82,120033,82,120189,82,120241,82,120293,82,120345,82,120397,82,120449,82,422,82,5025,82,5074,82,66740,82,5511,82,42211,82,94005,82,65363,115,119852,115,119904,115,119956,115,120008,115,120060,115,120112,115,120164,115,120216,115,120268,115,120320,115,120372,115,120424,115,120476,115,42801,115,445,115,1109,115,43946,115,71873,115,66632,115,65331,83,117992,83,119826,83,119878,83,119930,83,119982,83,120034,83,120086,83,120138,83,120190,83,120242,83,120294,83,120346,83,120398,83,120450,83,1029,83,1359,83,5077,83,5082,83,42210,83,94010,83,66198,83,66592,83,119853,116,119905,116,119957,116,120009,116,120061,116,120113,116,120165,116,120217,116,120269,116,120321,116,120373,116,120425,116,120477,116,8868,84,10201,84,128872,84,65332,84,117993,84,119827,84,119879,84,119931,84,119983,84,120035,84,120087,84,120139,84,120191,84,120243,84,120295,84,120347,84,120399,84,120451,84,932,84,120507,84,120565,84,120623,84,120681,84,120739,84,11430,84,5026,84,42196,84,93962,84,71868,84,66199,84,66225,84,66325,84,119854,117,119906,117,119958,117,120010,117,120062,117,120114,117,120166,117,120218,117,120270,117,120322,117,120374,117,120426,117,120478,117,42911,117,7452,117,43854,117,43858,117,651,117,965,117,120534,117,120592,117,120650,117,120708,117,120766,117,1405,117,66806,117,71896,117,8746,85,8899,85,117994,85,119828,85,119880,85,119932,85,119984,85,120036,85,120088,85,120140,85,120192,85,120244,85,120296,85,120348,85,120400,85,120452,85,1357,85,4608,85,66766,85,5196,85,42228,85,94018,85,71864,85,8744,118,8897,118,65366,118,8564,118,119855,118,119907,118,119959,118,120011,118,120063,118,120115,118,120167,118,120219,118,120271,118,120323,118,120375,118,120427,118,120479,118,7456,118,957,118,120526,118,120584,118,120642,118,120700,118,120758,118,1141,118,1496,118,71430,118,43945,118,71872,118,119309,86,1639,86,1783,86,8548,86,117995,86,119829,86,119881,86,119933,86,119985,86,120037,86,120089,86,120141,86,120193,86,120245,86,120297,86,120349,86,120401,86,120453,86,1140,86,11576,86,5081,86,5167,86,42719,86,42214,86,93960,86,71840,86,66845,86,623,119,119856,119,119908,119,119960,119,120012,119,120064,119,120116,119,120168,119,120220,119,120272,119,120324,119,120376,119,120428,119,120480,119,7457,119,1121,119,1309,119,1377,119,71434,119,71438,119,71439,119,43907,119,71910,87,71919,87,117996,87,119830,87,119882,87,119934,87,119986,87,120038,87,120090,87,120142,87,120194,87,120246,87,120298,87,120350,87,120402,87,120454,87,1308,87,5043,87,5076,87,42218,87,5742,120,10539,120,10540,120,10799,120,65368,120,8569,120,119857,120,119909,120,119961,120,120013,120,120065,120,120117,120,120169,120,120221,120,120273,120,120325,120,120377,120,120429,120,120481,120,5441,120,5501,120,5741,88,9587,88,66338,88,71916,88,65336,88,8553,88,117997,88,119831,88,119883,88,119935,88,119987,88,120039,88,120091,88,120143,88,120195,88,120247,88,120299,88,120351,88,120403,88,120455,88,42931,88,935,88,120510,88,120568,88,120626,88,120684,88,120742,88,11436,88,11613,88,5815,88,42219,88,66192,88,66228,88,66327,88,66855,88,611,121,7564,121,65369,121,119858,121,119910,121,119962,121,120014,121,120066,121,120118,121,120170,121,120222,121,120274,121,120326,121,120378,121,120430,121,120482,121,655,121,7935,121,43866,121,947,121,8509,121,120516,121,120574,121,120632,121,120690,121,120748,121,1199,121,4327,121,71900,121,65337,89,117998,89,119832,89,119884,89,119936,89,119988,89,120040,89,120092,89,120144,89,120196,89,120248,89,120300,89,120352,89,120404,89,120456,89,933,89,978,89,120508,89,120566,89,120624,89,120682,89,120740,89,11432,89,1198,89,5033,89,5053,89,42220,89,94019,89,71844,89,66226,89,119859,122,119911,122,119963,122,120015,122,120067,122,120119,122,120171,122,120223,122,120275,122,120327,122,120379,122,120431,122,120483,122,7458,122,43923,122,71876,122,71909,90,66293,90,65338,90,8484,90,8488,90,117999,90,119833,90,119885,90,119937,90,119989,90,120041,90,120197,90,120249,90,120301,90,120353,90,120405,90,120457,90,918,90,120493,90,120551,90,120609,90,120667,90,120725,90,5059,90,42204,90,71849,90,65282,34,65283,35,65284,36,65285,37,65286,38,65290,42,65291,43,65294,46,65295,47,65296,48,65298,50,65299,51,65300,52,65301,53,65302,54,65303,55,65304,56,65305,57,65308,60,65309,61,65310,62,65312,64,65316,68,65318,70,65319,71,65324,76,65329,81,65330,82,65333,85,65334,86,65335,87,65343,95,65346,98,65348,100,65350,102,65355,107,65357,109,65358,110,65361,113,65362,114,65364,116,65365,117,65367,119,65370,122,65371,123,65373,125,119846,109],"_default":[160,32,8211,45,65374,126,8218,44,65306,58,65281,33,8216,96,8217,96,8245,96,180,96,12494,47,1047,51,1073,54,1072,97,1040,65,1068,98,1042,66,1089,99,1057,67,1077,101,1045,69,1053,72,305,105,1050,75,921,73,1052,77,1086,111,1054,79,1009,112,1088,112,1056,80,1075,114,1058,84,215,120,1093,120,1061,88,1091,121,1059,89,65288,40,65289,41,65292,44,65297,49,65307,59,65311,63],"cs":[65374,126,8218,44,65306,58,65281,33,8216,96,8245,96,180,96,12494,47,1047,51,1073,54,1072,97,1040,65,1068,98,1042,66,1089,99,1057,67,1077,101,1045,69,1053,72,305,105,1050,75,921,73,1052,77,1086,111,1054,79,1009,112,1088,112,1056,80,1075,114,1058,84,1093,120,1061,88,1091,121,1059,89,65288,40,65289,41,65292,44,65297,49,65307,59,65311,63],"de":[65374,126,65306,58,65281,33,8245,96,180,96,12494,47,1047,51,1073,54,1072,97,1040,65,1068,98,1042,66,1089,99,1057,67,1077,101,1045,69,1053,72,305,105,1050,75,921,73,1052,77,1086,111,1054,79,1009,112,1088,112,1056,80,1075,114,1058,84,1093,120,1061,88,1091,121,1059,89,65288,40,65289,41,65292,44,65297,49,65307,59,65311,63],"es":[8211,45,65374,126,8218,44,65306,58,65281,33,8245,96,180,96,12494,47,1047,51,1073,54,1072,97,1040,65,1068,98,1042,66,1089,99,1057,67,1077,101,1045,69,1053,72,305,105,1050,75,1052,77,1086,111,1054,79,1009,112,1088,112,1056,80,1075,114,1058,84,215,120,1093,120,1061,88,1091,121,1059,89,65288,40,65289,41,65292,44,65297,49,65307,59,65311,63],"fr":[65374,126,8218,44,65306,58,65281,33,8216,96,8245,96,12494,47,1047,51,1073,54,1072,97,1040,65,1068,98,1042,66,1089,99,1057,67,1077,101,1045,69,1053,72,305,105,1050,75,921,73,1052,77,1086,111,1054,79,1009,112,1088,112,1056,80,1075,114,1058,84,215,120,1093,120,1061,88,1091,121,1059,89,65288,40,65289,41,65292,44,65297,49,65307,59,65311,63],"it":[160,32,8211,45,65374,126,8218,44,65306,58,65281,33,8245,96,180,96,12494,47,1047,51,1073,54,1072,97,1040,65,1068,98,1042,66,1089,99,1057,67,1077,101,1045,69,1053,72,305,105,1050,75,921,73,1052,77,1086,111,1054,79,1009,112,1088,112,1056,80,1075,114,1058,84,215,120,1093,120,1061,88,1091,121,1059,89,65288,40,65289,41,65292,44,65297,49,65307,59,65311,63],"ja":[8211,45,8218,44,65281,33,8216,96,8245,96,180,96,1047,51,1073,54,1072,97,1040,65,1068,98,1042,66,1089,99,1057,67,1077,101,1045,69,1053,72,305,105,1050,75,921,73,1052,77,1086,111,1054,79,1009,112,1088,112,1056,80,1075,114,1058,84,215,120,1093,120,1061,88,1091,121,1059,89,65292,44,65297,49,65307,59],"ko":[8211,45,65374,126,8218,44,65306,58,65281,33,8245,96,180,96,12494,47,1047,51,1073,54,1072,97,1040,65,1068,98,1042,66,1089,99,1057,67,1077,101,1045,69,1053,72,305,105,1050,75,921,73,1052,77,1086,111,1054,79,1009,112,1088,112,1056,80,1075,114,1058,84,215,120,1093,120,1061,88,1091,121,1059,89,65288,40,65289,41,65292,44,65297,49,65307,59,65311,63],"pl":[65374,126,65306,58,65281,33,8216,96,8245,96,180,96,12494,47,1047,51,1073,54,1072,97,1040,65,1068,98,1042,66,1089,99,1057,67,1077,101,1045,69,1053,72,305,105,1050,75,921,73,1052,77,1086,111,1054,79,1009,112,1088,112,1056,80,1075,114,1058,84,215,120,1093,120,1061,88,1091,121,1059,89,65288,40,65289,41,65292,44,65297,49,65307,59,65311,63],"pt-BR":[65374,126,8218,44,65306,58,65281,33,8216,96,8245,96,180,96,12494,47,1047,51,1073,54,1072,97,1040,65,1068,98,1042,66,1089,99,1057,67,1077,101,1045,69,1053,72,305,105,1050,75,921,73,1052,77,1086,111,1054,79,1009,112,1088,112,1056,80,1075,114,1058,84,215,120,1093,120,1061,88,1091,121,1059,89,65288,40,65289,41,65292,44,65297,49,65307,59,65311,63],"qps-ploc":[160,32,8211,45,65374,126,8218,44,65306,58,65281,33,8216,96,8245,96,180,96,12494,47,1047,51,1073,54,1072,97,1040,65,1068,98,1042,66,1089,99,1057,67,1077,101,1045,69,1053,72,305,105,1050,75,921,73,1052,77,1086,111,1054,79,1088,112,1056,80,1075,114,1058,84,215,120,1093,120,1061,88,1091,121,1059,89,65288,40,65289,41,65292,44,65297,49,65307,59,65311,63],"ru":[65374,126,8218,44,65306,58,65281,33,8216,96,8245,96,180,96,12494,47,305,105,921,73,1009,112,215,120,65288,40,65289,41,65292,44,65297,49,65307,59,65311,63],"tr":[160,32,8211,45,65374,126,8218,44,65306,58,65281,33,8245,96,180,96,12494,47,1047,51,1073,54,1072,97,1040,65,1068,98,1042,66,1089,99,1057,67,1077,101,1045,69,1053,72,1050,75,921,73,1052,77,1086,111,1054,79,1009,112,1088,112,1056,80,1075,114,1058,84,215,120,1093,120,1061,88,1091,121,1059,89,65288,40,65289,41,65292,44,65297,49,65307,59,65311,63],"zh-hans":[160,32,65374,126,8218,44,8245,96,180,96,12494,47,1047,51,1073,54,1072,97,1040,65,1068,98,1042,66,1089,99,1057,67,1077,101,1045,69,1053,72,305,105,1050,75,921,73,1052,77,1086,111,1054,79,1009,112,1088,112,1056,80,1075,114,1058,84,215,120,1093,120,1061,88,1091,121,1059,89,65297,49],"zh-hant":[8211,45,65374,126,8218,44,180,96,12494,47,1047,51,1073,54,1072,97,1040,65,1068,98,1042,66,1089,99,1057,67,1077,101,1045,69,1053,72,305,105,1050,75,921,73,1052,77,1086,111,1054,79,1009,112,1088,112,1056,80,1075,114,1058,84,215,120,1093,120,1061,88,1091,121,1059,89]}'))}static{this.d=new Ar({getCacheKey:JSON.stringify},t=>{function s(f){const u=new Map;for(let h=0;h<f.length;h+=2)u.set(f[h],f[h+1]);return u}function r(f,u){const h=new Map(f);for(const[d,p]of u)h.set(d,p);return h}function i(f,u){if(!f)return u;const h=new Map;for(const[d,p]of f)u.has(d)&&h.set(d,p);return h}const n=this.c.value;let a=t.filter(f=>!f.startsWith("_")&&f in n);a.length===0&&(a=["_default"]);let o;for(const f of a){const u=s(n[f]);o=i(o,u)}const l=s(n._common),c=r(l,o);return new $t(c)})}static getInstance(t){return $t.d.get(Array.from(t))}static{this.e=new Fe(()=>Object.keys($t.c.value).filter(t=>!t.startsWith("_")))}static getLocales(){return $t.e.value}constructor(t){this.f=t}isAmbiguous(t){return this.f.has(t)}containsAmbiguousCharacter(t){for(let s=0;s<t.length;s++){const r=t.codePointAt(s);if(typeof r=="number"&&this.isAmbiguous(r))return!0}return!1}getPrimaryConfusable(t){return this.f.get(t)}getConfusableCodePoints(){return new Set(this.f.keys())}},To=class Ct{static c(){return JSON.parse('{"_common":[11,12,13,127,847,1564,4447,4448,6068,6069,6155,6156,6157,6158,7355,7356,8192,8193,8194,8195,8196,8197,8198,8199,8200,8201,8202,8204,8205,8206,8207,8234,8235,8236,8237,8238,8239,8287,8288,8289,8290,8291,8292,8293,8294,8295,8296,8297,8298,8299,8300,8301,8302,8303,10240,12644,65024,65025,65026,65027,65028,65029,65030,65031,65032,65033,65034,65035,65036,65037,65038,65039,65279,65440,65520,65521,65522,65523,65524,65525,65526,65527,65528,65532,78844,119155,119156,119157,119158,119159,119160,119161,119162,917504,917505,917506,917507,917508,917509,917510,917511,917512,917513,917514,917515,917516,917517,917518,917519,917520,917521,917522,917523,917524,917525,917526,917527,917528,917529,917530,917531,917532,917533,917534,917535,917536,917537,917538,917539,917540,917541,917542,917543,917544,917545,917546,917547,917548,917549,917550,917551,917552,917553,917554,917555,917556,917557,917558,917559,917560,917561,917562,917563,917564,917565,917566,917567,917568,917569,917570,917571,917572,917573,917574,917575,917576,917577,917578,917579,917580,917581,917582,917583,917584,917585,917586,917587,917588,917589,917590,917591,917592,917593,917594,917595,917596,917597,917598,917599,917600,917601,917602,917603,917604,917605,917606,917607,917608,917609,917610,917611,917612,917613,917614,917615,917616,917617,917618,917619,917620,917621,917622,917623,917624,917625,917626,917627,917628,917629,917630,917631,917760,917761,917762,917763,917764,917765,917766,917767,917768,917769,917770,917771,917772,917773,917774,917775,917776,917777,917778,917779,917780,917781,917782,917783,917784,917785,917786,917787,917788,917789,917790,917791,917792,917793,917794,917795,917796,917797,917798,917799,917800,917801,917802,917803,917804,917805,917806,917807,917808,917809,917810,917811,917812,917813,917814,917815,917816,917817,917818,917819,917820,917821,917822,917823,917824,917825,917826,917827,917828,917829,917830,917831,917832,917833,917834,917835,917836,917837,917838,917839,917840,917841,917842,917843,917844,917845,917846,917847,917848,917849,917850,917851,917852,917853,917854,917855,917856,917857,917858,917859,917860,917861,917862,917863,917864,917865,917866,917867,917868,917869,917870,917871,917872,917873,917874,917875,917876,917877,917878,917879,917880,917881,917882,917883,917884,917885,917886,917887,917888,917889,917890,917891,917892,917893,917894,917895,917896,917897,917898,917899,917900,917901,917902,917903,917904,917905,917906,917907,917908,917909,917910,917911,917912,917913,917914,917915,917916,917917,917918,917919,917920,917921,917922,917923,917924,917925,917926,917927,917928,917929,917930,917931,917932,917933,917934,917935,917936,917937,917938,917939,917940,917941,917942,917943,917944,917945,917946,917947,917948,917949,917950,917951,917952,917953,917954,917955,917956,917957,917958,917959,917960,917961,917962,917963,917964,917965,917966,917967,917968,917969,917970,917971,917972,917973,917974,917975,917976,917977,917978,917979,917980,917981,917982,917983,917984,917985,917986,917987,917988,917989,917990,917991,917992,917993,917994,917995,917996,917997,917998,917999],"cs":[173,8203,12288],"de":[173,8203,12288],"es":[8203,12288],"fr":[173,8203,12288],"it":[160,173,12288],"ja":[173],"ko":[173,12288],"pl":[173,8203,12288],"pt-BR":[173,8203,12288],"qps-ploc":[160,173,8203,12288],"ru":[173,12288],"tr":[160,173,8203,12288],"zh-hans":[160,173,8203,12288],"zh-hant":[173,12288]}')}static{this.d=void 0}static e(){return this.d||(this.d=new Set([...Object.values(Ct.c())].flat())),this.d}static isInvisibleCharacter(t){return Ct.e().has(t)}static containsInvisibleCharacter(t){for(let s=0;s<t.length;s++){const r=t.codePointAt(s);if(typeof r=="number"&&(Ct.isInvisibleCharacter(r)||r===32))return!0}return!1}static get codePoints(){return Ct.e()}}}});function Se(e){return e===47||e===92}function Ir(e){return e.replace(/[\\/]/g,I.sep)}function Io(e){return e.indexOf("/")===-1&&(e=Ir(e)),/^[a-zA-Z]:(\/|$)/.test(e)&&(e="/"+e),e}function jr(e,t=I.sep){if(!e)return"";const s=e.length,r=e.charCodeAt(0);if(Se(r)){if(Se(e.charCodeAt(1))&&!Se(e.charCodeAt(2))){let n=3;const a=n;for(;n<s&&!Se(e.charCodeAt(n));n++);if(a!==n&&!Se(e.charCodeAt(n+1))){for(n+=1;n<s;n++)if(Se(e.charCodeAt(n)))return e.slice(0,n+1).replace(/[\\/]/g,t)}}return t}else if(Mr(r)&&e.charCodeAt(1)===58)return Se(e.charCodeAt(2))?e.slice(0,2)+t:e.slice(0,2);let i=e.indexOf("://");if(i!==-1){for(i+=3;i<s;i++)if(Se(e.charCodeAt(i)))return e.slice(0,i+1)}return""}function jo(e){if(!W||!e||e.length<5)return!1;let t=e.charCodeAt(0);if(t!==92||(t=e.charCodeAt(1),t!==92))return!1;let s=2;const r=s;for(;s<e.length&&(t=e.charCodeAt(s),t!==92);s++);return!(r===s||(t=e.charCodeAt(s+1),isNaN(t)||t===92))}function Fr(e,t,s){const r=e===t;return!s||r?r:!e||!t?!1:Dr(e,t)}function ft(e,t,s,r=oe){if(e===t)return!0;if(!e||!t||t.length>e.length)return!1;if(s){if(!Pr(e,t))return!1;if(t.length===e.length)return!0;let n=t.length;return t.charAt(t.length-1)===r&&n--,e.charAt(n)===r}return t.charAt(t.length-1)!==r&&(t+=r),e.indexOf(t)===0}function Mr(e){return e>=65&&e<=90||e>=97&&e<=122}function Fo(e){const t=Ft(e);return W?e.length>3?!1:Mo(t)&&(e.length===2||t.charCodeAt(2)===92):t===I.sep}function Mo(e,t=W){return t?Mr(e.charCodeAt(0))&&e.charCodeAt(1)===58:!1}function ht(e,t,s=8){let r="";for(let n=0;n<s;n++){let a;n===0&&W&&!t&&(s===3||s===4)?a=Vr:a=Ur,r+=a.charAt(Math.floor(Math.random()*a.length))}let i;return t?i=`${t}-${r}`:i=r,e?te(e,i):i}var Ur,Vr,Ue=O({"out-build/vs/base/common/extpath.js"(){"use strict";ae(),J(),Me(),d1(),Ur="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789",Vr="BDEFGHIJKMOQRSTUVWXYZbdefghijkmoqrstuvwxyz0123456789"}});function Uo(e,t){if(!e.scheme&&t)throw new Error(`[UriError]: Scheme is missing: {scheme: "", authority: "${e.authority}", path: "${e.path}", query: "${e.query}", fragment: "${e.fragment}"}`);if(e.scheme&&!Br.test(e.scheme))throw new Error("[UriError]: Scheme contains illegal characters.");if(e.path){if(e.authority){if(!zr.test(e.path))throw new Error('[UriError]: If a URI contains an authority component, then the path component must either be empty or begin with a slash ("/") character')}else if(Hr.test(e.path))throw new Error('[UriError]: If a URI does not contain an authority component, then the path cannot begin with two slash characters ("//")')}}function Vo(e,t){return!e&&!t?"file":e}function qo(e,t){switch(e){case"https":case"http":case"file":t?t[0]!==le&&(t=le+t):t=le;break}return t}function qr(e,t,s){let r,i=-1;for(let n=0;n<e.length;n++){const a=e.charCodeAt(n);if(a>=97&&a<=122||a>=65&&a<=90||a>=48&&a<=57||a===45||a===46||a===95||a===126||t&&a===47||s&&a===91||s&&a===93||s&&a===58)i!==-1&&(r+=encodeURIComponent(e.substring(i,n)),i=-1),r!==void 0&&(r+=e.charAt(n));else{r===void 0&&(r=e.substr(0,n));const o=N1[a];o!==void 0?(i!==-1&&(r+=encodeURIComponent(e.substring(i,n)),i=-1),r+=o):i===-1&&(i=n)}}return i!==-1&&(r+=encodeURIComponent(e.substring(i))),r!==void 0?r:e}function Wo(e){let t;for(let s=0;s<e.length;s++){const r=e.charCodeAt(s);r===35||r===63?(t===void 0&&(t=e.substr(0,s)),t+=N1[r]):t!==void 0&&(t+=e[s])}return t!==void 0?t:e}function qt(e,t){let s;return e.authority&&e.path.length>1&&e.scheme==="file"?s=`//${e.authority}${e.path}`:e.path.charCodeAt(0)===47&&(e.path.charCodeAt(1)>=65&&e.path.charCodeAt(1)<=90||e.path.charCodeAt(1)>=97&&e.path.charCodeAt(1)<=122)&&e.path.charCodeAt(2)===58?t?s=e.path.substr(1):s=e.path[1].toLowerCase()+e.path.substr(2):s=e.path,W&&(s=s.replace(/\//g,"\\")),s}function L1(e,t){const s=t?Wo:qr;let r="",{scheme:i,authority:n,path:a,query:o,fragment:l}=e;if(i&&(r+=i,r+=":"),(n||i==="file")&&(r+=le,r+=le),n){let c=n.indexOf("@");if(c!==-1){const f=n.substr(0,c);n=n.substr(c+1),c=f.lastIndexOf(":"),c===-1?r+=s(f,!1,!1):(r+=s(f.substr(0,c),!1,!1),r+=":",r+=s(f.substr(c+1),!1,!0)),r+="@"}n=n.toLowerCase(),c=n.lastIndexOf(":"),c===-1?r+=s(n,!1,!0):(r+=s(n.substr(0,c),!1,!0),r+=n.substr(c))}if(a){if(a.length>=3&&a.charCodeAt(0)===47&&a.charCodeAt(2)===58){const c=a.charCodeAt(1);c>=65&&c<=90&&(a=`/${String.fromCharCode(c+32)}:${a.substr(3)}`)}else if(a.length>=2&&a.charCodeAt(1)===58){const c=a.charCodeAt(0);c>=65&&c<=90&&(a=`${String.fromCharCode(c+32)}:${a.substr(2)}`)}r+=s(a,!0,!1)}return o&&(r+="?",r+=s(o,!1,!1)),l&&(r+="#",r+=t?l:qr(l,!1,!1)),r}function Wr(e){try{return decodeURIComponent(e)}catch{return e.length>3?e.substr(0,3)+Wr(e.substr(3)):e}}function Wt(e){return e.match(R1)?e.replace(R1,t=>Wr(t)):e}var Br,zr,Hr,M,le,Gr,K,k1,Ve,N1,R1,qe=O({"out-build/vs/base/common/uri.js"(){"use strict";ae(),J(),Br=/^\w[\w\d+.-]*$/,zr=/^\//,Hr=/^\/\//,M="",le="/",Gr=/^(([^:/?#]+?):)?(\/\/([^/?#]*))?([^?#]*)(\?([^#]*))?(#(.*))?/,K=class t1{static isUri(t){return t instanceof t1?!0:t?typeof t.authority=="string"&&typeof t.fragment=="string"&&typeof t.path=="string"&&typeof t.query=="string"&&typeof t.scheme=="string"&&typeof t.fsPath=="string"&&typeof t.with=="function"&&typeof t.toString=="function":!1}constructor(t,s,r,i,n,a=!1){typeof t=="object"?(this.scheme=t.scheme||M,this.authority=t.authority||M,this.path=t.path||M,this.query=t.query||M,this.fragment=t.fragment||M):(this.scheme=Vo(t,a),this.authority=s||M,this.path=qo(this.scheme,r||M),this.query=i||M,this.fragment=n||M,Uo(this,a))}get fsPath(){return qt(this,!1)}with(t){if(!t)return this;let{scheme:s,authority:r,path:i,query:n,fragment:a}=t;return s===void 0?s=this.scheme:s===null&&(s=M),r===void 0?r=this.authority:r===null&&(r=M),i===void 0?i=this.path:i===null&&(i=M),n===void 0?n=this.query:n===null&&(n=M),a===void 0?a=this.fragment:a===null&&(a=M),s===this.scheme&&r===this.authority&&i===this.path&&n===this.query&&a===this.fragment?this:new Ve(s,r,i,n,a)}static parse(t,s=!1){const r=Gr.exec(t);return r?new Ve(r[2]||M,Wt(r[4]||M),Wt(r[5]||M),Wt(r[7]||M),Wt(r[9]||M),s):new Ve(M,M,M,M,M)}static file(t){let s=M;if(W&&(t=t.replace(/\\/g,le)),t[0]===le&&t[1]===le){const r=t.indexOf(le,2);r===-1?(s=t.substring(2),t=le):(s=t.substring(2,r),t=t.substring(r)||le)}return new Ve("file",s,t,M,M)}static from(t,s){return new Ve(t.scheme,t.authority,t.path,t.query,t.fragment,s)}static joinPath(t,...s){if(!t.path)throw new Error("[UriError]: cannot call joinPath on URI without path");let r;return W&&t.scheme==="file"?r=t1.file(q.join(qt(t,!0),...s)).path:r=I.join(t.path,...s),t.with({path:r})}toString(t=!1){return L1(this,t)}toJSON(){return this}static revive(t){if(t){if(t instanceof t1)return t;{const s=new Ve(t);return s._formatted=t.external??null,s._fsPath=t._sep===k1?t.fsPath??null:null,s}}else return t}[Symbol.for("debug.description")](){return`URI(${this.toString()})`}},k1=W?1:void 0,Ve=class extends K{constructor(){super(...arguments),this._formatted=null,this._fsPath=null}get fsPath(){return this._fsPath||(this._fsPath=qt(this,!1)),this._fsPath}toString(e=!1){return e?L1(this,!0):(this._formatted||(this._formatted=L1(this,!1)),this._formatted)}toJSON(){const e={$mid:1};return this._fsPath&&(e.fsPath=this._fsPath,e._sep=k1),this._formatted&&(e.external=this._formatted),this.path&&(e.path=this.path),this.scheme&&(e.scheme=this.scheme),this.authority&&(e.authority=this.authority),this.query&&(e.query=this.query),this.fragment&&(e.fragment=this.fragment),e}},N1={58:"%3A",47:"%2F",63:"%3F",35:"%23",91:"%5B",93:"%5D",64:"%40",33:"%21",36:"%24",38:"%26",39:"%27",40:"%28",41:"%29",42:"%2A",43:"%2B",44:"%2C",59:"%3B",61:"%3D",32:"%20"},R1=/(%[0-9A-Za-z][0-9A-Za-z])+/g}});function Bo(e){return`${e.quality??"oss"}-${e.commit??"dev"}`}var H,Kr,Jr,Qr,Yr,Zr,Xr,zo,Ho,ei,T1=O({"out-build/vs/base/common/network.js"(){"use strict";Xe(),J(),Me(),qe(),ae(),function(e){e.inMemory="inmemory",e.vscode="vscode",e.internal="private",e.walkThrough="walkThrough",e.walkThroughSnippet="walkThroughSnippet",e.http="http",e.https="https",e.file="file",e.mailto="mailto",e.untitled="untitled",e.data="data",e.command="command",e.vscodeRemote="vscode-remote",e.vscodeRemoteResource="vscode-remote-resource",e.vscodeManagedRemoteResource="vscode-managed-remote-resource",e.vscodeUserData="vscode-userdata",e.vscodeCustomEditor="vscode-custom-editor",e.vscodeNotebookCell="vscode-notebook-cell",e.vscodeNotebookCellMetadata="vscode-notebook-cell-metadata",e.vscodeNotebookCellMetadataDiff="vscode-notebook-cell-metadata-diff",e.vscodeNotebookCellOutput="vscode-notebook-cell-output",e.vscodeNotebookCellOutputDiff="vscode-notebook-cell-output-diff",e.vscodeNotebookMetadata="vscode-notebook-metadata",e.vscodeInteractiveInput="vscode-interactive-input",e.vscodeSettings="vscode-settings",e.vscodeWorkspaceTrust="vscode-workspace-trust",e.vscodeTerminal="vscode-terminal",e.vscodeChatCodeBlock="vscode-chat-code-block",e.vscodeChatCodeCompareBlock="vscode-chat-code-compare-block",e.vscodeChatSesssion="vscode-chat-editor",e.webviewPanel="webview-panel",e.vscodeWebview="vscode-webview",e.extension="extension",e.vscodeFileResource="vscode-file",e.tmp="tmp",e.vsls="vsls",e.vscodeSourceControl="vscode-scm",e.commentsInput="comment",e.codeSetting="code-setting",e.outputChannel="output",e.accessibleView="accessible-view"}(H||(H={})),Kr="tkn",Jr=class{constructor(){this.a=Object.create(null),this.b=Object.create(null),this.c=Object.create(null),this.d="http",this.e=null,this.f="/"}setPreferredWebSchema(e){this.d=e}setDelegate(e){this.e=e}setServerRootPath(e,t){this.f=I.join(t??"/",Bo(e))}getServerRootPath(){return this.f}get g(){return I.join(this.f,H.vscodeRemoteResource)}set(e,t,s){this.a[e]=t,this.b[e]=s}setConnectionToken(e,t){this.c[e]=t}getPreferredWebSchema(){return this.d}rewrite(e){if(this.e)try{return this.e(e)}catch(a){return xt(a),e}const t=e.authority;let s=this.a[t];s&&s.indexOf(":")!==-1&&s.indexOf("[")===-1&&(s=`[${s}]`);const r=this.b[t],i=this.c[t];let n=`path=${encodeURIComponent(e.path)}`;return typeof i=="string"&&(n+=`&${Kr}=${encodeURIComponent(i)}`),K.from({scheme:ar?this.d:H.vscodeRemoteResource,authority:`${s}:${r}`,path:this.g,query:n})}},Qr=new Jr,Yr="vscode-app",Zr=class s1{static{this.a=Yr}asBrowserUri(t){const s=this.b(t);return this.uriToBrowserUri(s)}uriToBrowserUri(t){return t.scheme===H.vscodeRemote?Qr.rewrite(t):t.scheme===H.file&&(or||cr===`${H.vscodeFileResource}://${s1.a}`)?t.with({scheme:H.vscodeFileResource,authority:t.authority||s1.a,query:null,fragment:null}):t}asFileUri(t){const s=this.b(t);return this.uriToFileUri(s)}uriToFileUri(t){return t.scheme===H.vscodeFileResource?t.with({scheme:H.file,authority:t.authority!==s1.a?t.authority:null,query:null,fragment:null}):t}b(t){if(K.isUri(t))return t;if(globalThis._VSCODE_FILE_ROOT){const s=globalThis._VSCODE_FILE_ROOT;if(/^\w[\w\d+.-]*:\/\//.test(s))return K.joinPath(K.parse(s,!0),t);const r=te(s,t);return K.file(r)}throw new Error("Cannot determine URI for module id!")}},Xr=new Zr,zo=Object.freeze({"Cache-Control":"no-cache, no-store"}),Ho=Object.freeze({"Document-Policy":"include-js-call-stacks-in-crash-reports"}),function(e){const t=new Map([["1",{"Cross-Origin-Opener-Policy":"same-origin"}],["2",{"Cross-Origin-Embedder-Policy":"require-corp"}],["3",{"Cross-Origin-Opener-Policy":"same-origin","Cross-Origin-Embedder-Policy":"require-corp"}]]);e.CoopAndCoep=Object.freeze(t.get("3"));const s="vscode-coi";function r(n){let a;typeof n=="string"?a=new URL(n).searchParams:n instanceof URL?a=n.searchParams:K.isUri(n)&&(a=new URL(n.toString(!0)).searchParams);const o=a?.get(s);if(o)return t.get(o)}e.getHeadersFromQuery=r;function i(n,a,o){if(!globalThis.crossOriginIsolated)return;const l=a&&o?"3":o?"2":"1";n instanceof URLSearchParams?n.set(s,l):n[s]=l}e.addSearchParam=i}(ei||(ei={}))}});function Ce(e){return qt(e,!0)}var Bt,T,ti,Go,Ko,Jo,Qo,Yo,si,Zo,Xo,I1,e2,t2,s2,r2,j1,F1,i2,n2,ri,M1=O({"out-build/vs/base/common/resources.js"(){"use strict";Ue(),T1(),ae(),J(),Me(),qe(),Bt=class{constructor(e){this.a=e}compare(e,t,s=!1){return e===t?0:D1(this.getComparisonKey(e,s),this.getComparisonKey(t,s))}isEqual(e,t,s=!1){return e===t?!0:!e||!t?!1:this.getComparisonKey(e,s)===this.getComparisonKey(t,s)}getComparisonKey(e,t=!1){return e.with({path:this.a(e)?e.path.toLowerCase():void 0,fragment:t?null:void 0}).toString()}ignorePathCasing(e){return this.a(e)}isEqualOrParent(e,t,s=!1){if(e.scheme===t.scheme){if(e.scheme===H.file)return ft(Ce(e),Ce(t),this.a(e))&&e.query===t.query&&(s||e.fragment===t.fragment);if(j1(e.authority,t.authority))return ft(e.path,t.path,this.a(e),"/")&&e.query===t.query&&(s||e.fragment===t.fragment)}return!1}joinPath(e,...t){return K.joinPath(e,...t)}basenameOrAuthority(e){return si(e)||e.authority}basename(e){return I.basename(e.path)}extname(e){return I.extname(e.path)}dirname(e){if(e.path.length===0)return e;let t;return e.scheme===H.file?t=K.file(je(Ce(e))).path:(t=I.dirname(e.path),e.authority&&t.length&&t.charCodeAt(0)!==47&&(console.error(`dirname("${e.toString})) resulted in a relative path`),t="/")),e.with({path:t})}normalizePath(e){if(!e.path.length)return e;let t;return e.scheme===H.file?t=K.file(Ft(Ce(e))).path:t=I.normalize(e.path),e.with({path:t})}relativePath(e,t){if(e.scheme!==t.scheme||!j1(e.authority,t.authority))return;if(e.scheme===H.file){const i=$r(Ce(e),Ce(t));return W?Ir(i):i}let s=e.path||"/";const r=t.path||"/";if(this.a(e)){let i=0;for(const n=Math.min(s.length,r.length);i<n&&!(s.charCodeAt(i)!==r.charCodeAt(i)&&s.charAt(i).toLowerCase()!==r.charAt(i).toLowerCase());i++);s=r.substr(0,i)+s.substr(i)}return I.relative(s,r)}resolvePath(e,t){if(e.scheme===H.file){const s=K.file(S1(Ce(e),t));return e.with({authority:s.authority,path:s.path})}return t=Io(t),e.with({path:I.resolve(e.path,t)})}isAbsolutePath(e){return!!e.path&&e.path[0]==="/"}isEqualAuthority(e,t){return e===t||e!==void 0&&t!==void 0&&Dr(e,t)}hasTrailingPathSeparator(e,t=oe){if(e.scheme===H.file){const s=Ce(e);return s.length>jr(s).length&&s[s.length-1]===t}else{const s=e.path;return s.length>1&&s.charCodeAt(s.length-1)===47&&!/^[a-zA-Z]:(\/$|\\$)/.test(e.fsPath)}}removeTrailingPathSeparator(e,t=oe){return F1(e,t)?e.with({path:e.path.substr(0,e.path.length-1)}):e}addTrailingPathSeparator(e,t=oe){let s=!1;if(e.scheme===H.file){const r=Ce(e);s=r!==void 0&&r.length===jr(r).length&&r[r.length-1]===t}else{t="/";const r=e.path;s=r.length===1&&r.charCodeAt(r.length-1)===47}return!s&&!F1(e,t)?e.with({path:e.path+"/"}):e}},T=new Bt(()=>!1),ti=new Bt(e=>e.scheme===H.file?!Ee:!0),Go=new Bt(e=>!0),Ko=T.isEqual.bind(T),Jo=T.isEqualOrParent.bind(T),Qo=T.getComparisonKey.bind(T),Yo=T.basenameOrAuthority.bind(T),si=T.basename.bind(T),Zo=T.extname.bind(T),Xo=T.dirname.bind(T),I1=T.joinPath.bind(T),e2=T.normalizePath.bind(T),t2=T.relativePath.bind(T),s2=T.resolvePath.bind(T),r2=T.isAbsolutePath.bind(T),j1=T.isEqualAuthority.bind(T),F1=T.hasTrailingPathSeparator.bind(T),i2=T.removeTrailingPathSeparator.bind(T),n2=T.addTrailingPathSeparator.bind(T),function(e){e.META_DATA_LABEL="label",e.META_DATA_DESCRIPTION="description",e.META_DATA_SIZE="size",e.META_DATA_MIME="mime";function t(s){const r=new Map;s.path.substring(s.path.indexOf(";")+1,s.path.lastIndexOf(";")).split(";").forEach(a=>{const[o,l]=a.split(":");o&&l&&r.set(o,l)});const n=s.path.substring(0,s.path.indexOf(";"));return n&&r.set(e.META_DATA_MIME,n),r}e.parseMetaData=t}(ri||(ri={}))}}),o2,a2=O({"out-build/vs/base/common/symbols.js"(){"use strict";o2=Symbol("MicrotaskDelay")}});function U1(e){return!!e&&typeof e.then=="function"}function l2(e){const t=new Ge,s=e(t.token),r=new Promise((i,n)=>{const a=t.token.onCancellationRequested(()=>{a.dispose(),n(new Ze)});Promise.resolve(s).then(o=>{a.dispose(),t.dispose(),i(o)},o=>{a.dispose(),t.dispose(),n(o)})});return new class{cancel(){t.cancel(),t.dispose()}then(i,n){return r.then(i,n)}catch(i){return this.then(void 0,i)}finally(i){return r.finally(i)}}}function ii(e,t){return t?new Promise((s,r)=>{const i=setTimeout(()=>{n.dispose(),s()},e),n=t.onCancellationRequested(()=>{clearTimeout(i),n.dispose(),r(new Ze)})}):l2(s=>ii(e,s))}var ni,V1,oi,q1,ai,li,c2,W1,ci,B1,ui,fi,u2,dt=O({"out-build/vs/base/common/async.js"(){"use strict";Ut(),Xe(),y1(),Lt(),M1(),J(),a2(),Vt(),ni=class{constructor(e){this.a=0,this.b=!1,this.f=e,this.g=[],this.d=0,this.h=new fe}whenIdle(){return this.size>0?ne.toPromise(this.onDrained):Promise.resolve()}get onDrained(){return this.h.event}get size(){return this.a}queue(e){if(this.b)throw new Error("Object has been disposed");return this.a++,new Promise((t,s)=>{this.g.push({factory:e,c:t,e:s}),this.j()})}j(){for(;this.g.length&&this.d<this.f;){const e=this.g.shift();this.d++;const t=e.factory();t.then(e.c,e.e),t.then(()=>this.k(),()=>this.k())}}k(){this.b||(this.d--,--this.a===0&&this.h.fire(),this.g.length>0&&this.j())}clear(){if(this.b)throw new Error("Object has been disposed");this.g.length=0,this.a=this.d}dispose(){this.b=!0,this.g.length=0,this.a=0,this.h.dispose()}},V1=class extends ni{constructor(){super(1)}},oi=class{constructor(){this.a=new Map,this.b=new Set,this.d=void 0,this.f=0}async whenDrained(){if(this.g())return;const e=new B1;return this.b.add(e),e.p}g(){for(const[,e]of this.a)if(e.size>0)return!1;return!0}queueSize(e,t=T){const s=t.getComparisonKey(e);return this.a.get(s)?.size??0}queueFor(e,t,s=T){const r=s.getComparisonKey(e);let i=this.a.get(r);if(!i){i=new V1;const n=this.f++,a=ne.once(i.onDrained)(()=>{i?.dispose(),this.a.delete(r),this.h(),this.d?.deleteAndDispose(n),this.d?.size===0&&(this.d.dispose(),this.d=void 0)});this.d||(this.d=new Is),this.d.set(n,a),this.a.set(r,i)}return i.queue(t)}h(){this.g()&&this.j()}j(){for(const e of this.b)e.complete();this.b.clear()}dispose(){for(const[,e]of this.a)e.dispose();this.a.clear(),this.j(),this.d?.dispose()}},q1=class{constructor(e,t){this.b=-1,this.a=e,this.d=t,this.f=this.g.bind(this)}dispose(){this.cancel(),this.a=null}cancel(){this.isScheduled()&&(clearTimeout(this.b),this.b=-1)}schedule(e=this.d){this.cancel(),this.b=setTimeout(this.f,e)}get delay(){return this.d}set delay(e){this.d=e}isScheduled(){return this.b!==-1}flush(){this.isScheduled()&&(this.cancel(),this.h())}g(){this.b=-1,this.a&&this.h()}h(){this.a?.()}},ai=class extends q1{constructor(e,t){super(e,t),this.j=[]}work(e){this.j.push(e),this.isScheduled()||this.schedule()}h(){const e=this.j;this.j=[],this.a?.(e)}dispose(){this.j=[],super.dispose()}},li=class extends ve{constructor(e,t){super(),this.h=e,this.j=t,this.a=[],this.b=this.B(new m1),this.f=!1,this.g=0}get pending(){return this.a.length}work(e){if(this.f)return!1;if(typeof this.h.maxBufferedWork=="number"){if(this.b.value){if(this.pending+e.length>this.h.maxBufferedWork)return!1}else if(this.pending+e.length-this.h.maxWorkChunkSize>this.h.maxBufferedWork)return!1}for(const s of e)this.a.push(s);const t=Date.now()-this.g;return!this.b.value&&(!this.h.waitThrottleDelayBetweenWorkUnits||t>=this.h.throttleDelay)?this.m():!this.b.value&&this.h.waitThrottleDelayBetweenWorkUnits&&this.r(Math.max(this.h.throttleDelay-t,0)),!0}m(){this.g=Date.now(),this.j(this.a.splice(0,this.h.maxWorkChunkSize)),this.a.length>0&&this.r()}r(e=this.h.throttleDelay){this.b.value=new q1(()=>{this.b.clear(),this.m()},e),this.b.value.schedule()}dispose(){super.dispose(),this.f=!0}},function(){typeof globalThis.requestIdleCallback!="function"||typeof globalThis.cancelIdleCallback!="function"?W1=(e,t,s)=>{hr(()=>{if(r)return;const i=Date.now()+15;t(Object.freeze({didTimeout:!0,timeRemaining(){return Math.max(0,i-Date.now())}}))});let r=!1;return{dispose(){r||(r=!0)}}}:W1=(e,t,s)=>{const r=e.requestIdleCallback(t,typeof s=="number"?{timeout:s}:void 0);let i=!1;return{dispose(){i||(i=!0,e.cancelIdleCallback(r))}}},c2=(e,t)=>W1(globalThis,e,t)}(),function(e){e[e.Resolved=0]="Resolved",e[e.Rejected=1]="Rejected"}(ci||(ci={})),B1=class{get isRejected(){return this.d?.outcome===1}get isResolved(){return this.d?.outcome===0}get isSettled(){return!!this.d}get value(){return this.d?.outcome===0?this.d?.value:void 0}constructor(){this.p=new Promise((e,t)=>{this.a=e,this.b=t})}complete(e){return new Promise(t=>{this.a(e),this.d={outcome:0,value:e},t()})}error(e){return new Promise(t=>{this.b(e),this.d={outcome:1,value:e},t()})}cancel(){return this.error(new Ze)}},function(e){async function t(r){let i;const n=await Promise.all(r.map(a=>a.then(o=>o,o=>{i||(i=o)})));if(typeof i<"u")throw i;return n}e.settled=t;function s(r){return new Promise(async(i,n)=>{try{await r(i,n)}catch(a){n(a)}})}e.withAsyncBody=s}(ui||(ui={})),function(e){e[e.Initial=0]="Initial",e[e.DoneOK=1]="DoneOK",e[e.DoneError=2]="DoneError"}(fi||(fi={})),u2=class ie{static fromArray(t){return new ie(s=>{s.emitMany(t)})}static fromPromise(t){return new ie(async s=>{s.emitMany(await t)})}static fromPromisesResolveOrder(t){return new ie(async s=>{await Promise.all(t.map(async r=>s.emitOne(await r)))})}static merge(t){return new ie(async s=>{await Promise.all(t.map(async r=>{for await(const i of r)s.emitOne(i)}))})}static{this.EMPTY=ie.fromArray([])}constructor(t,s){this.a=0,this.b=[],this.d=null,this.f=s,this.g=new fe,queueMicrotask(async()=>{const r={emitOne:i=>this.h(i),emitMany:i=>this.j(i),reject:i=>this.l(i)};try{await Promise.resolve(t(r)),this.k()}catch(i){this.l(i)}finally{r.emitOne=void 0,r.emitMany=void 0,r.reject=void 0}})}[Symbol.asyncIterator](){let t=0;return{next:async()=>{do{if(this.a===2)throw this.d;if(t<this.b.length)return{done:!1,value:this.b[t++]};if(this.a===1)return{done:!0,value:void 0};await ne.toPromise(this.g.event)}while(!0)},return:async()=>(this.f?.(),{done:!0,value:void 0})}}static map(t,s){return new ie(async r=>{for await(const i of t)r.emitOne(s(i))})}map(t){return ie.map(this,t)}static filter(t,s){return new ie(async r=>{for await(const i of t)s(i)&&r.emitOne(i)})}filter(t){return ie.filter(this,t)}static coalesce(t){return ie.filter(t,s=>!!s)}coalesce(){return ie.coalesce(this)}static async toPromise(t){const s=[];for await(const r of t)s.push(r);return s}toPromise(){return ie.toPromise(this)}h(t){this.a===0&&(this.b.push(t),this.g.fire())}j(t){this.a===0&&(this.b=this.b.concat(t),this.g.fire())}k(){this.a===0&&(this.a=1,this.g.fire())}l(t){this.a===0&&(this.a=2,this.d=t,this.g.fire())}}}});function z1(e){return f2(e,"NFC",hi)}function f2(e,t,s){if(!e)return e;const r=s.get(e);if(r)return r;let i;return di.test(e)?i=e.normalize(t):i=e,s.set(e,i),i}var hi,h2,di,pi=O({"out-build/vs/base/common/normalization.js"(){"use strict";h1(),hi=new Pt(1e4),h2=new Pt(1e4),di=/[^\u0000-\u0080]/}});import*as R from"fs";import{tmpdir as d2}from"os";import{promisify as pt}from"util";async function gi(e,t=gt.UNLINK,s){if(Fo(e))throw new Error("rimraf - will refuse to recursively delete root");return t===gt.UNLINK?H1(e):p2(e,s)}async function p2(e,t=ht(d2())){try{try{await R.promises.rename(e,t)}catch(s){return s.code==="ENOENT"?void 0:H1(e)}H1(t).catch(s=>{})}catch(s){if(s.code!=="ENOENT")throw s}}async function H1(e){return R.promises.rm(e,{recursive:!0,force:!0,maxRetries:3})}async function zt(e,t){return m2(await(t?g2(e):R.promises.readdir(e)))}async function g2(e){try{return await R.promises.readdir(e,{withFileTypes:!0})}catch(r){console.warn("[node.js fs] readdir with filetypes failed with error: ",r)}const t=[],s=await zt(e);for(const r of s){let i=!1,n=!1,a=!1;try{const o=await R.promises.lstat(te(e,r));i=o.isFile(),n=o.isDirectory(),a=o.isSymbolicLink()}catch(o){console.warn("[node.js fs] unexpected error from lstat after readdir: ",o)}t.push({name:r,isFile:()=>i,isDirectory:()=>n,isSymbolicLink:()=>a})}return t}function m2(e){return e.map(t=>typeof t=="string"?Te?z1(t):t:(t.name=Te?z1(t.name):t.name,t))}async function w2(e){const t=await zt(e),s=[];for(const r of t)await mt.existsDirectory(te(e,r))&&s.push(r);return s}function mi(e,t=1e3){return new Promise(s=>{let r=!1;const i=setInterval(()=>{r||(r=!0,R.access(e,n=>{r=!1,n&&(clearInterval(i),s(void 0))}))},t)})}function b2(e,t,s){return _i.queueFor(K.file(e),()=>{const r=bi(s);return new Promise((i,n)=>y2(e,t,r,a=>a?n(a):i()))},ti)}function wi(e){Ht=e}function y2(e,t,s,r){if(!Ht)return R.writeFile(e,t,{mode:s.mode,flag:s.flag},r);R.open(e,s.flag,s.mode,(i,n)=>{if(i)return r(i);R.writeFile(n,t,a=>{if(a)return R.close(n,()=>r(a));R.fdatasync(n,o=>(o&&(console.warn("[node.js fs] fdatasync is now disabled for this session because it failed: ",o),wi(!1)),R.close(n,l=>r(l))))})})}function Ke(e,t,s){const r=bi(s);if(!Ht)return R.writeFileSync(e,t,{mode:r.mode,flag:r.flag});const i=R.openSync(e,r.flag,r.mode);try{R.writeFileSync(i,t);try{R.fdatasyncSync(i)}catch(n){console.warn("[node.js fs] fdatasyncSync is now disabled for this session because it failed: ",n),wi(!1)}}finally{R.closeSync(i)}}function bi(e){return e?{mode:typeof e.mode=="number"?e.mode:438,flag:typeof e.flag=="string"?e.flag:"w"}:{mode:438,flag:"w"}}async function v2(e,t,s=6e4){if(e!==t)try{W&&typeof s=="number"?await yi(e,t,Date.now(),s):await R.promises.rename(e,t)}catch(r){if(e.toLowerCase()!==t.toLowerCase()&&r.code==="EXDEV"||e.endsWith("."))await vi(e,t,{preserveSymlinks:!1}),await gi(e,gt.MOVE);else throw r}}async function yi(e,t,s,r,i=0){try{return await R.promises.rename(e,t)}catch(n){if(n.code!=="EACCES"&&n.code!=="EPERM"&&n.code!=="EBUSY")throw n;if(Date.now()-s>=r)throw console.error(`[node.js fs] rename failed after ${i} retries with error: ${n}`),n;if(i===0){let a=!1;try{const{stat:o}=await mt.stat(t);o.isFile()||(a=!0)}catch{}if(a)throw n}return await ii(Math.min(100,i*10)),yi(e,t,s,r,i+1)}}async function vi(e,t,s){return Ei(e,t,{root:{source:e,target:t},options:s,handledSourcePaths:new Set})}async function Ei(e,t,s){if(s.handledSourcePaths.has(e))return;s.handledSourcePaths.add(e);const{stat:r,symbolicLink:i}=await mt.stat(e);if(i){if(s.options.preserveSymlinks)try{return await $2(e,t,s)}catch{}if(i.dangling)return}return r.isDirectory()?E2(e,t,r.mode&G1,s):_2(e,t,r.mode&G1)}async function E2(e,t,s,r){await R.promises.mkdir(t,{recursive:!0,mode:s});const i=await zt(e);for(const n of i)await Ei(te(e,n),te(t,n),r)}async function _2(e,t,s){await R.promises.copyFile(e,t),await R.promises.chmod(t,s)}async function $2(e,t,s){let r=await R.promises.readlink(e);ft(r,s.root.source,!Ee)&&(r=te(s.root.target,r.substr(s.root.source.length+1))),await R.promises.symlink(r,t)}var gt,mt,_i,Ht,G1,we,K1=O({"out-build/vs/base/node/pfs.js"(){"use strict";dt(),Ue(),pi(),ae(),J(),M1(),qe(),function(e){e[e.UNLINK=0]="UNLINK",e[e.MOVE=1]="MOVE"}(gt||(gt={})),function(e){async function t(i){let n;try{if(n=await R.promises.lstat(i),!n.isSymbolicLink())return{stat:n}}catch{}try{return{stat:await R.promises.stat(i),symbolicLink:n?.isSymbolicLink()?{dangling:!1}:void 0}}catch(a){if(a.code==="ENOENT"&&n)return{stat:n,symbolicLink:{dangling:!0}};if(W&&a.code==="EACCES")try{return{stat:await R.promises.stat(await R.promises.readlink(i)),symbolicLink:{dangling:!1}}}catch(o){if(o.code==="ENOENT"&&n)return{stat:n,symbolicLink:{dangling:!0}};throw o}throw a}}e.stat=t;async function s(i){try{const{stat:n,symbolicLink:a}=await e.stat(i);return n.isFile()&&a?.dangling!==!0}catch{}return!1}e.existsFile=s;async function r(i){try{const{stat:n,symbolicLink:a}=await e.stat(i);return n.isDirectory()&&a?.dangling!==!0}catch{}return!1}e.existsDirectory=r}(mt||(mt={})),_i=new oi,Ht=!0,G1=511,we=new class{get read(){return(e,t,s,r,i)=>new Promise((n,a)=>{R.read(e,t,s,r,i,(o,l,c)=>o?a(o):n({bytesRead:l,buffer:c}))})}get write(){return(e,t,s,r,i)=>new Promise((n,a)=>{R.write(e,t,s,r,i,(o,l,c)=>o?a(o):n({bytesWritten:l,buffer:c}))})}get fdatasync(){return pt(R.fdatasync)}get open(){return pt(R.open)}get close(){return pt(R.close)}get realpath(){return pt(R.realpath)}get ftruncate(){return pt(R.ftruncate)}async exists(e){try{return await R.promises.access(e),!0}catch{return!1}}get readdir(){return zt}get readDirsInDir(){return w2}get writeFile(){return b2}get rm(){return gi}get rename(){return v2}get copy(){return vi}}}});import*as C2 from"net";function J1(e,t,s,r=1){let i=!1;return new Promise(n=>{const a=setTimeout(()=>{if(!i)return i=!0,n(0)},s);Q1(e,t,r,o=>{if(!i)return i=!0,clearTimeout(a),n(o)})})}function Q1(e,t,s,r){if(t===0)return r(0);const i=new C2.Socket;i.once("connect",()=>($i(i),Q1(e+s,t-1,s,r))),i.once("data",()=>{}),i.once("error",n=>($i(i),n.code!=="ECONNREFUSED"?Q1(e+s,t-1,s,r):r(e))),i.connect(e,"127.0.0.1")}function $i(e){try{e.removeAllListeners("connect"),e.removeAllListeners("error"),e.end(),e.destroy(),e.unref()}catch(t){console.error(t)}}var A2=O({"out-build/vs/base/node/ports.js"(){"use strict"}});import*as Ci from"fs";async function O2(e){try{return await we.realpath(e)}catch{const s=S2(e);return await Ci.promises.access(s,Ci.constants.R_OK),s}}function S2(e){return Do(Ft(e),oe)}var x2=O({"out-build/vs/base/node/extpath.js"(){"use strict";ae(),J(),Me(),K1()}});function Ai(e,t){switch(e){case 0:return"";case 1:return`${bt}*?`;default:return`(?:${wt}|${bt}+${wt}${t?`|${wt}${bt}+`:""})*?`}}function Oi(e,t){if(!e)return[];const s=[];let r=!1,i=!1,n="";for(const a of e){switch(a){case t:if(!r&&!i){s.push(n),n="";continue}break;case"{":r=!0;break;case"}":r=!1;break;case"[":i=!0;break;case"]":i=!1;break}n+=a}return n&&s.push(n),s}function Si(e){if(!e)return"";let t="";const s=Oi(e,X1);if(s.every(r=>r===Je))t=".*";else{let r=!1;s.forEach((i,n)=>{if(i===Je){if(r)return;t+=Ai(2,n===s.length-1)}else{let a=!1,o="",l=!1,c="";for(const f of i){if(f!=="}"&&a){o+=f;continue}if(l&&(f!=="]"||!c)){let u;f==="-"?u=f:(f==="^"||f==="!")&&!c?u="^":f===X1?u="":u=Or(f),c+=u;continue}switch(f){case"{":a=!0;continue;case"[":l=!0;continue;case"}":{const h=`(?:${Oi(o,",").map(d=>Si(d)).join("|")})`;t+=h,a=!1,o="";break}case"]":{t+="["+c+"]",l=!1,c="";break}case"?":t+=bt;continue;case"*":t+=Ai(1);continue;default:t+=Or(f)}}n<s.length-1&&(s[n+1]!==Je||n+2<s.length)&&(t+=wt)}r=i===Je})}return t}function Y1(e,t){if(!e)return de;let s;typeof e!="string"?s=e.pattern:s=e,s=s.trim();const r=`${s}_${!!t.trimForExclusions}`;let i=es.get(r);if(i)return xi(i,e);let n;return ki.test(s)?i=D2(s.substr(4),s):(n=Ni.exec(Z1(s,t)))?i=P2(n[1],s):(t.trimForExclusions?Ti:Ri).test(s)?i=L2(s,t):(n=Ii.exec(Z1(s,t)))?i=Di(n[1].substr(1),s,!0):(n=ji.exec(Z1(s,t)))?i=Di(n[1],s,!1):i=k2(s),es.set(r,i),xi(i,e)}function xi(e,t){if(typeof t=="string")return e;const s=function(r,i){return ft(r,t.base,!Ee)?e(xo(r.substr(t.base.length),oe),i):null};return s.allBasenames=e.allBasenames,s.allPaths=e.allPaths,s.basenames=e.basenames,s.patterns=e.patterns,s}function Z1(e,t){return t.trimForExclusions&&e.endsWith("/**")?e.substr(0,e.length-2):e}function D2(e,t){return function(s,r){return typeof s=="string"&&s.endsWith(e)?t:null}}function P2(e,t){const s=`/${e}`,r=`\\${e}`,i=function(a,o){return typeof a!="string"?null:o?o===e?t:null:a===e||a.endsWith(s)||a.endsWith(r)?t:null},n=[e];return i.basenames=n,i.patterns=[t],i.allBasenames=n,i}function L2(e,t){const s=Pi(e.slice(1,-1).split(",").map(o=>Y1(o,t)).filter(o=>o!==de),e),r=s.length;if(!r)return de;if(r===1)return s[0];const i=function(o,l){for(let c=0,f=s.length;c<f;c++)if(s[c](o,l))return e;return null},n=s.find(o=>!!o.allBasenames);n&&(i.allBasenames=n.allBasenames);const a=s.reduce((o,l)=>l.allPaths?o.concat(l.allPaths):o,[]);return a.length&&(i.allPaths=a),i}function Di(e,t,s){const r=oe===I.sep,i=r?e:e.replace(Li,oe),n=oe+i,a=I.sep+e;let o;return s?o=function(l,c){return typeof l=="string"&&(l===i||l.endsWith(n)||!r&&(l===e||l.endsWith(a)))?t:null}:o=function(l,c){return typeof l=="string"&&(l===i||!r&&l===e)?t:null},o.allPaths=[(s?"*/":"./")+e],o}function k2(e){try{const t=new RegExp(`^${Si(e)}$`);return function(s){return t.lastIndex=0,typeof s=="string"&&t.test(s)?e:null}}catch{return de}}function N2(e,t={}){if(!e)return ts;if(typeof e=="string"||R2(e)){const s=Y1(e,t);if(s===de)return ts;const r=function(i,n){return!!s(i,n)};return s.allBasenames&&(r.allBasenames=s.allBasenames),s.allPaths&&(r.allPaths=s.allPaths),r}return T2(e,t)}function R2(e){const t=e;return t?typeof t.base=="string"&&typeof t.pattern=="string":!1}function T2(e,t){const s=Pi(Object.getOwnPropertyNames(e).map(o=>I2(o,e[o],t)).filter(o=>o!==de)),r=s.length;if(!r)return de;if(!s.some(o=>!!o.requiresSiblings)){if(r===1)return s[0];const o=function(f,u){let h;for(let d=0,p=s.length;d<p;d++){const y=s[d](f,u);if(typeof y=="string")return y;U1(y)&&(h||(h=[]),h.push(y))}return h?(async()=>{for(const d of h){const p=await d;if(typeof p=="string")return p}return null})():null},l=s.find(f=>!!f.allBasenames);l&&(o.allBasenames=l.allBasenames);const c=s.reduce((f,u)=>u.allPaths?f.concat(u.allPaths):f,[]);return c.length&&(o.allPaths=c),o}const i=function(o,l,c){let f,u;for(let h=0,d=s.length;h<d;h++){const p=s[h];p.requiresSiblings&&c&&(l||(l=lt(o)),f||(f=l.substr(0,l.length-Cr(o).length)));const y=p(o,l,f,c);if(typeof y=="string")return y;U1(y)&&(u||(u=[]),u.push(y))}return u?(async()=>{for(const h of u){const d=await h;if(typeof d=="string")return d}return null})():null},n=s.find(o=>!!o.allBasenames);n&&(i.allBasenames=n.allBasenames);const a=s.reduce((o,l)=>l.allPaths?o.concat(l.allPaths):o,[]);return a.length&&(i.allPaths=a),i}function I2(e,t,s){if(t===!1)return de;const r=Y1(e,s);if(r===de)return de;if(typeof t=="boolean")return r;if(t){const i=t.when;if(typeof i=="string"){const n=(a,o,l,c)=>{if(!c||!r(a,o))return null;const f=i.replace("$(basename)",()=>l),u=c(f);return U1(u)?u.then(h=>h?e:null):u?e:null};return n.requiresSiblings=!0,n}}return r}function Pi(e,t){const s=e.filter(o=>!!o.basenames);if(s.length<2)return e;const r=s.reduce((o,l)=>{const c=l.basenames;return c?o.concat(c):o},[]);let i;if(t){i=[];for(let o=0,l=r.length;o<l;o++)i.push(t)}else i=s.reduce((o,l)=>{const c=l.patterns;return c?o.concat(c):o},[]);const n=function(o,l){if(typeof o!="string")return null;if(!l){let f;for(f=o.length;f>0;f--){const u=o.charCodeAt(f-1);if(u===47||u===92)break}l=o.substr(f)}const c=r.indexOf(l);return c!==-1?i[c]:null};n.basenames=r,n.patterns=i,n.allBasenames=r;const a=e.filter(o=>!o.basenames);return a.push(n),a}var Je,X1,wt,bt,Li,ki,Ni,Ri,Ti,Ii,ji,es,ts,de,j2=O({"out-build/vs/base/common/glob.js"(){"use strict";u1(),dt(),Ue(),h1(),ae(),J(),Me(),Je="**",X1="/",wt="[/\\\\]",bt="[^/\\\\]",Li=/\//g,ki=/^\*\*\/\*\.[\w\.-]+$/,Ni=/^\*\*\/([\w\.-]+)\/?$/,Ri=/^{\*\*\/\*?[\w\.-]+\/?(,\*\*\/\*?[\w\.-]+\/?)*}$/,Ti=/^{\*\*\/\*?[\w\.-]+(\/(\*\*)?)?(,\*\*\/\*?[\w\.-]+(\/(\*\*)?)?)*}$/,Ii=/^\*\*((\/[\w\.-]+)+)\/?$/,ji=/^([\w\.-]+(\/[\w\.-]+)*)\/?$/,es=new Pt(1e4),ts=function(){return!1},de=function(){return null}}}),Fi,Mi,ss,Ui,Vi,xe,yt,qi,Gt,F2=O({"out-build/vs/base/common/ternarySearchTree.js"(){"use strict";u1(),Me(),Fi=class{constructor(){this.b="",this.c=0}reset(e){return this.b=e,this.c=0,this}next(){return this.c+=1,this}hasNext(){return this.c<this.b.length-1}cmp(e){const t=e.charCodeAt(0),s=this.b.charCodeAt(this.c);return t-s}value(){return this.b[this.c]}},Mi=class{constructor(e=!0){this.e=e}reset(e){return this.b=e,this.c=0,this.d=0,this.next()}hasNext(){return this.d<this.b.length}next(){this.c=this.d;let e=!0;for(;this.d<this.b.length;this.d++)if(this.b.charCodeAt(this.d)===46)if(e)this.c++;else break;else e=!1;return this}cmp(e){return this.e?P1(e,this.b,0,e.length,this.c,this.d):ut(e,this.b,0,e.length,this.c,this.d)}value(){return this.b.substring(this.c,this.d)}},ss=class{constructor(e=!0,t=!0){this.f=e,this.g=t}reset(e){this.d=0,this.e=0,this.b=e,this.c=e.length;for(let t=e.length-1;t>=0;t--,this.c--){const s=this.b.charCodeAt(t);if(!(s===47||this.f&&s===92))break}return this.next()}hasNext(){return this.e<this.c}next(){this.d=this.e;let e=!0;for(;this.e<this.c;this.e++){const t=this.b.charCodeAt(this.e);if(t===47||this.f&&t===92)if(e)this.d++;else break;else e=!1}return this}cmp(e){return this.g?P1(e,this.b,0,e.length,this.d,this.e):ut(e,this.b,0,e.length,this.d,this.e)}value(){return this.b.substring(this.d,this.e)}},function(e){e[e.Scheme=1]="Scheme",e[e.Authority=2]="Authority",e[e.Path=3]="Path",e[e.Query=4]="Query",e[e.Fragment=5]="Fragment"}(Ui||(Ui={})),Vi=class{constructor(e,t){this.f=e,this.g=t,this.d=[],this.e=0}reset(e){return this.c=e,this.d=[],this.c.scheme&&this.d.push(1),this.c.authority&&this.d.push(2),this.c.path&&(this.b=new ss(!1,!this.f(e)),this.b.reset(e.path),this.b.value()&&this.d.push(3)),this.g(e)||(this.c.query&&this.d.push(4),this.c.fragment&&this.d.push(5)),this.e=0,this}next(){return this.d[this.e]===3&&this.b.hasNext()?this.b.next():this.e+=1,this}hasNext(){return this.d[this.e]===3&&this.b.hasNext()||this.e<this.d.length-1}cmp(e){if(this.d[this.e]===1)return Sr(e,this.c.scheme);if(this.d[this.e]===2)return Sr(e,this.c.authority);if(this.d[this.e]===3)return this.b.cmp(e);if(this.d[this.e]===4)return D1(e,this.c.query);if(this.d[this.e]===5)return D1(e,this.c.fragment);throw new Error}value(){if(this.d[this.e]===1)return this.c.scheme;if(this.d[this.e]===2)return this.c.authority;if(this.d[this.e]===3)return this.b.value();if(this.d[this.e]===4)return this.c.query;if(this.d[this.e]===5)return this.c.fragment;throw new Error}},xe=class bs{static{this.Val=Symbol("undefined_placeholder")}static wrap(t){return t===void 0?bs.Val:t}static unwrap(t){return t===bs.Val?void 0:t}},yt=class{constructor(){this.height=1}isEmpty(){return!this.left&&!this.mid&&!this.right&&this.value===void 0}rotateLeft(){const e=this.right;return this.right=e.left,e.left=this,this.updateHeight(),e.updateHeight(),e}rotateRight(){const e=this.left;return this.left=e.right,e.right=this,this.updateHeight(),e.updateHeight(),e}updateHeight(){this.height=1+Math.max(this.heightLeft,this.heightRight)}balanceFactor(){return this.heightRight-this.heightLeft}get heightLeft(){return this.left?.height??0}get heightRight(){return this.right?.height??0}},function(e){e[e.Left=-1]="Left",e[e.Mid=0]="Mid",e[e.Right=1]="Right"}(qi||(qi={})),Gt=class At{static forUris(t=()=>!1,s=()=>!1){return new At(new Vi(t,s))}static forPaths(t=!1){return new At(new ss(void 0,!t))}static forStrings(){return new At(new Fi)}static forConfigKeys(){return new At(new Mi)}constructor(t){this.b=t}clear(){this.c=void 0}fill(t,s){if(s){const r=s.slice(0);As(r);for(const i of r)this.set(i,t)}else{const r=t.slice(0);As(r);for(const i of r)this.set(i[0],i[1])}}set(t,s){const r=this.b.reset(t);let i;this.c||(this.c=new yt,this.c.segment=r.value());const n=[];for(i=this.c;;){const o=r.cmp(i.segment);if(o>0)i.left||(i.left=new yt,i.left.segment=r.value()),n.push([-1,i]),i=i.left;else if(o<0)i.right||(i.right=new yt,i.right.segment=r.value()),n.push([1,i]),i=i.right;else if(r.hasNext())r.next(),i.mid||(i.mid=new yt,i.mid.segment=r.value()),n.push([0,i]),i=i.mid;else break}const a=xe.unwrap(i.value);i.value=xe.wrap(s),i.key=t;for(let o=n.length-1;o>=0;o--){const l=n[o][1];l.updateHeight();const c=l.balanceFactor();if(c<-1||c>1){const f=n[o][0],u=n[o+1][0];if(f===1&&u===1)n[o][1]=l.rotateLeft();else if(f===-1&&u===-1)n[o][1]=l.rotateRight();else if(f===1&&u===-1)l.right=n[o+1][1]=n[o+1][1].rotateRight(),n[o][1]=l.rotateLeft();else if(f===-1&&u===1)l.left=n[o+1][1]=n[o+1][1].rotateLeft(),n[o][1]=l.rotateRight();else throw new Error;if(o>0)switch(n[o-1][0]){case-1:n[o-1][1].left=n[o][1];break;case 1:n[o-1][1].right=n[o][1];break;case 0:n[o-1][1].mid=n[o][1];break}else this.c=n[0][1]}}return a}get(t){return xe.unwrap(this.d(t)?.value)}d(t){const s=this.b.reset(t);let r=this.c;for(;r;){const i=s.cmp(r.segment);if(i>0)r=r.left;else if(i<0)r=r.right;else if(s.hasNext())s.next(),r=r.mid;else break}return r}has(t){const s=this.d(t);return!(s?.value===void 0&&s?.mid===void 0)}delete(t){return this.e(t,!1)}deleteSuperstr(t){return this.e(t,!0)}e(t,s){const r=this.b.reset(t),i=[];let n=this.c;for(;n;){const a=r.cmp(n.segment);if(a>0)i.push([-1,n]),n=n.left;else if(a<0)i.push([1,n]),n=n.right;else if(r.hasNext())r.next(),i.push([0,n]),n=n.mid;else break}if(n){if(s?(n.left=void 0,n.mid=void 0,n.right=void 0,n.height=1):(n.key=void 0,n.value=void 0),!n.mid&&!n.value)if(n.left&&n.right){const a=this.f(n.right);if(a.key){const{key:o,value:l,segment:c}=a;this.e(a.key,!1),n.key=o,n.value=l,n.segment=c}}else{const a=n.left??n.right;if(i.length>0){const[o,l]=i[i.length-1];switch(o){case-1:l.left=a;break;case 0:l.mid=a;break;case 1:l.right=a;break}}else this.c=a}for(let a=i.length-1;a>=0;a--){const o=i[a][1];o.updateHeight();const l=o.balanceFactor();if(l>1?(o.right.balanceFactor()>=0||(o.right=o.right.rotateRight()),i[a][1]=o.rotateLeft()):l<-1&&(o.left.balanceFactor()<=0||(o.left=o.left.rotateLeft()),i[a][1]=o.rotateRight()),a>0)switch(i[a-1][0]){case-1:i[a-1][1].left=i[a][1];break;case 1:i[a-1][1].right=i[a][1];break;case 0:i[a-1][1].mid=i[a][1];break}else this.c=i[0][1]}}}f(t){for(;t.left;)t=t.left;return t}findSubstr(t){const s=this.b.reset(t);let r=this.c,i;for(;r;){const n=s.cmp(r.segment);if(n>0)r=r.left;else if(n<0)r=r.right;else if(s.hasNext())s.next(),i=xe.unwrap(r.value)||i,r=r.mid;else break}return r&&xe.unwrap(r.value)||i}findSuperstr(t){return this.g(t,!1)}g(t,s){const r=this.b.reset(t);let i=this.c;for(;i;){const n=r.cmp(i.segment);if(n>0)i=i.left;else if(n<0)i=i.right;else if(r.hasNext())r.next(),i=i.mid;else return i.mid?this.h(i.mid):s?xe.unwrap(i.value):void 0}}hasElementOrSubtree(t){return this.g(t,!0)!==void 0}forEach(t){for(const[s,r]of this)t(r,s)}*[Symbol.iterator](){yield*this.h(this.c)}h(t){const s=[];return this.j(t,s),s[Symbol.iterator]()}j(t,s){t&&(t.left&&this.j(t.left,s),t.value!==void 0&&s.push([t.key,xe.unwrap(t.value)]),t.mid&&this.j(t.mid,s),t.right&&this.j(t.right,s))}_isBalanced(){const t=s=>{if(!s)return!0;const r=s.balanceFactor();return r<-1||r>1?!1:t(s.left)&&t(s.right)};return t(this.c)}}}});function M2(e,t,s){t[Ae.DI_TARGET]===t?t[Ae.DI_DEPENDENCIES].push({id:e,index:s}):(t[Ae.DI_DEPENDENCIES]=[{id:e,index:s}],t[Ae.DI_TARGET]=t)}function rs(e){if(Ae.serviceIds.has(e))return Ae.serviceIds.get(e);const t=function(s,r,i){if(arguments.length!==3)throw new Error("@IServiceName-decorator can only be used to decorate a parameter");M2(t,s,i)};return t.toString=()=>e,Ae.serviceIds.set(e,t),t}var Ae,U2,Wi=O({"out-build/vs/platform/instantiation/common/instantiation.js"(){"use strict";(function(e){e.serviceIds=new Map,e.DI_TARGET="$di$target",e.DI_DEPENDENCIES="$di$dependencies";function t(s){return s[e.DI_DEPENDENCIES]||[]}e.getServiceDependencies=t})(Ae||(Ae={})),U2=rs("instantiationService")}});function V2(e,t,s){return!e||!t||e===t||t.length>e.length?!1:(t.charAt(t.length-1)!==oe&&(t+=oe),s?Pr(e,t):e.indexOf(t)===0)}var q2,Bi,zi,Hi,Gi,Ki,Ji,Qi,W2,Yi,Zi,B2,z2=O({"out-build/vs/platform/files/common/files.js"(){"use strict";F2(),ae(),Me(),d1(),qe(),Nt(),Wi(),J(),T1(),Vt(),q2=rs("fileService"),function(e){e[e.Unknown=0]="Unknown",e[e.File=1]="File",e[e.Directory=2]="Directory",e[e.SymbolicLink=64]="SymbolicLink"}(Bi||(Bi={})),function(e){e[e.Readonly=1]="Readonly",e[e.Locked=2]="Locked"}(zi||(zi={})),function(e){e[e.UPDATED=2]="UPDATED",e[e.ADDED=4]="ADDED",e[e.DELETED=8]="DELETED"}(Hi||(Hi={})),function(e){e[e.None=0]="None",e[e.FileReadWrite=2]="FileReadWrite",e[e.FileOpenReadWriteClose=4]="FileOpenReadWriteClose",e[e.FileReadStream=16]="FileReadStream",e[e.FileFolderCopy=8]="FileFolderCopy",e[e.PathCaseSensitive=1024]="PathCaseSensitive",e[e.Readonly=2048]="Readonly",e[e.Trash=4096]="Trash",e[e.FileWriteUnlock=8192]="FileWriteUnlock",e[e.FileAtomicRead=16384]="FileAtomicRead",e[e.FileAtomicWrite=32768]="FileAtomicWrite",e[e.FileAtomicDelete=65536]="FileAtomicDelete",e[e.FileClone=131072]="FileClone"}(Gi||(Gi={})),function(e){e.FileExists="EntryExists",e.FileNotFound="EntryNotFound",e.FileNotADirectory="EntryNotADirectory",e.FileIsADirectory="EntryIsADirectory",e.FileExceedsStorageQuota="EntryExceedsStorageQuota",e.FileTooLarge="EntryTooLarge",e.FileWriteLocked="EntryWriteLocked",e.NoPermissions="NoPermissions",e.Unavailable="Unavailable",e.Unknown="Unknown"}(Ki||(Ki={})),function(e){e[e.CREATE=0]="CREATE",e[e.DELETE=1]="DELETE",e[e.MOVE=2]="MOVE",e[e.COPY=3]="COPY",e[e.WRITE=4]="WRITE"}(Ji||(Ji={})),function(e){e[e.UPDATED=0]="UPDATED",e[e.ADDED=1]="ADDED",e[e.DELETED=2]="DELETED"}(Qi||(Qi={})),W2=class r1{static{this.a=null}constructor(t,s){this.c=s,this.b=void 0,this.d=new Fe(()=>{const r=Gt.forUris(()=>this.c);return r.fill(this.rawAdded.map(i=>[i,!0])),r}),this.f=new Fe(()=>{const r=Gt.forUris(()=>this.c);return r.fill(this.rawUpdated.map(i=>[i,!0])),r}),this.g=new Fe(()=>{const r=Gt.forUris(()=>this.c);return r.fill(this.rawDeleted.map(i=>[i,!0])),r}),this.rawAdded=[],this.rawUpdated=[],this.rawDeleted=[];for(const r of t){switch(r.type){case 1:this.rawAdded.push(r.resource);break;case 0:this.rawUpdated.push(r.resource);break;case 2:this.rawDeleted.push(r.resource);break}this.b!==r1.a&&(typeof r.cId=="number"?this.b===void 0?this.b=r.cId:this.b!==r.cId&&(this.b=r1.a):this.b!==void 0&&(this.b=r1.a))}}contains(t,...s){return this.h(t,{includeChildren:!1},...s)}affects(t,...s){return this.h(t,{includeChildren:!0},...s)}h(t,s,...r){if(!t)return!1;const i=r.length>0;return!!((!i||r.includes(1))&&(this.d.value.get(t)||s.includeChildren&&this.d.value.findSuperstr(t))||(!i||r.includes(0))&&(this.f.value.get(t)||s.includeChildren&&this.f.value.findSuperstr(t))||(!i||r.includes(2))&&(this.g.value.findSubstr(t)||s.includeChildren&&this.g.value.findSuperstr(t)))}gotAdded(){return this.rawAdded.length>0}gotDeleted(){return this.rawDeleted.length>0}gotUpdated(){return this.rawUpdated.length>0}correlates(t){return this.b===t}hasCorrelation(){return typeof this.b=="number"}},function(e){e[e.FILE_IS_DIRECTORY=0]="FILE_IS_DIRECTORY",e[e.FILE_NOT_FOUND=1]="FILE_NOT_FOUND",e[e.FILE_NOT_MODIFIED_SINCE=2]="FILE_NOT_MODIFIED_SINCE",e[e.FILE_MODIFIED_SINCE=3]="FILE_MODIFIED_SINCE",e[e.FILE_MOVE_CONFLICT=4]="FILE_MOVE_CONFLICT",e[e.FILE_WRITE_LOCKED=5]="FILE_WRITE_LOCKED",e[e.FILE_PERMISSION_DENIED=6]="FILE_PERMISSION_DENIED",e[e.FILE_TOO_LARGE=7]="FILE_TOO_LARGE",e[e.FILE_INVALID_PATH=8]="FILE_INVALID_PATH",e[e.FILE_NOT_DIRECTORY=9]="FILE_NOT_DIRECTORY",e[e.FILE_OTHER_ERROR=10]="FILE_OTHER_ERROR"}(Yi||(Yi={})),function(e){e[e.FILE=0]="FILE",e[e.FOLDER=1]="FOLDER",e[e.ROOT_FOLDER=2]="ROOT_FOLDER"}(Zi||(Zi={})),B2=class Z{static{this.KB=1024}static{this.MB=Z.KB*Z.KB}static{this.GB=Z.MB*Z.KB}static{this.TB=Z.GB*Z.KB}static formatSize(t){return eo(t)||(t=0),t<Z.KB?v(2008,null,t.toFixed(0)):t<Z.MB?v(2009,null,(t/Z.KB).toFixed(2)):t<Z.GB?v(2010,null,(t/Z.MB).toFixed(2)):t<Z.TB?v(2011,null,(t/Z.GB).toFixed(2)):v(2012,null,(t/Z.TB).toFixed(2))}}}});function H2(e){return typeof e.correlationId=="number"}function G2(e){const t=new en;for(const s of e)t.processEvent(s);return t.coalesce()}function K2(e,t){return typeof t=="string"&&!t.startsWith(Je)&&!He(t)?{base:e,pattern:t}:t}function Xi(e,t){const s=[];for(const r of t)s.push(N2(K2(e,r)));return s}function J2(e,t){if(typeof t=="number")switch(e.type){case 1:return(t&4)===0;case 2:return(t&8)===0;case 0:return(t&2)===0}return!1}var Q2,en,Y2=O({"out-build/vs/platform/files/common/watcher.js"(){"use strict";j2(),Lt(),ae(),J(),qe(),z2(),Q2=class Dn extends ve{static{this.a=5}constructor(t,s,r,i){super(),this.h=t,this.j=s,this.m=r,this.n=i,this.c=this.B(new m1),this.f=void 0,this.g=0}s(){const t=new Ne;this.c.value=t,this.b=this.r(t),this.b.setVerboseLogging(this.m),t.add(this.b.onDidChangeFile(s=>this.h(s))),t.add(this.b.onDidLogMessage(s=>this.j(s))),t.add(this.b.onDidError(s=>this.t(s.error,s.request)))}t(t,s){this.u(t,s)?this.g<Dn.a&&this.f?(this.y(`restarting watcher after unexpected error: ${t}`),this.w(this.f)):this.y(`gave up attempting to restart watcher after unexpected error: ${t}`):this.y(t)}u(t,s){return!(!this.n.restartOnError||s||t.indexOf("No space left on device")!==-1||t.indexOf("EMFILE")!==-1)}w(t){this.g++,this.s(),this.watch(t)}async watch(t){this.f=t,await this.b?.watch(t)}async setVerboseLogging(t){this.m=t,await this.b?.setVerboseLogging(t)}y(t){this.j({type:"error",message:`[File Watcher (${this.n.type})] ${t}`})}z(t){this.j({type:"trace",message:`[File Watcher (${this.n.type})] ${t}`})}dispose(){return this.b=void 0,super.dispose()}},en=class{constructor(){this.a=new Set,this.b=new Map}c(e){return Ee?e.resource.fsPath:e.resource.fsPath.toLowerCase()}processEvent(e){const t=this.b.get(this.c(e));let s=!1;if(t){const r=t.type,i=e.type;t.resource.fsPath!==e.resource.fsPath&&(e.type===2||e.type===1)?s=!0:r===1&&i===2?(this.b.delete(this.c(e)),this.a.delete(t)):r===2&&i===1?t.type=0:r===1&&i===0||(t.type=i)}else s=!0;s&&(this.a.add(e),this.b.set(this.c(e),e))}coalesce(){const e=[],t=[];return Array.from(this.a).filter(s=>s.type!==2?(e.push(s),!1):!0).sort((s,r)=>s.resource.fsPath.length-r.resource.fsPath.length).filter(s=>t.some(r=>V2(s.resource.fsPath,r,!Ee))?!1:(t.push(s.resource.fsPath),!0)).concat(e)}}}});import{watch as Z2,promises as X2}from"fs";async function e0(e,t,s,r,i=512){const n=await we.open(e,"r"),a=Buffer.allocUnsafe(i),o=new Ge(r);let l,c=!1;const f={path:e,excludes:[],recursive:!1},u=new tn(f,void 0,h=>{(async()=>{for(const{type:d}of h)if(d===0){if(c)return;c=!0;try{for(;!o.token.isCancellationRequested;){const{bytesRead:p}=await we.read(n,a,0,i,null);if(!p||o.token.isCancellationRequested)break;t(a.slice(0,p))}}catch(p){l=new Error(p),o.dispose(!0)}finally{c=!1}}})()});return await u.ready,s(),new Promise((h,d)=>{o.token.onCancellationRequested(async()=>{u.dispose();try{await we.close(n)}catch(p){l=new Error(p)}l?d(l):h()})})}var tn,t0=O({"out-build/vs/platform/files/node/watcher/nodejs/nodejsWatcherLib.js"(){"use strict";dt(),Ut(),Ue(),Lt(),pi(),ae(),J(),M1(),qe(),x2(),K1(),Y2(),Vt(),tn=class i1 extends ve{static{this.a=100}static{this.b=75}get isReusingRecursiveWatcher(){return this.r}get failed(){return this.s}constructor(t,s,r,i,n,a){super(),this.t=t,this.u=s,this.w=r,this.y=i,this.z=n,this.C=a,this.c=this.B(new li({maxWorkChunkSize:100,throttleDelay:200,maxBufferedWork:1e4},o=>this.w(o))),this.f=this.B(new ai(o=>this.M(o),i1.b)),this.m=new Ge,this.n=new Fe(async()=>{let o=this.t.path;try{o=await O2(this.t.path),this.t.path!==o&&this.Q(`correcting a path to watch that seems to be a symbolic link (original: ${this.t.path}, real: ${o})`)}catch{}return o}),this.r=!1,this.s=!1,this.g=Xi(this.t.path,this.t.excludes),this.h=this.t.includes?Xi(this.t.path,this.t.includes):void 0,this.j=H2(this.t)?this.t.filter:void 0,this.ready=this.D()}async D(){try{const t=await X2.stat(this.t.path);if(this.m.token.isCancellationRequested)return;this.B(await this.G(t.isDirectory()))}catch(t){t.code!=="ENOENT"?this.O(t):this.Q(`ignoring a path for watching who's stat info failed to resolve: ${this.t.path} (error: ${t})`),this.F()}}F(){this.s=!0,this.y?.()}async G(t){const s=new Ne;return this.H(t,s)?(this.Q(`reusing an existing recursive watcher for ${this.t.path}`),this.r=!0):(this.r=!1,await this.I(t,s)),s}H(t,s){if(t)return!1;const r=K.file(this.t.path),i=this.u?.subscribe(this.t.path,async(n,a)=>{if(!s.isDisposed)if(n){const o=await this.G(t);s.isDisposed?o.dispose():s.add(o)}else a&&(typeof a.cId=="number"||typeof this.t.correlationId=="number")&&this.L({resource:r,type:a.type,cId:this.t.correlationId},!0)});return i?(s.add(i),!0):!1}async I(t,s){const r=await this.n.value;if(Te&&ft(r,"/Volumes/",!0)){this.O(`Refusing to watch ${r} for changes using fs.watch() for possibly being a network share where watching is unreliable and unstable.`);return}const i=new Ge(this.m.token);s.add(Le(()=>i.dispose(!0)));const n=new Ne;s.add(n);try{const a=K.file(this.t.path),o=lt(r),l=Z2(r);n.add(Le(()=>{l.removeAllListeners(),l.close()})),this.Q(`Started watching: '${r}'`);const c=new Set;if(t)try{for(const u of await we.readdir(r))c.add(u)}catch(u){this.O(u)}if(i.token.isCancellationRequested)return;const f=new Map;n.add(Le(()=>{for(const[,u]of f)u.dispose();f.clear()})),l.on("error",(u,h)=>{i.token.isCancellationRequested||(this.O(`Failed to watch ${r} for changes using fs.watch() (${u}, ${h})`),this.F())}),l.on("change",(u,h)=>{if(i.token.isCancellationRequested)return;this.C&&this.R(`[raw] ["${u}"] ${h}`);let d="";if(h&&(d=h.toString(),Te&&(d=z1(d))),!(!d||u!=="change"&&u!=="rename"))if(t)if(u==="rename"){f.get(d)?.dispose();const p=setTimeout(async()=>{if(f.delete(d),Fr(d,o,!Ee)&&!await we.exists(r)){this.J(a);return}if(i.token.isCancellationRequested)return;const y=await this.N(te(r,d));if(i.token.isCancellationRequested)return;let E;y?c.has(d)?E=0:(E=1,c.add(d)):(c.delete(d),E=2),this.L({resource:I1(a,d),type:E,cId:this.t.correlationId})},i1.a);f.set(d,Le(()=>clearTimeout(p)))}else{let p;c.has(d)?p=0:(p=1,c.add(d)),this.L({resource:I1(a,d),type:p,cId:this.t.correlationId})}else if(u==="rename"||!Fr(d,o,!Ee)){const p=setTimeout(async()=>{const y=await we.exists(r);i.token.isCancellationRequested||(y?(this.L({resource:a,type:0,cId:this.t.correlationId},!0),n.add(await this.G(!1))):this.J(a))},i1.a);n.clear(),n.add(Le(()=>clearTimeout(p)))}else this.L({resource:a,type:0,cId:this.t.correlationId},!0)})}catch(a){i.token.isCancellationRequested||this.O(`Failed to watch ${r} for changes using fs.watch() (${a.toString()})`),this.F()}}J(t){this.P("Watcher shutdown because watched path got deleted"),this.L({resource:t,type:2,cId:this.t.correlationId},!0),this.f.flush(),this.F()}L(t,s=!1){this.m.token.isCancellationRequested||(this.C&&this.R(`${t.type===1?"[ADDED]":t.type===2?"[DELETED]":"[CHANGED]"} ${t.resource.fsPath}`),!s&&this.g.some(r=>r(t.resource.fsPath))?this.C&&this.R(` >> ignored (excluded) ${t.resource.fsPath}`):!s&&this.h&&this.h.length>0&&!this.h.some(r=>r(t.resource.fsPath))?this.C&&this.R(` >> ignored (not included) ${t.resource.fsPath}`):this.f.work(t))}M(t){const s=G2(t),r=[];for(const n of s){if(J2(n,this.j)){this.C&&this.R(` >> ignored (filtered) ${n.resource.fsPath}`);continue}r.push(n)}if(r.length===0)return;if(this.C)for(const n of r)this.R(` >> normalized ${n.type===1?"[ADDED]":n.type===2?"[DELETED]":"[CHANGED]"} ${n.resource.fsPath}`);this.c.work(r)?this.c.pending>0&&this.Q(`started throttling events due to large amount of file change events at once (pending: ${this.c.pending}, most recent change: ${r[0].resource.fsPath}). Use 'files.watcherExclude' setting to exclude folders with lots of changing files (e.g. compilation output).`):this.P(`started ignoring events due to too many file change events at once (incoming: ${r.length}, most recent change: ${r[0].resource.fsPath}). Use 'files.watcherExclude' setting to exclude folders with lots of changing files (e.g. compilation output).`)}async N(t){if(Ee)return we.exists(t);try{const s=lt(t);return(await we.readdir(je(t))).some(i=>i===s)}catch(s){return this.Q(s),!1}}setVerboseLogging(t){this.C=t}O(t){this.m.token.isCancellationRequested||this.z?.({type:"error",message:`[File Watcher (node.js)] ${t}`})}P(t){this.m.token.isCancellationRequested||this.z?.({type:"warn",message:`[File Watcher (node.js)] ${t}`})}Q(t){!this.m.token.isCancellationRequested&&this.C&&this.z?.({type:"trace",message:`[File Watcher (node.js)] ${t}`})}R(t){!this.m.token.isCancellationRequested&&this.C&&this.Q(`${t}${typeof this.t.correlationId=="number"?` <${this.t.correlationId}> `:""}`)}dispose(){this.m.dispose(!0),super.dispose()}}}}),s0=Fn({"node_modules/minimist/index.js"(e,t){"use strict";function s(n,a){var o=n;a.slice(0,-1).forEach(function(c){o=o[c]||{}});var l=a[a.length-1];return l in o}function r(n){return typeof n=="number"||/^0x[0-9a-f]+$/i.test(n)?!0:/^[-+]?(?:\d+(?:\.\d*)?|\.\d+)(e[-+]?\d+)?$/.test(n)}function i(n,a){return a==="constructor"&&typeof n[a]=="function"||a==="__proto__"}t.exports=function(n,a){a||(a={});var o={bools:{},strings:{},unknownFn:null};typeof a.unknown=="function"&&(o.unknownFn=a.unknown),typeof a.boolean=="boolean"&&a.boolean?o.allBools=!0:[].concat(a.boolean).filter(Boolean).forEach(function(S){o.bools[S]=!0});var l={};function c(S){return l[S].some(function(U){return o.bools[U]})}Object.keys(a.alias||{}).forEach(function(S){l[S]=[].concat(a.alias[S]),l[S].forEach(function(U){l[U]=[S].concat(l[S].filter(function(ue){return U!==ue}))})}),[].concat(a.string).filter(Boolean).forEach(function(S){o.strings[S]=!0,l[S]&&[].concat(l[S]).forEach(function(U){o.strings[U]=!0})});var f=a.default||{},u={_:[]};function h(S,U){return o.allBools&&/^--[^=]+$/.test(U)||o.strings[S]||o.bools[S]||l[S]}function d(S,U,ue){for(var j=S,Pe=0;Pe<U.length-1;Pe++){var m=U[Pe];if(i(j,m))return;j[m]===void 0&&(j[m]={}),(j[m]===Object.prototype||j[m]===Number.prototype||j[m]===String.prototype)&&(j[m]={}),j[m]===Array.prototype&&(j[m]=[]),j=j[m]}var g=U[U.length-1];i(j,g)||((j===Object.prototype||j===Number.prototype||j===String.prototype)&&(j={}),j===Array.prototype&&(j=[]),j[g]===void 0||o.bools[g]||typeof j[g]=="boolean"?j[g]=ue:Array.isArray(j[g])?j[g].push(ue):j[g]=[j[g],ue])}function p(S,U,ue){if(!(ue&&o.unknownFn&&!h(S,ue)&&o.unknownFn(ue)===!1)){var j=!o.strings[S]&&r(U)?Number(U):U;d(u,S.split("."),j),(l[S]||[]).forEach(function(Pe){d(u,Pe.split("."),j)})}}Object.keys(o.bools).forEach(function(S){p(S,f[S]===void 0?!1:f[S])});var y=[];n.indexOf("--")!==-1&&(y=n.slice(n.indexOf("--")+1),n=n.slice(0,n.indexOf("--")));for(var E=0;E<n.length;E++){var C=n[E],N,k;if(/^--.+=/.test(C)){var X=C.match(/^--([^=]+)=([\s\S]*)$/);N=X[1];var _=X[2];o.bools[N]&&(_=_!=="false"),p(N,_,C)}else if(/^--no-.+/.test(C))N=C.match(/^--no-(.+)/)[1],p(N,!1,C);else if(/^--.+/.test(C))N=C.match(/^--(.+)/)[1],k=n[E+1],k!==void 0&&!/^(-|--)[^-]/.test(k)&&!o.bools[N]&&!o.allBools&&(!l[N]||!c(N))?(p(N,k,C),E+=1):/^(true|false)$/.test(k)?(p(N,k==="true",C),E+=1):p(N,o.strings[N]?"":!0,C);else if(/^-[^-]+/.test(C)){for(var $=C.slice(1,-1).split(""),x=!1,P=0;P<$.length;P++){if(k=C.slice(P+2),k==="-"){p($[P],k,C);continue}if(/[A-Za-z]/.test($[P])&&k[0]==="="){p($[P],k.slice(1),C),x=!0;break}if(/[A-Za-z]/.test($[P])&&/-?\d+(\.\d*)?(e-?\d+)?$/.test(k)){p($[P],k,C),x=!0;break}if($[P+1]&&$[P+1].match(/\W/)){p($[P],C.slice(P+2),C),x=!0;break}else p($[P],o.strings[$[P]]?"":!0,C)}N=C.slice(-1)[0],!x&&N!=="-"&&(n[E+1]&&!/^(-|--)[^-]/.test(n[E+1])&&!o.bools[N]&&(!l[N]||!c(N))?(p(N,n[E+1],C),E+=1):n[E+1]&&/^(true|false)$/.test(n[E+1])?(p(N,n[E+1]==="true",C),E+=1):p(N,o.strings[N]?"":!0,C))}else if((!o.unknownFn||o.unknownFn(C)!==!1)&&u._.push(o.strings._||!r(C)?C:Number(C)),a.stopEarly){u._.push.apply(u._,n.slice(E+1));break}}return Object.keys(f).forEach(function(S){s(u,S.split("."))||(d(u,S.split("."),f[S]),(l[S]||[]).forEach(function(U){d(u,U.split("."),f[S])}))}),a["--"]?u["--"]=y.slice():y.forEach(function(S){u._.push(S)}),u}}});function sn(e,t,s=ln){const r=e.find(h=>h.length>0&&h[0]!=="-"),i={},n=["_"],a=[],o={};let l;for(const h in t){const d=t[h];d.type==="subcommand"?h===r&&(l=d):(d.alias&&(i[h]=d.alias),d.type==="string"||d.type==="string[]"?(n.push(h),d.deprecates&&n.push(...d.deprecates)):d.type==="boolean"&&(a.push(h),d.deprecates&&a.push(...d.deprecates)),d.global&&(o[h]=d))}if(l&&r){const h=o;for(const E in l.options)h[E]=l.options[E];const d=e.filter(E=>E!==r),p=s.getSubcommandReporter?s.getSubcommandReporter(r):void 0,y=sn(d,h,p);return{[r]:y,_:[]}}const c=(0,on.default)(e,{string:n,boolean:a,alias:i}),f={},u=c;f._=c._.map(h=>String(h)).filter(h=>h.length>0),delete u._;for(const h in t){const d=t[h];if(d.type==="subcommand")continue;d.alias&&delete u[d.alias];let p=u[h];if(d.deprecates)for(const y of d.deprecates)u.hasOwnProperty(y)&&(p||(p=u[y],p&&s.onDeprecatedOption(y,d.deprecationMessage||v(1884,null,h))),delete u[y]);if(typeof p<"u"){if(d.type==="string[]"){if(Array.isArray(p)||(p=[p]),!d.allowEmptyValue){const y=p.filter(E=>E.length>0);y.length!==p.length&&(s.onEmptyValue(h),p=y.length>0?y:void 0)}}else d.type==="string"&&(Array.isArray(p)?(p=p.pop(),s.onMultipleValues(h,p)):!p&&!d.allowEmptyValue&&(s.onEmptyValue(h),p=void 0));f[h]=p,d.deprecationMessage&&s.onDeprecatedOption(h,d.deprecationMessage)}delete u[h]}for(const h in u)s.onUnknownOption(h);return f}function r0(e,t){let s="";return t.args&&(Array.isArray(t.args)?s=` <${t.args.join("> <")}>`:s=` <${t.args}>`),t.alias?`-${t.alias} --${e}${s}`:`--${e}${s}`}function i0(e,t){const s=[];for(const r in e){const i=e[r],n=r0(r,i);s.push([n,i.description])}return rn(s,t)}function rn(e,t){const r=e.reduce((a,o)=>Math.max(a,o[0].length),12)+2+1;if(t-r<25)return e.reduce((a,o)=>a.concat([`  ${o[0]}`,`      ${o[1]}`]),[]);const i=t-r-1,n=[];for(const a of e){const o=a[0],l=n0(a[1],i),c=nn(r-o.length-2);n.push("  "+o+c+l[0]);for(let f=1;f<l.length;f++)n.push(nn(r)+l[f])}return n}function nn(e){return" ".repeat(e)}function n0(e,t){const s=[];for(;e.length;){let r=e.length<t?e.length:e.lastIndexOf(" ",t);r===0&&(r=t);const i=e.slice(0,r).trim();e=e.slice(r).trimStart(),s.push(i)}return s}function o0(e,t,s,r,i){const n=process.stdout.isTTY&&process.stdout.columns||80,a=i?.noInputFiles!==!0?`[${v(1885,null)}...]`:"",o=[`${e} ${s}`];o.push(""),o.push(`${v(1886,null)}: ${t} [${v(1887,null)}]${a}`),o.push(""),i?.noPipe!==!0&&(W?o.push(v(1888,null,t)):o.push(v(1889,null,t)),o.push(""));const l={},c=[];for(const f in r){const u=r[f];if(u.type==="subcommand")u.description&&c.push({command:f,description:u.description});else if(u.description&&u.cat){let h=l[u.cat];h||(l[u.cat]=h={}),h[f]=u}}for(const f in l){const u=f,h=l[u];h&&(o.push(an[u]),o.push(...i0(h,n)),o.push(""))}return c.length&&(o.push(v(1890,null)),o.push(...rn(c.map(f=>[f.command,f.description]),n)),o.push("")),o.join(`
`)}function a0(e,t){return`${e||v(1891,null)}
${t||v(1892,null)}
${process.arch}`}var on,an,Kt,is,ln,cn=O({"out-build/vs/platform/environment/node/argv.js"(){"use strict";on=Vn(s0(),1),J(),Nt(),an={o:v(1842,null),e:v(1843,null),t:v(1844,null)},Kt=["tunnel","serve-web"],is={tunnel:{type:"subcommand",description:"Make the current machine accessible from vscode.dev or other machines through a secure tunnel",options:{"cli-data-dir":{type:"string",args:"dir",description:v(1845,null)},"disable-telemetry":{type:"boolean"},"telemetry-level":{type:"string"},user:{type:"subcommand",options:{login:{type:"subcommand",options:{provider:{type:"string"},"access-token":{type:"string"}}}}}}},"serve-web":{type:"subcommand",description:"Run a server that displays the editor UI in browsers.",options:{"cli-data-dir":{type:"string",args:"dir",description:v(1846,null)},"disable-telemetry":{type:"boolean"},"telemetry-level":{type:"string"}}},diff:{type:"boolean",cat:"o",alias:"d",args:["file","file"],description:v(1847,null)},merge:{type:"boolean",cat:"o",alias:"m",args:["path1","path2","base","result"],description:v(1848,null)},add:{type:"boolean",cat:"o",alias:"a",args:"folder",description:v(1849,null)},remove:{type:"boolean",cat:"o",args:"folder",description:v(1850,null)},goto:{type:"boolean",cat:"o",alias:"g",args:"file:line[:character]",description:v(1851,null)},"new-window":{type:"boolean",cat:"o",alias:"n",description:v(1852,null)},"reuse-window":{type:"boolean",cat:"o",alias:"r",description:v(1853,null)},wait:{type:"boolean",cat:"o",alias:"w",description:v(1854,null)},waitMarkerFilePath:{type:"string"},locale:{type:"string",cat:"o",args:"locale",description:v(1855,null)},"user-data-dir":{type:"string",cat:"o",args:"dir",description:v(1856,null)},profile:{type:"string",cat:"o",args:"profileName",description:v(1857,null)},help:{type:"boolean",cat:"o",alias:"h",description:v(1858,null)},"extensions-dir":{type:"string",deprecates:["extensionHomePath"],cat:"e",args:"dir",description:v(1859,null)},"extensions-download-dir":{type:"string"},"builtin-extensions-dir":{type:"string"},"list-extensions":{type:"boolean",cat:"e",description:v(1860,null)},"show-versions":{type:"boolean",cat:"e",description:v(1861,null)},category:{type:"string",allowEmptyValue:!0,cat:"e",description:v(1862,null),args:"category"},"install-extension":{type:"string[]",cat:"e",args:"ext-id | path",description:v(1863,null)},"pre-release":{type:"boolean",cat:"e",description:v(1864,null)},"uninstall-extension":{type:"string[]",cat:"e",args:"ext-id",description:v(1865,null)},"update-extensions":{type:"boolean",cat:"e",description:v(1866,null)},"enable-proposed-api":{type:"string[]",allowEmptyValue:!0,cat:"e",args:"ext-id",description:v(1867,null)},"add-mcp":{type:"string[]",cat:"o",args:"json",description:v(1868,null)},version:{type:"boolean",cat:"t",alias:"v",description:v(1869,null)},verbose:{type:"boolean",cat:"t",global:!0,description:v(1870,null)},log:{type:"string[]",cat:"t",args:"level",global:!0,description:v(1871,null)},status:{type:"boolean",alias:"s",cat:"t",description:v(1872,null)},"prof-startup":{type:"boolean",cat:"t",description:v(1873,null)},"prof-append-timers":{type:"string"},"prof-duration-markers":{type:"string[]"},"prof-duration-markers-file":{type:"string"},"no-cached-data":{type:"boolean"},"prof-startup-prefix":{type:"string"},"prof-v8-extensions":{type:"boolean"},"disable-extensions":{type:"boolean",deprecates:["disableExtensions"],cat:"t",description:v(1874,null)},"disable-extension":{type:"string[]",cat:"t",args:"ext-id",description:v(1875,null)},sync:{type:"string",cat:"t",description:v(1876,null),args:["on | off"]},"inspect-extensions":{type:"string",allowEmptyValue:!0,deprecates:["debugPluginHost"],args:"port",cat:"t",description:v(1877,null)},"inspect-brk-extensions":{type:"string",allowEmptyValue:!0,deprecates:["debugBrkPluginHost"],args:"port",cat:"t",description:v(1878,null)},"disable-lcd-text":{type:"boolean",cat:"t",description:v(1879,null)},"disable-gpu":{type:"boolean",cat:"t",description:v(1880,null)},"disable-chromium-sandbox":{type:"boolean",cat:"t",description:v(1881,null)},sandbox:{type:"boolean"},"locate-shell-integration-path":{type:"string",cat:"t",args:["shell"],description:v(1882,null)},telemetry:{type:"boolean",cat:"t",description:v(1883,null)},remote:{type:"string",allowEmptyValue:!0},"folder-uri":{type:"string[]",cat:"o",args:"uri"},"file-uri":{type:"string[]",cat:"o",args:"uri"},"locate-extension":{type:"string[]"},extensionDevelopmentPath:{type:"string[]"},extensionDevelopmentKind:{type:"string[]"},extensionTestsPath:{type:"string"},extensionEnvironment:{type:"string"},debugId:{type:"string"},debugRenderer:{type:"boolean"},"inspect-ptyhost":{type:"string",allowEmptyValue:!0},"inspect-brk-ptyhost":{type:"string",allowEmptyValue:!0},"inspect-search":{type:"string",deprecates:["debugSearch"],allowEmptyValue:!0},"inspect-brk-search":{type:"string",deprecates:["debugBrkSearch"],allowEmptyValue:!0},"inspect-sharedprocess":{type:"string",allowEmptyValue:!0},"inspect-brk-sharedprocess":{type:"string",allowEmptyValue:!0},"export-default-configuration":{type:"string"},"install-source":{type:"string"},"enable-smoke-test-driver":{type:"boolean"},logExtensionHostCommunication:{type:"boolean"},"skip-release-notes":{type:"boolean"},"skip-welcome":{type:"boolean"},"disable-telemetry":{type:"boolean"},"disable-updates":{type:"boolean"},"use-inmemory-secretstorage":{type:"boolean",deprecates:["disable-keytar"]},"password-store":{type:"string"},"disable-workspace-trust":{type:"boolean"},"disable-crash-reporter":{type:"boolean"},"crash-reporter-directory":{type:"string"},"crash-reporter-id":{type:"string"},"skip-add-to-recently-opened":{type:"boolean"},"open-url":{type:"boolean"},"file-write":{type:"boolean"},"file-chmod":{type:"boolean"},"install-builtin-extension":{type:"string[]"},force:{type:"boolean"},"do-not-sync":{type:"boolean"},"do-not-include-pack-dependencies":{type:"boolean"},trace:{type:"boolean"},"trace-memory-infra":{type:"boolean"},"trace-category-filter":{type:"string"},"trace-options":{type:"string"},"preserve-env":{type:"boolean"},"force-user-env":{type:"boolean"},"force-disable-user-env":{type:"boolean"},"open-devtools":{type:"boolean"},"disable-gpu-sandbox":{type:"boolean"},logsPath:{type:"string"},"__enable-file-policy":{type:"boolean"},editSessionId:{type:"string"},continueOn:{type:"string"},"enable-coi":{type:"boolean"},"unresponsive-sample-interval":{type:"string"},"unresponsive-sample-period":{type:"string"},"no-proxy-server":{type:"boolean"},"no-sandbox":{type:"boolean",alias:"sandbox"},"proxy-server":{type:"string"},"proxy-bypass-list":{type:"string"},"proxy-pac-url":{type:"string"},"js-flags":{type:"string"},inspect:{type:"string",allowEmptyValue:!0},"inspect-brk":{type:"string",allowEmptyValue:!0},nolazy:{type:"boolean"},"force-device-scale-factor":{type:"string"},"force-renderer-accessibility":{type:"boolean"},"ignore-certificate-errors":{type:"boolean"},"allow-insecure-localhost":{type:"boolean"},"log-net-log":{type:"string"},vmodule:{type:"string"},_urls:{type:"string[]"},"disable-dev-shm-usage":{type:"boolean"},"profile-temp":{type:"boolean"},"ozone-platform":{type:"string"},"enable-tracing":{type:"string"},"trace-startup-format":{type:"string"},"trace-startup-file":{type:"string"},"trace-startup-duration":{type:"string"},"xdg-portal-required-version":{type:"string"},_:{type:"string[]"}},ln={onUnknownOption:()=>{},onMultipleValues:()=>{},onEmptyValue:()=>{},onDeprecatedOption:()=>{}}}});import l0 from"assert";function c0(e,t){const s=(l,c)=>{console.warn(v(1893,null,l,c))},r=l=>{console.warn(v(1894,null,l))},i=(l,c)=>{console.warn(v(1895,null,l,c))},n=l=>({onUnknownOption:c=>{Kt.includes(l)||console.warn(v(1896,null,c,l))},onMultipleValues:s,onEmptyValue:r,onDeprecatedOption:i,getSubcommandReporter:Kt.includes(l)?n:void 0}),o=sn(e,is,t?{onUnknownOption:l=>{console.warn(v(1897,null,l))},onMultipleValues:s,onEmptyValue:r,onDeprecatedOption:i,getSubcommandReporter:n}:void 0);return o.goto&&o._.forEach(l=>l0(/^(\w:)?[^:]+(:\d*){0,2}:?$/.test(l),v(1898,null))),o}function u0(e){const t=e.findIndex(s=>!/^-/.test(s));if(t>-1)return[...e.slice(0,t),...e.slice(t+1)]}function f0(e){let[,,...t]=e;return process.env.VSCODE_DEV&&(t=u0(t)||[]),c0(t,!0)}function De(e,...t){const s=e.indexOf("--");return s===-1?e.push(...t):e.splice(s,0,...t),e}var h0=O({"out-build/vs/platform/environment/node/argvHelper.js"(){"use strict";Nt(),cn()}});import{exec as un}from"child_process";function d0(e){const t=e.replace(/[^a-zA-Z0-9]/g,"").toLowerCase();return fn[t]||t}async function p0(e){let t;const s=process.env.VSCODE_CLI_ENCODING;s?(e&&console.log(`Found VSCODE_CLI_ENCODING variable: ${s}`),t=Promise.resolve(s)):W?t=new Promise(i=>{e&&console.log('Running "chcp" to detect terminal encoding...'),un("chcp",(n,a,o)=>{if(a){e&&console.log(`Output from "chcp" command is: ${a}`);const l=Object.keys(ns);for(const c of l)if(a.indexOf(c)>=0)return i(ns[c])}return i(void 0)})}):t=new Promise(i=>{e&&console.log('Running "locale charmap" to detect terminal encoding...'),un("locale charmap",(n,a,o)=>i(a))});const r=await t;return e&&console.log(`Detected raw terminal encoding: ${r}`),!r||r.toLowerCase()==="utf-8"||r.toLowerCase()===os?os:d0(r)}var ns,fn,os,g0=O({"out-build/vs/base/node/terminalEncoding.js"(){"use strict";J(),ns={437:"cp437",850:"cp850",852:"cp852",855:"cp855",857:"cp857",860:"cp860",861:"cp861",863:"cp863",865:"cp865",866:"cp866",869:"cp869",936:"cp936",1252:"cp1252"},fn={ibm866:"cp866",big5:"cp950"},os="utf8"}});import*as Jt from"fs";import{tmpdir as m0}from"os";function w0(){try{return!process.stdin.isTTY}catch{}return!1}function b0(e){return new Promise(t=>{const s=()=>t(!0);setTimeout(()=>{process.stdin.removeListener("data",s),t(!1)},e),process.stdin.once("data",s)})}function y0(){return ht(m0(),"code-stdin",3)}async function v0(e){await Jt.promises.appendFile(e,""),await Jt.promises.chmod(e,384)}async function E0(e,t,s){let[r,i]=await Promise.all([p0(t),import("@vscode/iconv-lite-umd"),v0(e)]);i.default.encodingExists(r)||(console.log(`Unsupported terminal encoding: ${r}, falling back to UTF-8.`),r="utf8");const n=new V1,a=i.default.getDecoder(r);process.stdin.on("data",o=>{const l=a.write(o);n.queue(()=>Jt.promises.appendFile(e,l))}),process.stdin.on("end",()=>{const o=a.end();n.queue(async()=>{try{typeof o=="string"&&await Jt.promises.appendFile(e,o)}finally{s?.()}})})}var _0=O({"out-build/vs/platform/environment/node/stdin.js"(){"use strict";dt(),Ue(),g0()}});import{writeFileSync as $0}from"fs";import{tmpdir as C0}from"os";function A0(e){const t=ht(C0());try{return $0(t,""),e&&console.log(`Marker file for --wait created: ${t}`),t}catch(s){e&&console.error(`Failed to create marker file for --wait: ${s}`);return}}var O0=O({"out-build/vs/platform/environment/node/wait.js"(){"use strict";Ue()}}),se,Qt,re,S0=O({"out-build/vs/platform/product/common/product.js"(){"use strict";if(C1(),Qt=globalThis.vscode,typeof Qt<"u"&&typeof Qt.context<"u"){const e=Qt.context.configuration();if(e)se=e.product;else throw new Error("Sandbox: unable to resolve product configuration from preload script.")}else if(globalThis._VSCODE_PRODUCT_JSON&&globalThis._VSCODE_PACKAGE_JSON){if(se=globalThis._VSCODE_PRODUCT_JSON,$1.VSCODE_DEV&&Object.assign(se,{nameShort:`${se.nameShort} Dev`,nameLong:`${se.nameLong} Dev`,dataFolderName:`${se.dataFolderName}-dev`,serverDataFolderName:se.serverDataFolderName?`${se.serverDataFolderName}-dev`:void 0}),!se.version){const e=globalThis._VSCODE_PACKAGE_JSON;Object.assign(se,{version:e.version})}}else se={},Object.keys(se).length===0&&Object.assign(se,{version:"1.95.0-dev",nameShort:"Code - OSS Dev",nameLong:"Code - OSS Dev",applicationName:"code-oss",dataFolderName:".vscode-oss",urlProtocol:"code-oss",reportIssueUrl:"https://github.com/microsoft/vscode/issues/new",licenseName:"MIT",licenseUrl:"https://github.com/microsoft/vscode/blob/main/LICENSE.txt",serverLicenseUrl:"https://github.com/microsoft/vscode/blob/main/LICENSE.txt"});re=se}}),x0,as,D0=O({"out-build/vs/platform/profiling/common/profiling.js"(){"use strict";ae(),Wi(),x0=rs("IV8InspectProfilingService"),function(e){function t(r){return!!(r.samples&&r.timeDeltas)}e.isValidProfile=t;function s(r,i="noAbsolutePaths"){for(const n of r.nodes)n.callFrame&&n.callFrame.url&&(He(n.callFrame.url)||/^\w[\w\d+.-]*:\/\/\/?/.test(n.callFrame.url))&&(n.callFrame.url=te(i,lt(n.callFrame.url)));return r}e.rewriteAbsolutePaths=s}(as||(as={}))}});function P0(){return process.uncHostAllowlist}function hn(e){if(process.platform!=="win32")return;const t=P0();if(t)if(typeof e=="string")t.add(e.toLowerCase());else for(const s of L0(e))hn(s)}function L0(e){const t=new Set;if(Array.isArray(e))for(const s of e)typeof s=="string"&&t.add(s);return Array.from(t)}var k0=O({"out-build/vs/base/node/unc.js"(){"use strict"}}),dn={};Mn(dn,{$ju:()=>wn});import{spawn as Yt}from"child_process";import{chmodSync as pn,existsSync as ls,readFileSync as gn,statSync as Zt,truncateSync as N0,unlinkSync as mn}from"fs";import{homedir as R0,release as T0,tmpdir as I0}from"os";function j0(e){return!!e["install-source"]||!!e["list-extensions"]||!!e["install-extension"]||!!e["uninstall-extension"]||!!e["update-extensions"]||!!e["locate-extension"]||!!e["add-mcp"]||!!e.telemetry}async function wn(e){let t;try{t=f0(e)}catch(s){console.error(s.message);return}for(const s of Kt)if(t[s]){if(!re.tunnelApplicationName){console.error(`'${s}' command not supported in ${re.applicationName}`);return}const r={...process.env};delete r.ELECTRON_RUN_AS_NODE;const i=e.slice(e.indexOf(s)+1);return new Promise((n,a)=>{let o;const l=["ignore","pipe","pipe"];if(process.env.VSCODE_DEV)o=Yt("cargo",["run","--",s,...i],{cwd:te(bn(),"cli"),stdio:l,env:r});else{const c=process.platform==="darwin"?te(je(je(process.execPath)),"Resources","app"):je(process.execPath),f=te(c,"bin",`${re.tunnelApplicationName}${W?".exe":""}`);o=Yt(f,[s,...i],{cwd:ze(),stdio:l,env:r})}o.stdout.pipe(process.stdout),o.stderr.pipe(process.stderr),o.on("exit",n),o.on("error",a)})}if(t.help){const s=`${re.applicationName}${W?".exe":""}`;console.log(o0(re.nameLong,s,re.version,is))}else if(t.version)console.log(a0(re.version,re.commit));else if(t["locate-shell-integration-path"]){let s;switch(t["locate-shell-integration-path"]){case"bash":s="shellIntegration-bash.sh";break;case"pwsh":s="shellIntegration.ps1";break;case"zsh":s="shellIntegration-rc.zsh";break;case"fish":s="shellIntegration.fish";break;default:throw new Error("Error using --locate-shell-integration-path: Invalid shell type")}console.log(te(bn(),"out","vs","workbench","contrib","terminal","common","scripts",s))}else if(j0(t)){let s;process.env.VSCODE_DEV?s="./cliProcessMain.js":s="./vs/code/node/cliProcessMain.js",await(await import(s)).main(t);return}else if(t["file-write"]){const s=t._[0];if(!s||!He(s)||!ls(s)||!Zt(s).isFile())throw new Error("Using --file-write with invalid arguments.");let r,i;try{const n=JSON.parse(gn(s,"utf8"));r=n.source,i=n.target}catch{throw new Error("Using --file-write with invalid arguments.")}if(W)for(const n of[r,i])typeof n=="string"&&jo(n)&&hn(K.file(n).authority);if(!r||!i||r===i||!He(r)||!He(i)||!ls(r)||!Zt(r).isFile()||!ls(i)||!Zt(i).isFile())throw new Error("Using --file-write with invalid arguments.");try{let n=0,a=!1;t["file-chmod"]&&(n=Zt(i).mode,n&128||(pn(i,n|128),a=!0));const o=gn(r);W?(N0(i,0),Ke(i,o,{flag:"r+"})):Ke(i,o),a&&pn(i,n)}catch(n){throw n.message=`Error using --file-write: ${n.message}`,n}}else{const s={...process.env,ELECTRON_NO_ATTACH_CONSOLE:"1"};delete s.ELECTRON_RUN_AS_NODE;const r=[];t.verbose&&(s.ELECTRON_ENABLE_LOGGING="1"),(t.verbose||t.status)&&r.push(async f=>{f.stdout?.on("data",u=>console.log(u.toString("utf8").trim())),f.stderr?.on("data",u=>console.log(u.toString("utf8").trim())),await ne.toPromise(ne.fromNodeEventEmitter(f,"exit"))});const i=t._.some(f=>f==="-");i&&(t._=t._.filter(f=>f!=="-"),e=e.filter(f=>f!=="-"));let n;if(w0())if(i){n=y0();try{const f=new B1;await E0(n,!!t.verbose,()=>f.complete()),t.wait||r.push(()=>f.p),De(e,n),De(e,"--skip-add-to-recently-opened"),console.log(`Reading from stdin via: ${n}`)}catch(f){console.log(`Failed to create file to read via stdin: ${f.toString()}`),n=void 0}}else r.push(f=>b0(1e3).then(u=>{u&&console.log(W?`Run with '${re.applicationName} -' to read output from another program (e.g. 'echo Hello World | ${re.applicationName} -').`:`Run with '${re.applicationName} -' to read from stdin (e.g. 'ps aux | grep code | ${re.applicationName} -').`)}));const a=Te&&T0()>"20.0.0";let o;if(t.wait&&(o=A0(t.verbose),o&&De(e,"--waitMarkerFilePath",o),r.push(async f=>{let u;a?u=new Promise(h=>{f.on("exit",(d,p)=>{(d!==0||p)&&h()})}):u=ne.toPromise(ne.fromNodeEventEmitter(f,"exit"));try{await Promise.race([mi(o),ne.toPromise(ne.fromNodeEventEmitter(f,"error")),u])}finally{n&&mn(n)}})),t["prof-startup"]){const f="127.0.0.1",u=await J1(Co(),10,3e3),h=await J1(u+1,10,3e3),d=await J1(h+1,10,3e3);if(u*h*d===0)throw new Error("Failed to find free ports for profiler. Make sure to shutdown all instances of the editor first.");const p=ht(R0(),"prof");De(e,`--inspect-brk=${f}:${u}`),De(e,`--remote-debugging-port=${f}:${h}`),De(e,`--inspect-brk-extensions=${f}:${d}`),De(e,"--prof-startup-prefix",p),De(e,"--no-cached-data"),Ke(p,e.slice(-6).join("|")),r.push(async y=>{class E{static async start(N,k,X){const _=await import("v8-inspect-profiler");let $;try{$=await _.startProfiling({...X,host:f})}catch{console.error(`FAILED to start profiling for '${N}' on port '${X.port}'`)}return{async stop(){if(!$)return;let x="";const P=await $.stop();process.env.VSCODE_DEV||(P.profile=as.rewriteAbsolutePaths(P.profile,"piiRemoved"),x=".txt"),Ke(`${k}.${N}.cpuprofile${x}`,JSON.stringify(P.profile,void 0,4))}}}}try{const C=E.start("main",p,{port:u}),N=E.start("extHost",p,{port:d,tries:300}),k=E.start("renderer",p,{port:h,tries:200,target:function(x){return x.filter(P=>P.webSocketDebuggerUrl?P.type==="page"?P.url.indexOf("workbench/workbench.html")>0||P.url.indexOf("workbench/workbench-dev.html")>0:!0:!1)[0]}}),X=await C,_=await N,$=await k;await mi(p),await X.stop(),await $.stop(),await _.stop(),Ke(p,"")}catch{console.error("Failed to profile startup. Make sure to quit Code first.")}})}const l={detached:!0,env:s};t.verbose||(l.stdio="ignore");let c;if(!a)!t.verbose&&t.status&&(l.stdio=["ignore","pipe","ignore"]),c=Yt(process.execPath,e.slice(2),l);else{const f=["-n","-g"];if(f.push("-a",process.execPath),t.verbose||t.status){f.push("--wait-apps");for(const u of t.verbose?["stdout","stderr"]:["stdout"]){const h=ht(I0(),`code-${u}`);Ke(h,""),f.push(`--${u}`,h),r.push(async d=>{try{const p=u==="stdout"?process.stdout:process.stderr,y=new Ge;d.on("close",()=>{setTimeout(()=>y.dispose(!0),200)}),await e0(h,E=>p.write(E),()=>{},y.token)}finally{mn(h)}})}}for(const u in s)u!=="_"&&(f.push("--env"),f.push(`${u}=${s[u]}`));if(f.push("--args",...e.slice(2)),s.VSCODE_DEV){const u=".",h=f.indexOf(u);h!==-1&&(f[h]=S1(u))}c=Yt("open",f,{...l,env:{}})}return Promise.all(r.map(f=>f(c)))}}function bn(){return je(Xr.asFileUri("").fsPath)}function yn(e){setTimeout(()=>process.exit(e),0)}var F0=O({"out-build/vs/code/node/cli.js"(){"use strict";y1(),ae(),J(),Ao(),K1(),A2(),t0(),cn(),h0(),_0(),O0(),S0(),Ut(),Ue(),D0(),T1(),C1(),k0(),qe(),dt(),wn(process.argv).then(()=>yn(0)).then(null,e=>{console.error(e.message||e.stack||e),yn(1)})}});delete process.env.VSCODE_CWD;import{dirname as M0}from"path";import{fileURLToPath as U0}from"url";import*as ce from"path";import*as vn from"fs";import{fileURLToPath as V0}from"url";import{createRequire as q0}from"node:module";var Sa=q0(import.meta.url),W0=ce.dirname(V0(import.meta.url)),xa=process.platform==="win32";if(Error.stackTraceLimit=100,!process.env.VSCODE_HANDLES_SIGPIPE){let e=!1;process.on("SIGPIPE",()=>{e||(e=!0,console.error(new Error("Unexpected SIGPIPE")))})}function B0(){try{typeof process.env.VSCODE_CWD!="string"&&(process.env.VSCODE_CWD=process.cwd()),process.platform==="win32"&&process.chdir(ce.dirname(process.execPath))}catch(e){console.error(e)}}B0();function z0(e){const t=ce.dirname(W0);function s(){return process.env.VSCODE_DEV?t:process.platform==="darwin"?ce.dirname(ce.dirname(ce.dirname(t))):ce.dirname(ce.dirname(t))}function r(){if(process.env.VSCODE_PORTABLE)return process.env.VSCODE_PORTABLE;if(process.platform==="win32"||process.platform==="linux")return ce.join(s(),"data");const l=e.portable||`${e.applicationName}-portable-data`;return ce.join(ce.dirname(s()),l)}const i=r(),n=!("target"in e)&&vn.existsSync(i),a=ce.join(i,"tmp"),o=n&&vn.existsSync(a);return n?process.env.VSCODE_PORTABLE=i:delete process.env.VSCODE_PORTABLE,o&&(process.platform==="win32"?(process.env.TMP=a,process.env.TEMP=a):process.env.TMPDIR=a),{portableDataPath:i,isPortable:n}}import*as H0 from"path";import*as cs from"fs";import{fileURLToPath as G0}from"url";import{createRequire as K0,register as J0}from"node:module";import{createRequire as Q0}from"node:module";var En=Q0(import.meta.url),us={BUILD_INSERT_PRODUCT_CONFIGURATION:"BUILD_INSERT_PRODUCT_CONFIGURATION"};us.BUILD_INSERT_PRODUCT_CONFIGURATION&&(us=En("../product.json"));var fs={"name":"Code","version":"1.99.3","distro":"21c8d8ea1e46d97c5639a7cabda6c0e063cc8dd5","author":{"name":"Microsoft Corporation"},"license":"MIT","main":"./out/main.js","type":"module","private":true,"scripts":{"test":"echo Please run any of the test scripts from the scripts folder.","test-browser":"npx playwright install && node test/unit/browser/index.js","test-browser-no-install":"node test/unit/browser/index.js","test-node":"mocha test/unit/node/index.js --delay --ui=tdd --timeout=5000 --exit","test-extension":"vscode-test","preinstall":"node build/npm/preinstall.js","postinstall":"node build/npm/postinstall.js","compile":"node ./node_modules/gulp/bin/gulp.js compile","watch":"npm-run-all -lp watch-client watch-extensions","watchd":"deemon npm run watch","watch-webd":"deemon npm run watch-web","kill-watchd":"deemon --kill npm run watch","kill-watch-webd":"deemon --kill npm run watch-web","restart-watchd":"deemon --restart npm run watch","restart-watch-webd":"deemon --restart npm run watch-web","watch-client":"node --max-old-space-size=8192 ./node_modules/gulp/bin/gulp.js watch-client","watch-clientd":"deemon npm run watch-client","kill-watch-clientd":"deemon --kill npm run watch-client","watch-extensions":"node --max-old-space-size=8192 ./node_modules/gulp/bin/gulp.js watch-extensions watch-extension-media","watch-extensionsd":"deemon npm run watch-extensions","kill-watch-extensionsd":"deemon --kill npm run watch-extensions","precommit":"node build/hygiene.js","gulp":"node --max-old-space-size=8192 ./node_modules/gulp/bin/gulp.js","electron":"node build/lib/electron","7z":"7z","update-grammars":"node build/npm/update-all-grammars.mjs","update-localization-extension":"node build/npm/update-localization-extension.js","smoketest":"node build/lib/preLaunch.js && cd test/smoke && npm run compile && node test/index.js","smoketest-no-compile":"cd test/smoke && node test/index.js","download-builtin-extensions":"node build/lib/builtInExtensions.js","download-builtin-extensions-cg":"node build/lib/builtInExtensionsCG.js","monaco-compile-check":"tsc -p src/tsconfig.monaco.json --noEmit","tsec-compile-check":"node node_modules/tsec/bin/tsec -p src/tsconfig.tsec.json","vscode-dts-compile-check":"tsc -p src/tsconfig.vscode-dts.json && tsc -p src/tsconfig.vscode-proposed-dts.json","valid-layers-check":"node build/lib/layersChecker.js","property-init-order-check":"node build/lib/propertyInitOrderChecker.js","update-distro":"node build/npm/update-distro.mjs","web":"echo 'npm run web' is replaced by './scripts/code-server' or './scripts/code-web'","compile-cli":"gulp compile-cli","compile-web":"node ./node_modules/gulp/bin/gulp.js compile-web","watch-web":"node ./node_modules/gulp/bin/gulp.js watch-web","watch-cli":"node ./node_modules/gulp/bin/gulp.js watch-cli","eslint":"node build/eslint","stylelint":"node build/stylelint","playwright-install":"npm exec playwright install","compile-build":"node ./node_modules/gulp/bin/gulp.js compile-build-with-mangling","compile-extensions-build":"node ./node_modules/gulp/bin/gulp.js compile-extensions-build","minify-vscode":"node ./node_modules/gulp/bin/gulp.js minify-vscode","minify-vscode-reh":"node ./node_modules/gulp/bin/gulp.js minify-vscode-reh","minify-vscode-reh-web":"node ./node_modules/gulp/bin/gulp.js minify-vscode-reh-web","hygiene":"node ./node_modules/gulp/bin/gulp.js hygiene","core-ci":"node ./node_modules/gulp/bin/gulp.js core-ci","core-ci-pr":"node ./node_modules/gulp/bin/gulp.js core-ci-pr","extensions-ci":"node ./node_modules/gulp/bin/gulp.js extensions-ci","extensions-ci-pr":"node ./node_modules/gulp/bin/gulp.js extensions-ci-pr","perf":"node scripts/code-perf.js","update-build-ts-version":"npm install typescript@next && tsc -p ./build/tsconfig.build.json"},"dependencies":{"@c4312/eventsource-umd":"^3.0.5","@microsoft/1ds-core-js":"^3.2.13","@microsoft/1ds-post-js":"^3.2.13","@parcel/watcher":"2.5.1","@types/semver":"^7.5.8","@vscode/deviceid":"^0.1.1","@vscode/iconv-lite-umd":"0.7.0","@vscode/policy-watcher":"^1.3.0","@vscode/proxy-agent":"^0.32.0","@vscode/ripgrep":"^1.15.11","@vscode/spdlog":"^0.15.0","@vscode/sqlite3":"5.1.8-vscode","@vscode/sudo-prompt":"9.3.1","@vscode/tree-sitter-wasm":"^0.1.4","@vscode/vscode-languagedetection":"1.0.21","@vscode/windows-mutex":"^0.5.0","@vscode/windows-process-tree":"^0.6.0","@vscode/windows-registry":"^1.1.0","@xterm/addon-clipboard":"^0.2.0-beta.82","@xterm/addon-image":"^0.9.0-beta.99","@xterm/addon-ligatures":"^0.10.0-beta.99","@xterm/addon-progress":"^0.2.0-beta.5","@xterm/addon-search":"^0.16.0-beta.99","@xterm/addon-serialize":"^0.14.0-beta.99","@xterm/addon-unicode11":"^0.9.0-beta.99","@xterm/addon-webgl":"^0.19.0-beta.99","@xterm/headless":"^5.6.0-beta.99","@xterm/xterm":"^5.6.0-beta.99","http-proxy-agent":"^7.0.0","https-proxy-agent":"^7.0.2","jschardet":"3.1.4","kerberos":"2.1.1","minimist":"^1.2.6","native-is-elevated":"0.7.0","native-keymap":"^3.3.5","native-watchdog":"^1.4.1","node-pty":"^1.1.0-beta33","open":"^8.4.2","tas-client-umd":"0.2.0","v8-inspect-profiler":"^0.1.1","vscode-oniguruma":"1.7.0","vscode-regexpp":"^3.1.0","vscode-textmate":"9.2.0","yauzl":"^3.0.0","yazl":"^2.4.3"},"devDependencies":{"@playwright/test":"^1.50.0","@stylistic/eslint-plugin-ts":"^2.8.0","@types/cookie":"^0.3.3","@types/debug":"^4.1.5","@types/eslint":"^9.6.1","@types/gulp-svgmin":"^1.2.1","@types/http-proxy-agent":"^2.0.1","@types/kerberos":"^1.1.2","@types/minimist":"^1.2.1","@types/mocha":"^9.1.1","@types/node":"20.x","@types/sinon":"^10.0.2","@types/sinon-test":"^2.4.2","@types/trusted-types":"^1.0.6","@types/vscode-notebook-renderer":"^1.72.0","@types/webpack":"^5.28.5","@types/wicg-file-system-access":"^2020.9.6","@types/windows-foreground-love":"^0.3.0","@types/winreg":"^1.2.30","@types/yauzl":"^2.10.0","@types/yazl":"^2.4.2","@typescript-eslint/utils":"^8.8.0","@vscode/gulp-electron":"^1.36.0","@vscode/l10n-dev":"0.0.35","@vscode/telemetry-extractor":"^1.10.2","@vscode/test-cli":"^0.0.6","@vscode/test-electron":"^2.4.0","@vscode/test-web":"^0.0.62","@vscode/v8-heap-parser":"^0.1.0","@vscode/vscode-perf":"^0.0.19","@webgpu/types":"^0.1.44","ansi-colors":"^3.2.3","asar":"^3.0.3","chromium-pickle-js":"^0.2.0","cookie":"^0.7.2","copy-webpack-plugin":"^11.0.0","css-loader":"^6.9.1","cssnano":"^6.0.3","debounce":"^1.0.0","deemon":"^1.8.0","electron":"34.3.2","eslint":"^9.11.1","eslint-formatter-compact":"^8.40.0","eslint-plugin-header":"3.1.1","eslint-plugin-jsdoc":"^50.3.1","eslint-plugin-local":"^6.0.0","event-stream":"3.3.4","fancy-log":"^1.3.3","file-loader":"^6.2.0","glob":"^5.0.13","gulp":"^4.0.0","gulp-azure-storage":"^0.12.1","gulp-bom":"^3.0.0","gulp-buffer":"0.0.2","gulp-filter":"^5.1.0","gulp-flatmap":"^1.0.2","gulp-gunzip":"^1.0.0","gulp-gzip":"^1.4.2","gulp-json-editor":"^2.5.0","gulp-plumber":"^1.2.0","gulp-rename":"^1.2.0","gulp-replace":"^0.5.4","gulp-sourcemaps":"^3.0.0","gulp-svgmin":"^4.1.0","gulp-untar":"^0.0.7","husky":"^0.13.1","innosetup":"^6.4.1","istanbul-lib-coverage":"^3.2.0","istanbul-lib-instrument":"^6.0.1","istanbul-lib-report":"^3.0.0","istanbul-lib-source-maps":"^4.0.1","istanbul-reports":"^3.1.5","lazy.js":"^0.4.2","merge-options":"^1.0.1","mime":"^1.4.1","minimatch":"^3.0.4","minimist":"^1.2.6","mocha":"^10.8.2","mocha-junit-reporter":"^2.2.1","mocha-multi-reporters":"^1.5.1","npm-run-all":"^4.1.5","os-browserify":"^0.3.0","p-all":"^1.0.0","path-browserify":"^1.0.1","postcss":"^8.4.33","postcss-nesting":"^12.0.2","pump":"^1.0.1","rcedit":"^1.1.0","rimraf":"^2.2.8","sinon":"^12.0.1","sinon-test":"^3.1.3","source-map":"0.6.1","source-map-support":"^0.3.2","style-loader":"^3.3.2","ts-loader":"^9.5.1","ts-node":"^10.9.1","tsec":"0.2.7","tslib":"^2.6.3","typescript":"^5.8.0-dev.20250207","typescript-eslint":"^8.8.0","util":"^0.12.4","webpack":"^5.94.0","webpack-cli":"^5.1.4","webpack-stream":"^7.0.0","xml2js":"^0.5.0","yaserver":"^0.4.0"},"overrides":{"node-gyp-build":"4.8.1","kerberos@2.1.1":{"node-addon-api":"7.1.0"}},"repository":{"type":"git","url":"https://github.com/microsoft/vscode.git"},"bugs":{"url":"https://github.com/microsoft/vscode/issues"},"optionalDependencies":{"windows-foreground-love":"0.5.0"}};fs.BUILD_INSERT_PACKAGE_CONFIGURATION&&(fs=En("../package.json"));var hs=us,Y0=fs;function ds(e){const t=[];typeof e=="number"&&t.push("code/timeOrigin",e);function s(i,n){t.push(i,n?.startTime??Date.now())}function r(){const i=[];for(let n=0;n<t.length;n+=2)i.push({name:t[n],startTime:t[n+1]});return i}return{mark:s,getMarks:r}}function Z0(){if(typeof performance=="object"&&typeof performance.mark=="function"&&!performance.nodeTiming)return typeof performance.timeOrigin!="number"&&!performance.timing?ds():{mark(e,t){performance.mark(e,t)},getMarks(){let e=performance.timeOrigin;typeof e!="number"&&(e=performance.timing.navigationStart||performance.timing.redirectStart||performance.timing.fetchStart);const t=[{name:"code/timeOrigin",startTime:Math.round(e)}];for(const s of performance.getEntriesByType("mark"))t.push({name:s.name,startTime:Math.round(e+s.startTime)});return t}};if(typeof process=="object"){const e=performance?.timeOrigin;return ds(e)}else return console.trace("perf-util loaded in UNKNOWN environment"),ds()}function X0(e){return e.MonacoPerformanceMarks||(e.MonacoPerformanceMarks=Z0()),e.MonacoPerformanceMarks}var _n=X0(globalThis),Qe=_n.mark,ka=_n.getMarks,ea=K0(import.meta.url),ta=H0.dirname(G0(import.meta.url));if((process.env.ELECTRON_RUN_AS_NODE||process.versions.electron)&&J0(`data:text/javascript;base64,${Buffer.from(`
	export async function resolve(specifier, context, nextResolve) {
		if (specifier === 'fs') {
			return {
				format: 'builtin',
				shortCircuit: true,
				url: 'node:original-fs'
			};
		}

		// Defer to the next hook in the chain, which would be the
		// Node.js default resolve if this is the last user-specified loader.
		return nextResolve(specifier, context);
	}`).toString("base64")}`,import.meta.url),globalThis._VSCODE_PRODUCT_JSON={...hs},process.env.VSCODE_DEV)try{const e=ea("../product.overrides.json");globalThis._VSCODE_PRODUCT_JSON=Object.assign(globalThis._VSCODE_PRODUCT_JSON,e)}catch{}globalThis._VSCODE_PACKAGE_JSON={...Y0},globalThis._VSCODE_FILE_ROOT=ta;var ps=void 0;function sa(){return ps||(ps=ra()),ps}async function ra(){Qe("code/willLoadNls");let e,t;if(process.env.VSCODE_NLS_CONFIG)try{e=JSON.parse(process.env.VSCODE_NLS_CONFIG),e?.languagePack?.messagesFile?t=e.languagePack.messagesFile:e?.defaultMessagesFile&&(t=e.defaultMessagesFile),globalThis._VSCODE_NLS_LANGUAGE=e?.resolvedLanguage}catch(s){console.error(`Error reading VSCODE_NLS_CONFIG from environment: ${s}`)}if(!(process.env.VSCODE_DEV||!t)){try{globalThis._VSCODE_NLS_MESSAGES=JSON.parse((await cs.promises.readFile(t)).toString())}catch(s){if(console.error(`Error reading NLS messages file ${t}: ${s}`),e?.languagePack?.corruptMarkerFile)try{await cs.promises.writeFile(e.languagePack.corruptMarkerFile,"corrupted")}catch(r){console.error(`Error writing corrupted NLS marker file: ${r}`)}if(e?.defaultMessagesFile&&e.defaultMessagesFile!==t)try{globalThis._VSCODE_NLS_MESSAGES=JSON.parse((await cs.promises.readFile(e.defaultMessagesFile)).toString())}catch(r){console.error(`Error reading default NLS messages file ${e.defaultMessagesFile}: ${r}`)}}return Qe("code/didLoadNls"),e}}async function ia(){await sa()}import*as be from"path";import*as ye from"fs";async function na({userLocale:e,osLocale:t,userDataPath:s,commit:r,nlsMetadataPath:i}){if(Qe("code/willGenerateNls"),process.env.VSCODE_DEV||e==="pseudo"||e.startsWith("en")||!r||!s)return vt(e,t,i);try{const n=await oa(s);if(!n)return vt(e,t,i);const a=aa(n,e);if(!a)return vt(e,t,i);const o=n[a],l=o?.translations?.vscode;if(!o||typeof o.hash!="string"||!o.translations||typeof l!="string"||!await gs(l))return vt(e,t,i);const c=`${o.hash}.${a}`,f=be.join(s,"clp",c),u=be.join(f,r),h=be.join(u,"nls.messages.json"),d=be.join(f,"tcf.json"),p=be.join(f,"corrupted.info");await gs(p)&&await ye.promises.rm(f,{recursive:!0,force:!0,maxRetries:3});const y={userLocale:e,osLocale:t,resolvedLanguage:a,defaultMessagesFile:be.join(i,"nls.messages.json"),languagePack:{translationsConfigFile:d,messagesFile:h,corruptMarkerFile:p},locale:e,availableLanguages:{"*":a},_languagePackId:c,_languagePackSupport:!0,_translationsConfigFile:d,_cacheRoot:f,_resolvedLanguagePackCoreLocation:u,_corruptedFile:p};if(await gs(u))return la(u).catch(()=>{}),Qe("code/didGenerateNls"),y;const[,E,C,N]=await Promise.all([ye.promises.mkdir(u,{recursive:!0}),JSON.parse(await ye.promises.readFile(be.join(i,"nls.keys.json"),"utf-8")),JSON.parse(await ye.promises.readFile(be.join(i,"nls.messages.json"),"utf-8")),JSON.parse(await ye.promises.readFile(l,"utf-8"))]),k=[];let X=0;for(const[_,$]of E){const x=N.contents[_];for(const P of $)k.push(x?.[P]||C[X]),X++}return await Promise.all([ye.promises.writeFile(h,JSON.stringify(k),"utf-8"),ye.promises.writeFile(d,JSON.stringify(o.translations),"utf-8")]),Qe("code/didGenerateNls"),y}catch(n){console.error("Generating translation files failed.",n)}return vt(e,t,i)}async function oa(e){const t=be.join(e,"languagepacks.json");try{return JSON.parse(await ye.promises.readFile(t,"utf-8"))}catch{return}}function aa(e,t){try{for(;t;){if(e[t])return t;const s=t.lastIndexOf("-");if(s>0)t=t.substring(0,s);else return}}catch(s){console.error("Resolving language pack configuration failed.",s)}}function vt(e,t,s){return Qe("code/didGenerateNls"),{userLocale:e,osLocale:t,resolvedLanguage:"en",defaultMessagesFile:be.join(s,"nls.messages.json"),locale:e,availableLanguages:{}}}async function gs(e){try{return await ye.promises.access(e),!0}catch{return!1}}function la(e){const t=new Date;return ye.promises.utimes(e,t,t)}var ca=M0(U0(import.meta.url)),ua=await na({userLocale:"en",osLocale:"en",commit:hs.commit,userDataPath:"",nlsMetadataPath:ca});process.env.VSCODE_NLS_CONFIG=JSON.stringify(ua),z0(hs),process.env.VSCODE_CLI="1",await ia(),await Promise.resolve().then(()=>(F0(),dn));

//# sourceMappingURL=https://main.vscode-cdn.net/sourcemaps/17baf841131aa23349f217ca7c570c76ee87b957/core/cli.js.map
