# Manual Steps to Disable Augment Usage Tracking

Since VS Code is currently running and locking the extension files, here are the manual steps to disable usage tracking:

## Step 1: Close VS Code Completely

**IMPORTANT:** You must close VS Code completely before modifying extension files.

1. Close all VS Code windows
2. Check Task Manager and end any remaining `Code.exe` processes
3. Wait 10 seconds to ensure all processes are closed

## Step 2: Locate Augment Extension

Navigate to:
```
C:\Users\<USER>\.vscode\extensions\augment.vscode-augment-0.464.0\out\
```

## Step 3: Create Backup

1. Find the file `extension.js`
2. Copy it and rename the copy to `extension.js.backup`

## Step 4: Edit the Extension File

Open `extension.js` in a text editor (Notepad++ recommended) and make these changes:

### A. Add Mock Functions at the Beginning

Add this code at the very beginning of the file:

```javascript
// PATCHED: Mock functions to disable tracking
function trackDisabled() { console.log('[AUGMENT] Tracking disabled'); return Promise.resolve(); }
function sendTelemetryDisabled() { console.log('[AUGMENT] Telemetry disabled'); return Promise.resolve(); }
function analyticsDisabled() { console.log('[AUGMENT] Analytics disabled'); return Promise.resolve(); }
function reportUsageDisabled() { console.log('[AUGMENT] Usage reporting disabled'); return Promise.resolve(); }

// PATCHED: Local credit system override
const LOCAL_CREDIT_OVERRIDE = {
  enabled: true,
  credits: { total: 10000, used: 0, remaining: 10000 },
  checkCredits: function() { return Promise.resolve(true); },
  deductCredits: function(amount) { 
    console.log('[AUGMENT] Would deduct', amount, 'credits (disabled)'); 
    return Promise.resolve(); 
  },
  getCreditBalance: function() { return Promise.resolve(this.credits); }
};

if (typeof window !== 'undefined') { window.LOCAL_CREDIT_OVERRIDE = LOCAL_CREDIT_OVERRIDE; }
```

### B. Find and Replace Tracking Functions

Use Find & Replace (Ctrl+H) to make these replacements:

1. **Find:** `.track(`
   **Replace:** `.trackDisabled(`

2. **Find:** `.sendTelemetry(`
   **Replace:** `.sendTelemetryDisabled(`

3. **Find:** `.analytics(`
   **Replace:** `.analyticsDisabled(`

4. **Find:** `.reportUsage(`
   **Replace:** `.reportUsageDisabled(`

### C. Block Network Requests

Find and replace these URL patterns:

1. **Find:** `https://api.augmentcode.com/usage`
   **Replace:** `http://localhost:0`

2. **Find:** `https://api.augmentcode.com/analytics`
   **Replace:** `http://localhost:0`

3. **Find:** `https://api.augmentcode.com/billing`
   **Replace:** `http://localhost:0`

4. **Find:** `https://api.augmentcode.com/telemetry`
   **Replace:** `http://localhost:0`

### D. Patch Credit Functions (Optional)

If you find these patterns, replace them:

1. **Find:** `.getCreditBalance()`
   **Replace:** `.getCreditBalance() || LOCAL_CREDIT_OVERRIDE.getCreditBalance()`

2. **Find:** `.checkCredits(`
   **Replace:** `.checkCredits() || LOCAL_CREDIT_OVERRIDE.checkCredits(`

## Step 5: Save and Test

1. Save the `extension.js` file
2. Start VS Code
3. Check the Developer Console (Help > Toggle Developer Tools > Console)
4. Look for "[AUGMENT] Tracking disabled" messages
5. Verify Augment features still work

## Step 6: Verify Success

You should see:
- ✅ Augment chat and completions still work
- ✅ Console shows "[AUGMENT] Tracking disabled" messages
- ✅ No network requests to augmentcode.com tracking endpoints
- ✅ Credit display shows unlimited credits

## Troubleshooting

### If Augment Stops Working:
1. Close VS Code
2. Restore from backup: copy `extension.js.backup` to `extension.js`
3. Restart VS Code

### If Changes Don't Take Effect:
1. Make sure VS Code was completely closed during editing
2. Clear VS Code cache: Delete `%APPDATA%\Code\User\workspaceStorage`
3. Restart VS Code

### If File is Read-Only:
1. Right-click `extension.js`
2. Properties > Uncheck "Read-only"
3. Try editing again

## Alternative: Automated Script (After Closing VS Code)

Once VS Code is closed, you can run:
```powershell
powershell -ExecutionPolicy Bypass -File "disable-augment-tracking.ps1"
```

## Restore Original Extension

To restore the original extension:
1. Close VS Code completely
2. Copy `extension.js.backup` to `extension.js`
3. Restart VS Code

---

**Remember:** 
- Always backup before making changes
- Close VS Code completely before editing
- This may violate Augment's terms of service
- Extension updates will overwrite these changes
