{"name": "@xterm/addon-clipboard", "version": "0.2.0-beta.82", "author": {"name": "The xterm.js authors", "url": "https://xtermjs.org/"}, "main": "lib/addon-clipboard.js", "module": "lib/addon-clipboard.mjs", "types": "typings/addon-clipboard.d.ts", "repository": "https://github.com/xtermjs/xterm.js/tree/master/addons/addon-clipboard", "license": "MIT", "keywords": ["terminal", "xterm", "xterm.js"], "scripts": {"build": "../../node_modules/.bin/tsc -p .", "prepackage": "npm run build", "package": "../../node_modules/.bin/webpack", "prepublishOnly": "npm run package", "start": "node ../../demo/start"}, "peerDependencies": {"@xterm/xterm": "^5.6.0-beta.99"}, "dependencies": {"js-base64": "^3.7.5"}, "commit": "a260f7d2889142d6566a66cb9856a07050dea611"}