FIND & REPLACE INSTRUCTIONS FOR AUGMENT EXTENSION.JS
=====================================================

Open extension.js in Notepad++ or VS Code, then use Find & Replace (Ctrl+H) for each of these:

1. DISABLE TRACKING FUNCTIONS:
   Find: .track(
   Replace: .trackDisabled(

2. DISABLE TELEMETRY:
   Find: .sendTelemetry(
   Replace: .sendTelemetryDisabled(

3. DISABLE ANALYTICS:
   Find: .analytics(
   Replace: .analyticsDisabled(

4. DISABLE USAGE REPORTING:
   Find: .reportUsage(
   Replace: .reportUsageDisabled(

5. BLOCK USAGE ENDPOINT:
   Find: https://api.augmentcode.com/usage
   Replace: http://localhost:0

6. BLOCK ANALYTICS ENDPOINT:
   Find: https://api.augmentcode.com/analytics
   Replace: http://localhost:0

7. BLOCK BILLING ENDPOINT:
   Find: https://api.augmentcode.com/billing
   Replace: http://localhost:0

8. BLOCK TELEMETRY ENDPOINT:
   Find: https://api.augmentcode.com/telemetry
   Replace: http://localhost:0

9. PATCH CREDIT BALANCE (Optional):
   Find: .getCreditBalance()
   Replace: .getCreditBalance() || LOCAL_CREDIT_OVERRIDE.getCreditBalance()

10. PATCH CREDIT CHECK (Optional):
    Find: .checkCredits(
    Replace: .checkCredits() || LOCAL_CREDIT_OVERRIDE.checkCredits(

NOTES:
- Make sure to backup the original file first!
- Some patterns might not exist in your version - that's OK
- The important ones are #1-8
- After saving, restart VS Code completely
