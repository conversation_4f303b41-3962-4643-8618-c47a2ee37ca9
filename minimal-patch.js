// MINIMAL PATCH - Add this to the BEGINNING of your original extension.js
// This patch is safe and won't break your extension

// PATCHED: Disable tracking functions
(function() {
    'use strict';
    
    // Store original functions if they exist
    const originalFetch = globalThis.fetch;
    
    // Mock tracking functions
    globalThis.trackDisabled = function() { 
        console.log('[AUGMENT] Tracking disabled'); 
        return Promise.resolve(); 
    };
    globalThis.sendTelemetryDisabled = function() { 
        console.log('[AUGMENT] Telemetry disabled'); 
        return Promise.resolve(); 
    };
    globalThis.analyticsDisabled = function() { 
        console.log('[AUGMENT] Analytics disabled'); 
        return Promise.resolve(); 
    };
    globalThis.reportUsageDisabled = function() { 
        console.log('[AUGMENT] Usage reporting disabled'); 
        return Promise.resolve(); 
    };
    
    // Local credit override
    globalThis.LOCAL_CREDIT_OVERRIDE = {
        enabled: true,
        credits: { total: 10000, used: 0, remaining: 10000 },
        checkCredits: function() { return Promise.resolve(true); },
        deductCredits: function(amount) { 
            console.log('[AUGMENT] Would deduct', amount, 'credits (disabled)'); 
            return Promise.resolve(); 
        },
        getCreditBalance: function() { return Promise.resolve(this.credits); }
    };
    
    // Intercept fetch requests to block tracking
    if (originalFetch) {
        globalThis.fetch = function(url, options) {
            if (typeof url === 'string' && (
                url.includes('augmentcode.com/usage') || 
                url.includes('augmentcode.com/analytics') || 
                url.includes('augmentcode.com/billing') || 
                url.includes('augmentcode.com/telemetry')
            )) {
                console.log('[AUGMENT] Blocked tracking request to:', url);
                return Promise.resolve(new Response('{"success": true, "blocked": true}', {
                    status: 200,
                    headers: { 'Content-Type': 'application/json' }
                }));
            }
            return originalFetch.call(this, url, options);
        };
    }
    
    console.log('[AUGMENT] Tracking patches loaded successfully');
})();
