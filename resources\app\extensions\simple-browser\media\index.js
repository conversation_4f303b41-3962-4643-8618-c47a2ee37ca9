function s(e){document.readyState==="loading"||document.readyState==="uninitialized"?document.addEventListener("DOMContentLoaded",e):e()}var d=acquireVsCodeApi();function i(){let e=document.getElementById("simple-browser-settings");if(e){let t=e.getAttribute("data-settings");if(t)return JSON.parse(t)}throw new Error("Could not load settings")}var r=i(),c=document.querySelector("iframe"),n=document.querySelector(".header"),a=n.querySelector(".url-input"),l=n.querySelector(".forward-button"),m=n.querySelector(".back-button"),g=n.querySelector(".reload-button"),E=n.querySelector(".open-external-button");window.addEventListener("message",e=>{switch(e.data.type){case"focus":{c.focus();break}case"didChangeFocusLockIndicatorEnabled":{u(e.data.enabled);break}}});s(()=>{setInterval(()=>{let t=document.activeElement?.tagName==="IFRAME";document.body.classList.toggle("iframe-focused",t)},50),c.addEventListener("load",()=>{}),a.addEventListener("change",t=>{let o=t.target.value;e(o)}),l.addEventListener("click",()=>{history.forward()}),m.addEventListener("click",()=>{history.back()}),E.addEventListener("click",()=>{d.postMessage({type:"openExternal",url:a.value})}),g.addEventListener("click",()=>{e(a.value)}),e(r.url),a.value=r.url,u(r.focusLockIndicatorEnabled);function e(t){try{let o=new URL(t);o.searchParams.append("vscodeBrowserReqId",Date.now().toString()),c.src=o.toString()}catch{c.src=t}d.setState({url:t})}});function u(e){document.body.classList.toggle("enable-focus-lock-indicator",e)}
