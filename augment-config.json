{"name": "Augment Usage Tracking Disabler", "version": "1.0.0", "description": "Configuration for disabling Augment usage tracking while keeping local credit display", "localCreditSystem": {"enabled": true, "mockCredits": {"total": 10000, "used": 0, "remaining": 10000, "resetDaily": false}, "displayInStatusBar": true, "showWarnings": false}, "trackingDisabled": {"telemetry": true, "analytics": true, "usageReporting": true, "networkRequests": true}, "endpoints": {"blocked": ["https://api.augmentcode.com/usage", "https://api.augmentcode.com/analytics", "https://api.augmentcode.com/billing", "https://api.augmentcode.com/telemetry"]}, "features": {"keepCreditDisplay": true, "keepLocalFunctionality": true, "blockNetworkTracking": true, "mockAPIResponses": true}, "warnings": ["This modification may violate Augment's terms of service", "Extension updates will overwrite these changes", "Use at your own risk", "Always backup original files before modification"]}