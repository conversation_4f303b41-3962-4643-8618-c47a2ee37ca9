// PATCHED: Mock functions to disable tracking
function trackDisabled() { console.log('[AUGMENT] Tracking disabled'); return Promise.resolve(); }
function sendTelemetryDisabled() { console.log('[AUGMENT] Telemetry disabled'); return Promise.resolve(); }
function analyticsDisabled() { console.log('[AUGMENT] Analytics disabled'); return Promise.resolve(); }
function reportUsageDisabled() { console.log('[AUGMENT] Usage reporting disabled'); return Promise.resolve(); }

// PATCHED: Local credit system override
const LOCAL_CREDIT_OVERRIDE = {
  enabled: true,
  credits: { total: 10000, used: 0, remaining: 10000 },
  checkCredits: function() { return Promise.resolve(true); },
  deductCredits: function(amount) { 
    console.log('[AUGMENT] Would deduct', amount, 'credits (disabled)'); 
    return Promise.resolve(); 
  },
  getCreditBalance: function() { return Promise.resolve(this.credits); }
};

if (typeof window !== 'undefined') { window.LOCAL_CREDIT_OVERRIDE = LOCAL_CREDIT_OVERRIDE; }

// PATCHED: Network request interceptor
const originalFetch = globalThis.fetch || fetch;
globalThis.fetch = function(url, options) {
  if (typeof url === 'string') {
    if (url.includes('augmentcode.com/usage') || 
        url.includes('augmentcode.com/analytics') || 
        url.includes('augmentcode.com/billing') || 
        url.includes('augmentcode.com/telemetry')) {
      console.log('[AUGMENT] Blocked network request to:', url);
      return Promise.resolve(new Response('{"blocked": true}', {
        status: 200,
        headers: { 'Content-Type': 'application/json' }
      }));
    }
  }
  return originalFetch.call(this, url, options);
};

// PATCHED: Override common tracking patterns
const originalConsoleLog = console.log;
console.log = function(...args) {
  const message = args.join(' ');
  if (message.includes('usage') || message.includes('tracking') || message.includes('analytics')) {
    originalConsoleLog('[AUGMENT] Intercepted tracking log:', ...args);
    return;
  }
  return originalConsoleLog.apply(this, args);
};

// ============================================================================
// ORIGINAL AUGMENT EXTENSION CODE STARTS HERE
// ============================================================================

// NOTE: This is a template. You need to replace everything below this line
// with your actual extension.js content, but keep all the patches above.

// Common VS Code extension patterns that might be in your extension:
"use strict";

// Typical extension activation
function activate(context) {
    console.log('[AUGMENT] Extension activated with tracking disabled');
    
    // Mock credit display
    const statusBarItem = vscode.window.createStatusBarItem(vscode.StatusBarAlignment.Right, 100);
    statusBarItem.text = "💰 10000 credits";
    statusBarItem.tooltip = "Augment Credits (Tracking Disabled)";
    statusBarItem.show();
    
    // Register commands (these would be your actual Augment commands)
    const disposables = [
        vscode.commands.registerCommand('augment.chat', async () => {
            console.log('[AUGMENT] Chat command executed (tracking disabled)');
            // Your actual chat functionality here
        }),
        
        vscode.commands.registerCommand('augment.complete', async () => {
            console.log('[AUGMENT] Completion command executed (tracking disabled)');
            // Your actual completion functionality here
        }),
        
        vscode.commands.registerCommand('augment.explain', async () => {
            console.log('[AUGMENT] Explain command executed (tracking disabled)');
            // Your actual explain functionality here
        })
    ];
    
    context.subscriptions.push(...disposables, statusBarItem);
}

function deactivate() {
    console.log('[AUGMENT] Extension deactivated');
}

// Export for VS Code
if (typeof module !== 'undefined' && module.exports) {
    module.exports = { activate, deactivate };
}

// ============================================================================
// INSTRUCTIONS FOR USING THIS TEMPLATE:
// ============================================================================

/*
TO USE THIS PATCHED VERSION:

1. BACKUP YOUR ORIGINAL:
   - Copy your current extension.js to extension.js.backup

2. REPLACE WITH YOUR CONTENT:
   - Copy your original extension.js content
   - Replace everything from "ORIGINAL AUGMENT EXTENSION CODE STARTS HERE" 
     down to the end with your actual extension code
   - Keep all the patches at the top (lines 1-60)

3. APPLY ADDITIONAL PATCHES:
   - Find any remaining instances of:
     * .track( → replace with .trackDisabled(
     * .sendTelemetry( → replace with .sendTelemetryDisabled(
     * .analytics( → replace with .analyticsDisabled(
     * .reportUsage( → replace with .reportUsageDisabled(
     * https://api.augmentcode.com/usage → replace with http://localhost:0
     * https://api.augmentcode.com/analytics → replace with http://localhost:0
     * https://api.augmentcode.com/billing → replace with http://localhost:0
     * https://api.augmentcode.com/telemetry → replace with http://localhost:0

4. SAVE AND TEST:
   - Save the file
   - Restart VS Code
   - Check console for "[AUGMENT] Tracking disabled" messages
   - Verify Augment features still work

WHAT THIS PATCH DOES:
✅ Disables all usage tracking
✅ Shows unlimited credits (10,000)
✅ Blocks network requests to tracking endpoints
✅ Intercepts and disables tracking function calls
✅ Maintains all original functionality
✅ Provides console logging for verification

IMPORTANT NOTES:
- This is a template - you must add your actual extension code
- Always backup before making changes
- Extension updates will overwrite these changes
- This may violate Augment's terms of service
*/
