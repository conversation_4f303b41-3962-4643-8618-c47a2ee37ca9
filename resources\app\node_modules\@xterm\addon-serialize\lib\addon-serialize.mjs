/**
 * Copyright (c) 2014-2024 The xterm.js authors. All rights reserved.
 * @license MIT
 *
 * Copyright (c) 2012-2013, <PERSON> (MIT License)
 * @license MIT
 *
 * Originally forked from (with the author's permission):
 *   <PERSON><PERSON><PERSON>'s javascript vt100 for jslinux:
 *   http://bellard.org/jslinux/
 *   Copyright (c) 2011 F<PERSON><PERSON>
 */
/*---------------------------------------------------------------------------------------------
 *  Copyright (c) Microsoft Corporation. All rights reserved.
 *  Licensed under the MIT License. See License.txt in the project root for license information.
 *--------------------------------------------------------------------------------------------*/
var m=0,p=0,g=0,b=0;var I;(r=>{function a(t,l,s,i){return i!==void 0?`#${B(t)}${B(l)}${B(s)}${B(i)}`:`#${B(t)}${B(l)}${B(s)}`}r.toCss=a;function o(t,l,s,i=255){return(t<<24|l<<16|s<<8|i)>>>0}r.toRgba=o;function e(t,l,s,i){return{css:r.toCss(t,l,s,i),rgba:r.toRgba(t,l,s,i)}}r.toColor=e})(I||={});var $;(i=>{function a(n,u){if(b=(u.rgba&255)/255,b===1)return{css:u.css,rgba:u.rgba};let c=u.rgba>>24&255,d=u.rgba>>16&255,f=u.rgba>>8&255,C=n.rgba>>24&255,h=n.rgba>>16&255,x=n.rgba>>8&255;m=C+Math.round((c-C)*b),p=h+Math.round((d-h)*b),g=x+Math.round((f-x)*b);let R=I.toCss(m,p,g),L=I.toRgba(m,p,g);return{css:R,rgba:L}}i.blend=a;function o(n){return(n.rgba&255)===255}i.isOpaque=o;function e(n,u,c){let d=y.ensureContrastRatio(n.rgba,u.rgba,c);if(d)return I.toColor(d>>24&255,d>>16&255,d>>8&255)}i.ensureContrastRatio=e;function r(n){let u=(n.rgba|255)>>>0;return[m,p,g]=y.toChannels(u),{css:I.toCss(m,p,g),rgba:u}}i.opaque=r;function t(n,u){return b=Math.round(u*255),[m,p,g]=y.toChannels(n.rgba),{css:I.toCss(m,p,g,b),rgba:I.toRgba(m,p,g,b)}}i.opacity=t;function l(n,u){return b=n.rgba&255,t(n,b*u/255)}i.multiplyOpacity=l;function s(n){return[n.rgba>>24&255,n.rgba>>16&255,n.rgba>>8&255]}i.toColorRGB=s})($||={});var _;(r=>{let a,o;try{let t=document.createElement("canvas");t.width=1,t.height=1;let l=t.getContext("2d",{willReadFrequently:!0});l&&(a=l,a.globalCompositeOperation="copy",o=a.createLinearGradient(0,0,1,1))}catch{}function e(t){if(t.match(/#[\da-f]{3,8}/i))switch(t.length){case 4:return m=parseInt(t.slice(1,2).repeat(2),16),p=parseInt(t.slice(2,3).repeat(2),16),g=parseInt(t.slice(3,4).repeat(2),16),I.toColor(m,p,g);case 5:return m=parseInt(t.slice(1,2).repeat(2),16),p=parseInt(t.slice(2,3).repeat(2),16),g=parseInt(t.slice(3,4).repeat(2),16),b=parseInt(t.slice(4,5).repeat(2),16),I.toColor(m,p,g,b);case 7:return{css:t,rgba:(parseInt(t.slice(1),16)<<8|255)>>>0};case 9:return{css:t,rgba:parseInt(t.slice(1),16)>>>0}}let l=t.match(/rgba?\(\s*(\d{1,3})\s*,\s*(\d{1,3})\s*,\s*(\d{1,3})\s*(,\s*(0|1|\d?\.(\d+))\s*)?\)/);if(l)return m=parseInt(l[1]),p=parseInt(l[2]),g=parseInt(l[3]),b=Math.round((l[5]===void 0?1:parseFloat(l[5]))*255),I.toColor(m,p,g,b);if(!a||!o)throw new Error("css.toColor: Unsupported css format");if(a.fillStyle=o,a.fillStyle=t,typeof a.fillStyle!="string")throw new Error("css.toColor: Unsupported css format");if(a.fillRect(0,0,1,1),[m,p,g,b]=a.getImageData(0,0,1,1).data,b!==255)throw new Error("css.toColor: Unsupported css format");return{rgba:I.toRgba(m,p,g,b),css:t}}r.toColor=e})(_||={});var v;(e=>{function a(r){return o(r>>16&255,r>>8&255,r&255)}e.relativeLuminance=a;function o(r,t,l){let s=r/255,i=t/255,n=l/255,u=s<=.03928?s/12.92:Math.pow((s+.055)/1.055,2.4),c=i<=.03928?i/12.92:Math.pow((i+.055)/1.055,2.4),d=n<=.03928?n/12.92:Math.pow((n+.055)/1.055,2.4);return u*.2126+c*.7152+d*.0722}e.relativeLuminance2=o})(v||={});var y;(l=>{function a(s,i){if(b=(i&255)/255,b===1)return i;let n=i>>24&255,u=i>>16&255,c=i>>8&255,d=s>>24&255,f=s>>16&255,C=s>>8&255;return m=d+Math.round((n-d)*b),p=f+Math.round((u-f)*b),g=C+Math.round((c-C)*b),I.toRgba(m,p,g)}l.blend=a;function o(s,i,n){let u=v.relativeLuminance(s>>8),c=v.relativeLuminance(i>>8);if(F(u,c)<n){if(c<u){let h=e(s,i,n),x=F(u,v.relativeLuminance(h>>8));if(x<n){let R=r(s,i,n),L=F(u,v.relativeLuminance(R>>8));return x>L?h:R}return h}let f=r(s,i,n),C=F(u,v.relativeLuminance(f>>8));if(C<n){let h=e(s,i,n),x=F(u,v.relativeLuminance(h>>8));return C>x?f:h}return f}}l.ensureContrastRatio=o;function e(s,i,n){let u=s>>24&255,c=s>>16&255,d=s>>8&255,f=i>>24&255,C=i>>16&255,h=i>>8&255,x=F(v.relativeLuminance2(f,C,h),v.relativeLuminance2(u,c,d));for(;x<n&&(f>0||C>0||h>0);)f-=Math.max(0,Math.ceil(f*.1)),C-=Math.max(0,Math.ceil(C*.1)),h-=Math.max(0,Math.ceil(h*.1)),x=F(v.relativeLuminance2(f,C,h),v.relativeLuminance2(u,c,d));return(f<<24|C<<16|h<<8|255)>>>0}l.reduceLuminance=e;function r(s,i,n){let u=s>>24&255,c=s>>16&255,d=s>>8&255,f=i>>24&255,C=i>>16&255,h=i>>8&255,x=F(v.relativeLuminance2(f,C,h),v.relativeLuminance2(u,c,d));for(;x<n&&(f<255||C<255||h<255);)f=Math.min(255,f+Math.ceil((255-f)*.1)),C=Math.min(255,C+Math.ceil((255-C)*.1)),h=Math.min(255,h+Math.ceil((255-h)*.1)),x=F(v.relativeLuminance2(f,C,h),v.relativeLuminance2(u,c,d));return(f<<24|C<<16|h<<8|255)>>>0}l.increaseLuminance=r;function t(s){return[s>>24&255,s>>16&255,s>>8&255,s&255]}l.toChannels=t})(y||={});function B(a){let o=a.toString(16);return o.length<2?"0"+o:o}function F(a,o){return a<o?(o+.05)/(a+.05):(a+.05)/(o+.05)}var E=Object.freeze((()=>{let a=[_.toColor("#2e3436"),_.toColor("#cc0000"),_.toColor("#4e9a06"),_.toColor("#c4a000"),_.toColor("#3465a4"),_.toColor("#75507b"),_.toColor("#06989a"),_.toColor("#d3d7cf"),_.toColor("#555753"),_.toColor("#ef2929"),_.toColor("#8ae234"),_.toColor("#fce94f"),_.toColor("#729fcf"),_.toColor("#ad7fa8"),_.toColor("#34e2e2"),_.toColor("#eeeeec")],o=[0,95,135,175,215,255];for(let e=0;e<216;e++){let r=o[e/36%6|0],t=o[e/6%6|0],l=o[e%6];a.push({css:I.toCss(r,t,l),rgba:I.toRgba(r,t,l)})}for(let e=0;e<24;e++){let r=8+e*10;a.push({css:I.toCss(r,r,r),rgba:I.toRgba(r,r,r)})}return a})());function A(a,o,e){return Math.max(o,Math.min(a,e))}function O(a){switch(a){case"&":return"&amp;";case"<":return"&lt;"}return a}var S=class{constructor(o){this._buffer=o}serialize(o,e){let r=this._buffer.getNullCell(),t=this._buffer.getNullCell(),l=r,s=o.start.y,i=o.end.y,n=o.start.x,u=o.end.x;this._beforeSerialize(i-s,s,i);for(let c=s;c<=i;c++){let d=this._buffer.getLine(c);if(d){let f=c===o.start.y?n:0,C=c===o.end.y?u:d.length;for(let h=f;h<C;h++){let x=d.getCell(h,l===r?t:r);if(!x){console.warn(`Can't get cell at row=${c}, col=${h}`);continue}this._nextCell(x,l,c,h),l=x}}this._rowEnd(c,c===i)}return this._afterSerialize(),this._serializeString(e)}_nextCell(o,e,r,t){}_rowEnd(o,e){}_beforeSerialize(o,e,r){}_afterSerialize(){}_serializeString(o){return""}};function T(a,o){return a.getFgColorMode()===o.getFgColorMode()&&a.getFgColor()===o.getFgColor()}function w(a,o){return a.getBgColorMode()===o.getBgColorMode()&&a.getBgColor()===o.getBgColor()}function z(a,o){return a.isInverse()===o.isInverse()&&a.isBold()===o.isBold()&&a.isUnderline()===o.isUnderline()&&a.isOverline()===o.isOverline()&&a.isBlink()===o.isBlink()&&a.isInvisible()===o.isInvisible()&&a.isItalic()===o.isItalic()&&a.isDim()===o.isDim()&&a.isStrikethrough()===o.isStrikethrough()}var k=class extends S{constructor(e,r){super(e);this._terminal=r;this._rowIndex=0;this._allRows=new Array;this._allRowSeparators=new Array;this._currentRow="";this._nullCellCount=0;this._cursorStyle=this._buffer.getNullCell();this._cursorStyleRow=0;this._cursorStyleCol=0;this._backgroundCell=this._buffer.getNullCell();this._firstRow=0;this._lastCursorRow=0;this._lastCursorCol=0;this._lastContentCursorRow=0;this._lastContentCursorCol=0;this._thisRowLastChar=this._buffer.getNullCell();this._thisRowLastSecondChar=this._buffer.getNullCell();this._nextRowFirstChar=this._buffer.getNullCell()}_beforeSerialize(e,r,t){this._allRows=new Array(e),this._lastContentCursorRow=r,this._lastCursorRow=r,this._firstRow=r}_rowEnd(e,r){this._nullCellCount>0&&!w(this._cursorStyle,this._backgroundCell)&&(this._currentRow+=`\x1B[${this._nullCellCount}X`);let t="";if(!r){e-this._firstRow>=this._terminal.rows&&this._buffer.getLine(this._cursorStyleRow)?.getCell(this._cursorStyleCol,this._backgroundCell);let l=this._buffer.getLine(e),s=this._buffer.getLine(e+1);if(!s.isWrapped)t=`\r
`,this._lastCursorRow=e+1,this._lastCursorCol=0;else{t="";let i=l.getCell(l.length-1,this._thisRowLastChar),n=l.getCell(l.length-2,this._thisRowLastSecondChar),u=s.getCell(0,this._nextRowFirstChar),c=u.getWidth()>1,d=!1;(u.getChars()&&c?this._nullCellCount<=1:this._nullCellCount<=0)&&((i.getChars()||i.getWidth()===0)&&w(i,u)&&(d=!0),c&&(n.getChars()||n.getWidth()===0)&&w(i,u)&&w(n,u)&&(d=!0)),d||(t="-".repeat(this._nullCellCount+1),t+="\x1B[1D\x1B[1X",this._nullCellCount>0&&(t+="\x1B[A",t+=`\x1B[${l.length-this._nullCellCount}C`,t+=`\x1B[${this._nullCellCount}X`,t+=`\x1B[${l.length-this._nullCellCount}D`,t+="\x1B[B"),this._lastContentCursorRow=e+1,this._lastContentCursorCol=0,this._lastCursorRow=e+1,this._lastCursorCol=0)}}this._allRows[this._rowIndex]=this._currentRow,this._allRowSeparators[this._rowIndex++]=t,this._currentRow="",this._nullCellCount=0}_diffStyle(e,r){let t=[],l=!T(e,r),s=!w(e,r),i=!z(e,r);if(l||s||i)if(e.isAttributeDefault())r.isAttributeDefault()||t.push(0);else{if(l){let n=e.getFgColor();e.isFgRGB()?t.push(38,2,n>>>16&255,n>>>8&255,n&255):e.isFgPalette()?n>=16?t.push(38,5,n):t.push(n&8?90+(n&7):30+(n&7)):t.push(39)}if(s){let n=e.getBgColor();e.isBgRGB()?t.push(48,2,n>>>16&255,n>>>8&255,n&255):e.isBgPalette()?n>=16?t.push(48,5,n):t.push(n&8?100+(n&7):40+(n&7)):t.push(49)}i&&(e.isInverse()!==r.isInverse()&&t.push(e.isInverse()?7:27),e.isBold()!==r.isBold()&&t.push(e.isBold()?1:22),e.isUnderline()!==r.isUnderline()&&t.push(e.isUnderline()?4:24),e.isOverline()!==r.isOverline()&&t.push(e.isOverline()?53:55),e.isBlink()!==r.isBlink()&&t.push(e.isBlink()?5:25),e.isInvisible()!==r.isInvisible()&&t.push(e.isInvisible()?8:28),e.isItalic()!==r.isItalic()&&t.push(e.isItalic()?3:23),e.isDim()!==r.isDim()&&t.push(e.isDim()?2:22),e.isStrikethrough()!==r.isStrikethrough()&&t.push(e.isStrikethrough()?9:29))}return t}_nextCell(e,r,t,l){if(e.getWidth()===0)return;let i=e.getChars()==="",n=this._diffStyle(e,this._cursorStyle);if(i?!w(this._cursorStyle,e):n.length>0){this._nullCellCount>0&&(w(this._cursorStyle,this._backgroundCell)||(this._currentRow+=`\x1B[${this._nullCellCount}X`),this._currentRow+=`\x1B[${this._nullCellCount}C`,this._nullCellCount=0),this._lastContentCursorRow=this._lastCursorRow=t,this._lastContentCursorCol=this._lastCursorCol=l,this._currentRow+=`\x1B[${n.join(";")}m`;let c=this._buffer.getLine(t);c!==void 0&&(c.getCell(l,this._cursorStyle),this._cursorStyleRow=t,this._cursorStyleCol=l)}i?this._nullCellCount+=e.getWidth():(this._nullCellCount>0&&(w(this._cursorStyle,this._backgroundCell)?this._currentRow+=`\x1B[${this._nullCellCount}C`:(this._currentRow+=`\x1B[${this._nullCellCount}X`,this._currentRow+=`\x1B[${this._nullCellCount}C`),this._nullCellCount=0),this._currentRow+=e.getChars(),this._lastContentCursorRow=this._lastCursorRow=t,this._lastContentCursorCol=this._lastCursorCol=l+e.getWidth())}_serializeString(e){let r=this._allRows.length;this._buffer.length-this._firstRow<=this._terminal.rows&&(r=this._lastContentCursorRow+1-this._firstRow,this._lastCursorCol=this._lastContentCursorCol,this._lastCursorRow=this._lastContentCursorRow);let t="";for(let i=0;i<r;i++)t+=this._allRows[i],i+1<r&&(t+=this._allRowSeparators[i]);if(!e){let i=this._buffer.baseY+this._buffer.cursorY,n=this._buffer.cursorX,u=i!==this._lastCursorRow||n!==this._lastCursorCol,c=f=>{f>0?t+=`\x1B[${f}C`:f<0&&(t+=`\x1B[${-f}D`)};u&&((f=>{f>0?t+=`\x1B[${f}B`:f<0&&(t+=`\x1B[${-f}A`)})(i-this._lastCursorRow),c(n-this._lastCursorCol))}let l=this._terminal._core._inputHandler._curAttrData,s=this._diffStyle(l,this._cursorStyle);return s.length>0&&(t+=`\x1B[${s.join(";")}m`),t}},D=class{activate(o){this._terminal=o}_serializeBufferByScrollback(o,e,r){let t=e.length,l=r===void 0?t:A(r+o.rows,0,t);return this._serializeBufferByRange(o,e,{start:t-l,end:t-1},!1)}_serializeBufferByRange(o,e,r,t){return new k(e,o).serialize({start:{x:0,y:typeof r.start=="number"?r.start:r.start.line},end:{x:o.cols,y:typeof r.end=="number"?r.end:r.end.line}},t)}_serializeBufferAsHTML(o,e){let r=o.buffer.active,t=new M(r,o,e);if(!(e.onlySelection??!1)){let i=r.length,n=e.scrollback,u=n===void 0?i:A(n+o.rows,0,i);return t.serialize({start:{x:0,y:i-u},end:{x:o.cols,y:i-1}})}let s=this._terminal?.getSelectionPosition();return s!==void 0?t.serialize({start:{x:s.start.x,y:s.start.y},end:{x:s.end.x,y:s.end.y}}):""}_serializeModes(o){let e="",r=o.modes;if(r.applicationCursorKeysMode&&(e+="\x1B[?1h"),r.applicationKeypadMode&&(e+="\x1B[?66h"),r.bracketedPasteMode&&(e+="\x1B[?2004h"),r.insertMode&&(e+="\x1B[4h"),r.originMode&&(e+="\x1B[?6h"),r.reverseWraparoundMode&&(e+="\x1B[?45h"),r.sendFocusMode&&(e+="\x1B[?1004h"),r.wraparoundMode===!1&&(e+="\x1B[?7l"),r.mouseTrackingMode!=="none")switch(r.mouseTrackingMode){case"x10":e+="\x1B[?9h";break;case"vt200":e+="\x1B[?1000h";break;case"drag":e+="\x1B[?1002h";break;case"any":e+="\x1B[?1003h";break}return e}serialize(o){if(!this._terminal)throw new Error("Cannot use addon until it has been loaded");let e=o?.range?this._serializeBufferByRange(this._terminal,this._terminal.buffer.normal,o.range,!0):this._serializeBufferByScrollback(this._terminal,this._terminal.buffer.normal,o?.scrollback);if(!o?.excludeAltBuffer&&this._terminal.buffer.active.type==="alternate"){let r=this._serializeBufferByScrollback(this._terminal,this._terminal.buffer.alternate,void 0);e+=`\x1B[?1049h\x1B[H${r}`}return o?.excludeModes||(e+=this._serializeModes(this._terminal)),e}serializeAsHTML(o){if(!this._terminal)throw new Error("Cannot use addon until it has been loaded");return this._serializeBufferAsHTML(this._terminal,o||{})}dispose(){}},M=class extends S{constructor(e,r,t){super(e);this._terminal=r;this._options=t;this._currentRow="";this._htmlContent="";r._core._themeService?this._ansiColors=r._core._themeService.colors.ansi:this._ansiColors=E}_padStart(e,r,t){return r=r>>0,t=t??" ",e.length>r?e:(r-=e.length,r>t.length&&(t+=t.repeat(r/t.length)),t.slice(0,r)+e)}_beforeSerialize(e,r,t){this._htmlContent+="<html><body><!--StartFragment--><pre>";let l="#000000",s="#ffffff";(this._options.includeGlobalBackground??!1)&&(l=this._terminal.options.theme?.foreground??"#ffffff",s=this._terminal.options.theme?.background??"#000000");let i=[];i.push("color: "+l+";"),i.push("background-color: "+s+";"),i.push("font-family: "+this._terminal.options.fontFamily+";"),i.push("font-size: "+this._terminal.options.fontSize+"px;"),this._htmlContent+="<div style='"+i.join(" ")+"'>"}_afterSerialize(){this._htmlContent+="</div>",this._htmlContent+="</pre><!--EndFragment--></body></html>"}_rowEnd(e,r){this._htmlContent+="<div><span>"+this._currentRow+"</span></div>",this._currentRow=""}_getHexColor(e,r){let t=r?e.getFgColor():e.getBgColor();if(r?e.isFgRGB():e.isBgRGB())return"#"+[t>>16&255,t>>8&255,t&255].map(s=>this._padStart(s.toString(16),2,"0")).join("");if(r?e.isFgPalette():e.isBgPalette())return this._ansiColors[t].css}_diffStyle(e,r){let t=[],l=!T(e,r),s=!w(e,r),i=!z(e,r);if(l||s||i){let n=this._getHexColor(e,!0);n&&t.push("color: "+n+";");let u=this._getHexColor(e,!1);return u&&t.push("background-color: "+u+";"),e.isInverse()&&t.push("color: #000000; background-color: #BFBFBF;"),e.isBold()&&t.push("font-weight: bold;"),e.isUnderline()&&e.isOverline()?t.push("text-decoration: overline underline;"):e.isUnderline()?t.push("text-decoration: underline;"):e.isOverline()&&t.push("text-decoration: overline;"),e.isBlink()&&t.push("text-decoration: blink;"),e.isInvisible()&&t.push("visibility: hidden;"),e.isItalic()&&t.push("font-style: italic;"),e.isDim()&&t.push("opacity: 0.5;"),e.isStrikethrough()&&t.push("text-decoration: line-through;"),t}}_nextCell(e,r,t,l){if(e.getWidth()===0)return;let i=e.getChars()==="",n=this._diffStyle(e,r);n&&(this._currentRow+=n.length===0?"</span><span>":"</span><span style='"+n.join(" ")+"'>"),i?this._currentRow+=" ":this._currentRow+=O(e.getChars())}_serializeString(){return this._htmlContent}};export{M as HTMLSerializeHandler,D as SerializeAddon};
//# sourceMappingURL=addon-serialize.mjs.map
