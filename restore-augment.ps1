# Augment Extension Restorer - PowerShell Version

Write-Host ""
Write-Host "========================================" -ForegroundColor Cyan
Write-Host "   Augment Extension Restorer" -ForegroundColor Cyan
Write-Host "========================================" -ForegroundColor Cyan
Write-Host ""

# Set paths
$augmentPath = Join-Path $env:USERPROFILE ".vscode\extensions\augment.vscode-augment-0.464.0"
$extensionFile = Join-Path $augmentPath "out\extension.js"
$backupFile = Join-Path $augmentPath "out\extension.js.backup"

# Check if backup exists
if (-not (Test-Path $backupFile)) {
    Write-Host "ERROR: Backup file not found!" -ForegroundColor Red
    Write-Host "Expected: $backupFile" -ForegroundColor Red
    Write-Host "Cannot restore original extension." -ForegroundColor Red
    Read-Host "Press Enter to exit"
    exit 1
}

Write-Host "Found backup file: $backupFile" -ForegroundColor Green
Write-Host ""

# Restore from backup
Write-Host "Restoring original extension..." -ForegroundColor Yellow
try {
    Copy-Item $backupFile $extensionFile -Force
    Write-Host ""
    Write-Host "========================================" -ForegroundColor Cyan
    Write-Host "   Restoration Complete!" -ForegroundColor Cyan
    Write-Host "========================================" -ForegroundColor Cyan
    Write-Host ""
    Write-Host "✅ Original Augment extension restored!" -ForegroundColor Green
    Write-Host "🔄 Please restart VS Code for changes to take effect." -ForegroundColor Yellow
} catch {
    Write-Host "ERROR: Failed to restore extension!" -ForegroundColor Red
    Write-Host $_.Exception.Message -ForegroundColor Red
    Read-Host "Press Enter to exit"
    exit 1
}

Write-Host ""
Read-Host "Press Enter to exit"
