{"version": 3, "sources": ["../../../src/common/input/UnicodeV6.ts", "../../../src/vs/base/common/errors.ts", "../../../src/vs/base/common/functional.ts", "../../../src/vs/base/common/arraysFind.ts", "../../../src/vs/base/common/arrays.ts", "../../../src/vs/base/common/collections.ts", "../../../src/vs/base/common/map.ts", "../../../src/vs/base/common/iterator.ts", "../../../src/vs/base/common/lifecycle.ts", "../../../src/vs/base/common/linkedList.ts", "../../../src/vs/base/common/stopwatch.ts", "../../../src/vs/base/common/event.ts", "../../../src/common/services/UnicodeService.ts", "../src/UnicodeV11.ts", "../src/Unicode11Addon.ts"], "sourcesContent": ["/**\n * Copyright (c) 2019 The xterm.js authors. All rights reserved.\n * @license MIT\n */\nimport { IUnicodeVersionProvider, UnicodeCharProperties, UnicodeCharWidth } from 'common/services/Services';\nimport { UnicodeService } from 'common/services/UnicodeService';\n\nconst BMP_COMBINING = [\n  [0x0300, 0x036F], [0x0483, 0x0486], [0x0488, 0x0489],\n  [0x0591, 0x05BD], [0x05BF, 0x05BF], [0x05C1, 0x05C2],\n  [0x05C4, 0x05C5], [0x05C7, 0x05C7], [0x0600, 0x0603],\n  [0x0610, 0x0615], [0x064B, 0x065E], [0x0670, 0x0670],\n  [0x06D6, 0x06E4], [0x06E7, 0x06E8], [0x06EA, 0x06ED],\n  [0x070F, 0x070F], [0x0711, 0x0711], [0x0730, 0x074A],\n  [0x07A6, 0x07B0], [0x07EB, 0x07F3], [0x0901, 0x0902],\n  [0x093C, 0x093C], [0x0941, 0x0948], [0x094D, 0x094D],\n  [0x0951, 0x0954], [0x0962, 0x0963], [0x0981, 0x0981],\n  [0x09BC, 0x09BC], [0x09C1, 0x09C4], [0x09CD, 0x09CD],\n  [0x09E2, 0x09E3], [0x0A01, 0x0A02], [0x0A3C, 0x0A3C],\n  [0x0A41, 0x0A42], [0x0A47, 0x0A48], [0x0A4B, 0x0A4D],\n  [0x0A70, 0x0A71], [0x0A81, 0x0A82], [0x0ABC, 0x0ABC],\n  [0x0AC1, 0x0AC5], [0x0AC7, 0x0AC8], [0x0ACD, 0x0ACD],\n  [0x0AE2, 0x0AE3], [0x0B01, 0x0B01], [0x0B3C, 0x0B3C],\n  [0x0B3F, 0x0B3F], [0x0B41, 0x0B43], [0x0B4D, 0x0B4D],\n  [0x0B56, 0x0B56], [0x0B82, 0x0B82], [0x0BC0, 0x0BC0],\n  [0x0BCD, 0x0BCD], [0x0C3E, 0x0C40], [0x0C46, 0x0C48],\n  [0x0C4A, 0x0C4D], [0x0C55, 0x0C56], [0x0CBC, 0x0CBC],\n  [0x0CBF, 0x0CBF], [0x0CC6, 0x0CC6], [0x0CCC, 0x0CCD],\n  [0x0CE2, 0x0CE3], [0x0D41, 0x0D43], [0x0D4D, 0x0D4D],\n  [0x0DCA, 0x0DCA], [0x0DD2, 0x0DD4], [0x0DD6, 0x0DD6],\n  [0x0E31, 0x0E31], [0x0E34, 0x0E3A], [0x0E47, 0x0E4E],\n  [0x0EB1, 0x0EB1], [0x0EB4, 0x0EB9], [0x0EBB, 0x0EBC],\n  [0x0EC8, 0x0ECD], [0x0F18, 0x0F19], [0x0F35, 0x0F35],\n  [0x0F37, 0x0F37], [0x0F39, 0x0F39], [0x0F71, 0x0F7E],\n  [0x0F80, 0x0F84], [0x0F86, 0x0F87], [0x0F90, 0x0F97],\n  [0x0F99, 0x0FBC], [0x0FC6, 0x0FC6], [0x102D, 0x1030],\n  [0x1032, 0x1032], [0x1036, 0x1037], [0x1039, 0x1039],\n  [0x1058, 0x1059], [0x1160, 0x11FF], [0x135F, 0x135F],\n  [0x1712, 0x1714], [0x1732, 0x1734], [0x1752, 0x1753],\n  [0x1772, 0x1773], [0x17B4, 0x17B5], [0x17B7, 0x17BD],\n  [0x17C6, 0x17C6], [0x17C9, 0x17D3], [0x17DD, 0x17DD],\n  [0x180B, 0x180D], [0x18A9, 0x18A9], [0x1920, 0x1922],\n  [0x1927, 0x1928], [0x1932, 0x1932], [0x1939, 0x193B],\n  [0x1A17, 0x1A18], [0x1B00, 0x1B03], [0x1B34, 0x1B34],\n  [0x1B36, 0x1B3A], [0x1B3C, 0x1B3C], [0x1B42, 0x1B42],\n  [0x1B6B, 0x1B73], [0x1DC0, 0x1DCA], [0x1DFE, 0x1DFF],\n  [0x200B, 0x200F], [0x202A, 0x202E], [0x2060, 0x2063],\n  [0x206A, 0x206F], [0x20D0, 0x20EF], [0x302A, 0x302F],\n  [0x3099, 0x309A], [0xA806, 0xA806], [0xA80B, 0xA80B],\n  [0xA825, 0xA826], [0xFB1E, 0xFB1E], [0xFE00, 0xFE0F],\n  [0xFE20, 0xFE23], [0xFEFF, 0xFEFF], [0xFFF9, 0xFFFB]\n];\nconst HIGH_COMBINING = [\n  [0x10A01, 0x10A03], [0x10A05, 0x10A06], [0x10A0C, 0x10A0F],\n  [0x10A38, 0x10A3A], [0x10A3F, 0x10A3F], [0x1D167, 0x1D169],\n  [0x1D173, 0x1D182], [0x1D185, 0x1D18B], [0x1D1AA, 0x1D1AD],\n  [0x1D242, 0x1D244], [0xE0001, 0xE0001], [0xE0020, 0xE007F],\n  [0xE0100, 0xE01EF]\n];\n\n// BMP lookup table, lazy initialized during first addon loading\nlet table: Uint8Array;\n\nfunction bisearch(ucs: number, data: number[][]): boolean {\n  let min = 0;\n  let max = data.length - 1;\n  let mid;\n  if (ucs < data[0][0] || ucs > data[max][1]) {\n    return false;\n  }\n  while (max >= min) {\n    mid = (min + max) >> 1;\n    if (ucs > data[mid][1]) {\n      min = mid + 1;\n    } else if (ucs < data[mid][0]) {\n      max = mid - 1;\n    } else {\n      return true;\n    }\n  }\n  return false;\n}\n\nexport class UnicodeV6 implements IUnicodeVersionProvider {\n  public readonly version = '6';\n\n  constructor() {\n    // init lookup table once\n    if (!table) {\n      table = new Uint8Array(65536);\n      table.fill(1);\n      table[0] = 0;\n      // control chars\n      table.fill(0, 1, 32);\n      table.fill(0, 0x7f, 0xa0);\n\n      // apply wide char rules first\n      // wide chars\n      table.fill(2, 0x1100, 0x1160);\n      table[0x2329] = 2;\n      table[0x232a] = 2;\n      table.fill(2, 0x2e80, 0xa4d0);\n      table[0x303f] = 1;  // wrongly in last line\n\n      table.fill(2, 0xac00, 0xd7a4);\n      table.fill(2, 0xf900, 0xfb00);\n      table.fill(2, 0xfe10, 0xfe1a);\n      table.fill(2, 0xfe30, 0xfe70);\n      table.fill(2, 0xff00, 0xff61);\n      table.fill(2, 0xffe0, 0xffe7);\n\n      // apply combining last to ensure we overwrite\n      // wrongly wide set chars:\n      //    the original algo evals combining first and falls\n      //    through to wide check so we simply do here the opposite\n      // combining 0\n      for (let r = 0; r < BMP_COMBINING.length; ++r) {\n        table.fill(0, BMP_COMBINING[r][0], BMP_COMBINING[r][1] + 1);\n      }\n    }\n  }\n\n  public wcwidth(num: number): UnicodeCharWidth {\n    if (num < 32) return 0;\n    if (num < 127) return 1;\n    if (num < 65536) return table[num] as UnicodeCharWidth;\n    if (bisearch(num, HIGH_COMBINING)) return 0;\n    if ((num >= 0x20000 && num <= 0x2fffd) || (num >= 0x30000 && num <= 0x3fffd)) return 2;\n    return 1;\n  }\n\n  public charProperties(codepoint: number, preceding: UnicodeCharProperties): UnicodeCharProperties {\n    let width = this.wcwidth(codepoint);\n    let shouldJoin = width === 0 && preceding !== 0;\n    if (shouldJoin) {\n      const oldWidth = UnicodeService.extractWidth(preceding);\n      if (oldWidth === 0) {\n        shouldJoin = false;\n      } else if (oldWidth > width) {\n        width = oldWidth;\n      }\n    }\n    return UnicodeService.createPropertyValue(0, width, shouldJoin);\n  }\n}\n", "/*---------------------------------------------------------------------------------------------\n *  Copyright (c) Microsoft Corporation. All rights reserved.\n *  Licensed under the MIT License. See License.txt in the project root for license information.\n *--------------------------------------------------------------------------------------------*/\n\nexport interface ErrorListenerCallback {\n\t(error: any): void;\n}\n\nexport interface ErrorListenerUnbind {\n\t(): void;\n}\n\n// Avoid circular dependency on EventEmitter by implementing a subset of the interface.\nexport class ErrorHandler {\n\tprivate unexpectedErrorHandler: (e: any) => void;\n\tprivate listeners: ErrorListenerCallback[];\n\n\tconstructor() {\n\n\t\tthis.listeners = [];\n\n\t\tthis.unexpectedErrorHandler = function (e: any) {\n\t\t\tsetTimeout(() => {\n\t\t\t\tif (e.stack) {\n\t\t\t\t\tif (ErrorNoTelemetry.isErrorNoTelemetry(e)) {\n\t\t\t\t\t\tthrow new ErrorNoTelemetry(e.message + '\\n\\n' + e.stack);\n\t\t\t\t\t}\n\n\t\t\t\t\tthrow new Error(e.message + '\\n\\n' + e.stack);\n\t\t\t\t}\n\n\t\t\t\tthrow e;\n\t\t\t}, 0);\n\t\t};\n\t}\n\n\taddListener(listener: ErrorListenerCallback): ErrorListenerUnbind {\n\t\tthis.listeners.push(listener);\n\n\t\treturn () => {\n\t\t\tthis._removeListener(listener);\n\t\t};\n\t}\n\n\tprivate emit(e: any): void {\n\t\tthis.listeners.forEach((listener) => {\n\t\t\tlistener(e);\n\t\t});\n\t}\n\n\tprivate _removeListener(listener: ErrorListenerCallback): void {\n\t\tthis.listeners.splice(this.listeners.indexOf(listener), 1);\n\t}\n\n\tsetUnexpectedErrorHandler(newUnexpectedErrorHandler: (e: any) => void): void {\n\t\tthis.unexpectedErrorHandler = newUnexpectedErrorHandler;\n\t}\n\n\tgetUnexpectedErrorHandler(): (e: any) => void {\n\t\treturn this.unexpectedErrorHandler;\n\t}\n\n\tonUnexpectedError(e: any): void {\n\t\tthis.unexpectedErrorHandler(e);\n\t\tthis.emit(e);\n\t}\n\n\t// For external errors, we don't want the listeners to be called\n\tonUnexpectedExternalError(e: any): void {\n\t\tthis.unexpectedErrorHandler(e);\n\t}\n}\n\nexport const errorHandler = new ErrorHandler();\n\n/** @skipMangle */\nexport function setUnexpectedErrorHandler(newUnexpectedErrorHandler: (e: any) => void): void {\n\terrorHandler.setUnexpectedErrorHandler(newUnexpectedErrorHandler);\n}\n\n/**\n * Returns if the error is a SIGPIPE error. SIGPIPE errors should generally be\n * logged at most once, to avoid a loop.\n *\n * @see https://github.com/microsoft/vscode-remote-release/issues/6481\n */\nexport function isSigPipeError(e: unknown): e is Error {\n\tif (!e || typeof e !== 'object') {\n\t\treturn false;\n\t}\n\n\tconst cast = e as Record<string, string | undefined>;\n\treturn cast.code === 'EPIPE' && cast.syscall?.toUpperCase() === 'WRITE';\n}\n\nexport function onUnexpectedError(e: any): undefined {\n\t// ignore errors from cancelled promises\n\tif (!isCancellationError(e)) {\n\t\terrorHandler.onUnexpectedError(e);\n\t}\n\treturn undefined;\n}\n\nexport function onUnexpectedExternalError(e: any): undefined {\n\t// ignore errors from cancelled promises\n\tif (!isCancellationError(e)) {\n\t\terrorHandler.onUnexpectedExternalError(e);\n\t}\n\treturn undefined;\n}\n\nexport interface SerializedError {\n\treadonly $isError: true;\n\treadonly name: string;\n\treadonly message: string;\n\treadonly stack: string;\n\treadonly noTelemetry: boolean;\n}\n\nexport function transformErrorForSerialization(error: Error): SerializedError;\nexport function transformErrorForSerialization(error: any): any;\nexport function transformErrorForSerialization(error: any): any {\n\tif (error instanceof Error) {\n\t\tconst { name, message } = error;\n\t\tconst stack: string = (<any>error).stacktrace || (<any>error).stack;\n\t\treturn {\n\t\t\t$isError: true,\n\t\t\tname,\n\t\t\tmessage,\n\t\t\tstack,\n\t\t\tnoTelemetry: ErrorNoTelemetry.isErrorNoTelemetry(error)\n\t\t};\n\t}\n\n\t// return as is\n\treturn error;\n}\n\nexport function transformErrorFromSerialization(data: SerializedError): Error {\n\tlet error: Error;\n\tif (data.noTelemetry) {\n\t\terror = new ErrorNoTelemetry();\n\t} else {\n\t\terror = new Error();\n\t\terror.name = data.name;\n\t}\n\terror.message = data.message;\n\terror.stack = data.stack;\n\treturn error;\n}\n\n// see https://github.com/v8/v8/wiki/Stack%20Trace%20API#basic-stack-traces\nexport interface V8CallSite {\n\tgetThis(): unknown;\n\tgetTypeName(): string | null;\n\tgetFunction(): Function | undefined;\n\tgetFunctionName(): string | null;\n\tgetMethodName(): string | null;\n\tgetFileName(): string | null;\n\tgetLineNumber(): number | null;\n\tgetColumnNumber(): number | null;\n\tgetEvalOrigin(): string | undefined;\n\tisToplevel(): boolean;\n\tisEval(): boolean;\n\tisNative(): boolean;\n\tisConstructor(): boolean;\n\ttoString(): string;\n}\n\nconst canceledName = 'Canceled';\n\n/**\n * Checks if the given error is a promise in canceled state\n */\nexport function isCancellationError(error: any): boolean {\n\tif (error instanceof CancellationError) {\n\t\treturn true;\n\t}\n\treturn error instanceof Error && error.name === canceledName && error.message === canceledName;\n}\n\n// !!!IMPORTANT!!!\n// Do NOT change this class because it is also used as an API-type.\nexport class CancellationError extends Error {\n\tconstructor() {\n\t\tsuper(canceledName);\n\t\tthis.name = this.message;\n\t}\n}\n\n/**\n * @deprecated use {@link CancellationError `new CancellationError()`} instead\n */\nexport function canceled(): Error {\n\tconst error = new Error(canceledName);\n\terror.name = error.message;\n\treturn error;\n}\n\nexport function illegalArgument(name?: string): Error {\n\tif (name) {\n\t\treturn new Error(`Illegal argument: ${name}`);\n\t} else {\n\t\treturn new Error('Illegal argument');\n\t}\n}\n\nexport function illegalState(name?: string): Error {\n\tif (name) {\n\t\treturn new Error(`Illegal state: ${name}`);\n\t} else {\n\t\treturn new Error('Illegal state');\n\t}\n}\n\nexport class ReadonlyError extends TypeError {\n\tconstructor(name?: string) {\n\t\tsuper(name ? `${name} is read-only and cannot be changed` : 'Cannot change read-only property');\n\t}\n}\n\nexport function getErrorMessage(err: any): string {\n\tif (!err) {\n\t\treturn 'Error';\n\t}\n\n\tif (err.message) {\n\t\treturn err.message;\n\t}\n\n\tif (err.stack) {\n\t\treturn err.stack.split('\\n')[0];\n\t}\n\n\treturn String(err);\n}\n\nexport class NotImplementedError extends Error {\n\tconstructor(message?: string) {\n\t\tsuper('NotImplemented');\n\t\tif (message) {\n\t\t\tthis.message = message;\n\t\t}\n\t}\n}\n\nexport class NotSupportedError extends Error {\n\tconstructor(message?: string) {\n\t\tsuper('NotSupported');\n\t\tif (message) {\n\t\t\tthis.message = message;\n\t\t}\n\t}\n}\n\nexport class ExpectedError extends Error {\n\treadonly isExpected = true;\n}\n\n/**\n * Error that when thrown won't be logged in telemetry as an unhandled error.\n */\nexport class ErrorNoTelemetry extends Error {\n\toverride readonly name: string;\n\n\tconstructor(msg?: string) {\n\t\tsuper(msg);\n\t\tthis.name = 'CodeExpectedError';\n\t}\n\n\tpublic static fromError(err: Error): ErrorNoTelemetry {\n\t\tif (err instanceof ErrorNoTelemetry) {\n\t\t\treturn err;\n\t\t}\n\n\t\tconst result = new ErrorNoTelemetry();\n\t\tresult.message = err.message;\n\t\tresult.stack = err.stack;\n\t\treturn result;\n\t}\n\n\tpublic static isErrorNoTelemetry(err: Error): err is ErrorNoTelemetry {\n\t\treturn err.name === 'CodeExpectedError';\n\t}\n}\n\n/**\n * This error indicates a bug.\n * Do not throw this for invalid user input.\n * Only catch this error to recover gracefully from bugs.\n */\nexport class BugIndicatingError extends Error {\n\tconstructor(message?: string) {\n\t\tsuper(message || 'An unexpected bug occurred.');\n\t\tObject.setPrototypeOf(this, BugIndicatingError.prototype);\n\n\t\t// Because we know for sure only buggy code throws this,\n\t\t// we definitely want to break here and fix the bug.\n\t\t// eslint-disable-next-line no-debugger\n\t\t// debugger;\n\t}\n}\n", "/*---------------------------------------------------------------------------------------------\n *  Copyright (c) Microsoft Corporation. All rights reserved.\n *  Licensed under the MIT License. See License.txt in the project root for license information.\n *--------------------------------------------------------------------------------------------*/\n\n/**\n * Given a function, returns a function that is only calling that function once.\n */\nexport function createSingleCallFunction<T extends Function>(this: unknown, fn: T, fnDidRunCallback?: () => void): T {\n\tconst _this = this;\n\tlet didCall = false;\n\tlet result: unknown;\n\n\treturn function () {\n\t\tif (didCall) {\n\t\t\treturn result;\n\t\t}\n\n\t\tdidCall = true;\n\t\tif (fnDidRunCallback) {\n\t\t\ttry {\n\t\t\t\tresult = fn.apply(_this, arguments);\n\t\t\t} finally {\n\t\t\t\tfnDidRunCallback();\n\t\t\t}\n\t\t} else {\n\t\t\tresult = fn.apply(_this, arguments);\n\t\t}\n\n\t\treturn result;\n\t} as unknown as T;\n}\n", "/*---------------------------------------------------------------------------------------------\n *  Copyright (c) Microsoft Corporation. All rights reserved.\n *  Licensed under the MIT License. See License.txt in the project root for license information.\n *--------------------------------------------------------------------------------------------*/\n\nimport { Comparator } from './arrays';\n\nexport function findLast<T>(array: readonly T[], predicate: (item: T) => boolean): T | undefined {\n\tconst idx = findLastIdx(array, predicate);\n\tif (idx === -1) {\n\t\treturn undefined;\n\t}\n\treturn array[idx];\n}\n\nexport function findLastIdx<T>(array: readonly T[], predicate: (item: T) => boolean, fromIndex = array.length - 1): number {\n\tfor (let i = fromIndex; i >= 0; i--) {\n\t\tconst element = array[i];\n\n\t\tif (predicate(element)) {\n\t\t\treturn i;\n\t\t}\n\t}\n\n\treturn -1;\n}\n\n/**\n * Finds the last item where predicate is true using binary search.\n * `predicate` must be monotonous, i.e. `arr.map(predicate)` must be like `[true, ..., true, false, ..., false]`!\n *\n * @returns `undefined` if no item matches, otherwise the last item that matches the predicate.\n */\nexport function findLastMonotonous<T>(array: readonly T[], predicate: (item: T) => boolean): T | undefined {\n\tconst idx = findLastIdxMonotonous(array, predicate);\n\treturn idx === -1 ? undefined : array[idx];\n}\n\n/**\n * Finds the last item where predicate is true using binary search.\n * `predicate` must be monotonous, i.e. `arr.map(predicate)` must be like `[true, ..., true, false, ..., false]`!\n *\n * @returns `startIdx - 1` if predicate is false for all items, otherwise the index of the last item that matches the predicate.\n */\nexport function findLastIdxMonotonous<T>(array: readonly T[], predicate: (item: T) => boolean, startIdx = 0, endIdxEx = array.length): number {\n\tlet i = startIdx;\n\tlet j = endIdxEx;\n\twhile (i < j) {\n\t\tconst k = Math.floor((i + j) / 2);\n\t\tif (predicate(array[k])) {\n\t\t\ti = k + 1;\n\t\t} else {\n\t\t\tj = k;\n\t\t}\n\t}\n\treturn i - 1;\n}\n\n/**\n * Finds the first item where predicate is true using binary search.\n * `predicate` must be monotonous, i.e. `arr.map(predicate)` must be like `[false, ..., false, true, ..., true]`!\n *\n * @returns `undefined` if no item matches, otherwise the first item that matches the predicate.\n */\nexport function findFirstMonotonous<T>(array: readonly T[], predicate: (item: T) => boolean): T | undefined {\n\tconst idx = findFirstIdxMonotonousOrArrLen(array, predicate);\n\treturn idx === array.length ? undefined : array[idx];\n}\n\n/**\n * Finds the first item where predicate is true using binary search.\n * `predicate` must be monotonous, i.e. `arr.map(predicate)` must be like `[false, ..., false, true, ..., true]`!\n *\n * @returns `endIdxEx` if predicate is false for all items, otherwise the index of the first item that matches the predicate.\n */\nexport function findFirstIdxMonotonousOrArrLen<T>(array: readonly T[], predicate: (item: T) => boolean, startIdx = 0, endIdxEx = array.length): number {\n\tlet i = startIdx;\n\tlet j = endIdxEx;\n\twhile (i < j) {\n\t\tconst k = Math.floor((i + j) / 2);\n\t\tif (predicate(array[k])) {\n\t\t\tj = k;\n\t\t} else {\n\t\t\ti = k + 1;\n\t\t}\n\t}\n\treturn i;\n}\n\nexport function findFirstIdxMonotonous<T>(array: readonly T[], predicate: (item: T) => boolean, startIdx = 0, endIdxEx = array.length): number {\n\tconst idx = findFirstIdxMonotonousOrArrLen(array, predicate, startIdx, endIdxEx);\n\treturn idx === array.length ? -1 : idx;\n}\n\n/**\n * Use this when\n * * You have a sorted array\n * * You query this array with a monotonous predicate to find the last item that has a certain property.\n * * You query this array multiple times with monotonous predicates that get weaker and weaker.\n */\nexport class MonotonousArray<T> {\n\tpublic static assertInvariants = false;\n\n\tprivate _findLastMonotonousLastIdx = 0;\n\tprivate _prevFindLastPredicate: ((item: T) => boolean) | undefined;\n\n\tconstructor(private readonly _array: readonly T[]) {\n\t}\n\n\t/**\n\t * The predicate must be monotonous, i.e. `arr.map(predicate)` must be like `[true, ..., true, false, ..., false]`!\n\t * For subsequent calls, current predicate must be weaker than (or equal to) the previous predicate, i.e. more entries must be `true`.\n\t */\n\tfindLastMonotonous(predicate: (item: T) => boolean): T | undefined {\n\t\tif (MonotonousArray.assertInvariants) {\n\t\t\tif (this._prevFindLastPredicate) {\n\t\t\t\tfor (const item of this._array) {\n\t\t\t\t\tif (this._prevFindLastPredicate(item) && !predicate(item)) {\n\t\t\t\t\t\tthrow new Error('MonotonousArray: current predicate must be weaker than (or equal to) the previous predicate.');\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t}\n\t\t\tthis._prevFindLastPredicate = predicate;\n\t\t}\n\n\t\tconst idx = findLastIdxMonotonous(this._array, predicate, this._findLastMonotonousLastIdx);\n\t\tthis._findLastMonotonousLastIdx = idx + 1;\n\t\treturn idx === -1 ? undefined : this._array[idx];\n\t}\n}\n\n/**\n * Returns the first item that is equal to or greater than every other item.\n*/\nexport function findFirstMax<T>(array: readonly T[], comparator: Comparator<T>): T | undefined {\n\tif (array.length === 0) {\n\t\treturn undefined;\n\t}\n\n\tlet max = array[0];\n\tfor (let i = 1; i < array.length; i++) {\n\t\tconst item = array[i];\n\t\tif (comparator(item, max) > 0) {\n\t\t\tmax = item;\n\t\t}\n\t}\n\treturn max;\n}\n\n/**\n * Returns the last item that is equal to or greater than every other item.\n*/\nexport function findLastMax<T>(array: readonly T[], comparator: Comparator<T>): T | undefined {\n\tif (array.length === 0) {\n\t\treturn undefined;\n\t}\n\n\tlet max = array[0];\n\tfor (let i = 1; i < array.length; i++) {\n\t\tconst item = array[i];\n\t\tif (comparator(item, max) >= 0) {\n\t\t\tmax = item;\n\t\t}\n\t}\n\treturn max;\n}\n\n/**\n * Returns the first item that is equal to or less than every other item.\n*/\nexport function findFirstMin<T>(array: readonly T[], comparator: Comparator<T>): T | undefined {\n\treturn findFirstMax(array, (a, b) => -comparator(a, b));\n}\n\nexport function findMaxIdx<T>(array: readonly T[], comparator: Comparator<T>): number {\n\tif (array.length === 0) {\n\t\treturn -1;\n\t}\n\n\tlet maxIdx = 0;\n\tfor (let i = 1; i < array.length; i++) {\n\t\tconst item = array[i];\n\t\tif (comparator(item, array[maxIdx]) > 0) {\n\t\t\tmaxIdx = i;\n\t\t}\n\t}\n\treturn maxIdx;\n}\n\n/**\n * Returns the first mapped value of the array which is not undefined.\n */\nexport function mapFindFirst<T, R>(items: Iterable<T>, mapFn: (value: T) => R | undefined): R | undefined {\n\tfor (const value of items) {\n\t\tconst mapped = mapFn(value);\n\t\tif (mapped !== undefined) {\n\t\t\treturn mapped;\n\t\t}\n\t}\n\n\treturn undefined;\n}\n", "/*---------------------------------------------------------------------------------------------\n *  Copyright (c) Microsoft Corporation. All rights reserved.\n *  Licensed under the MIT License. See License.txt in the project root for license information.\n *--------------------------------------------------------------------------------------------*/\n\nimport { CancellationToken } from 'vs/base/common/cancellation';\nimport { CancellationError } from 'vs/base/common/errors';\nimport { ISplice } from 'vs/base/common/sequence';\nimport { findFirstIdxMonotonousOrArrLen } from './arraysFind';\n\n/**\n * Returns the last element of an array.\n * @param array The array.\n * @param n Which element from the end (default is zero).\n */\nexport function tail<T>(array: ArrayLike<T>, n: number = 0): T | undefined {\n\treturn array[array.length - (1 + n)];\n}\n\nexport function tail2<T>(arr: T[]): [T[], T] {\n\tif (arr.length === 0) {\n\t\tthrow new Error('Invalid tail call');\n\t}\n\n\treturn [arr.slice(0, arr.length - 1), arr[arr.length - 1]];\n}\n\nexport function equals<T>(one: ReadonlyArray<T> | undefined, other: ReadonlyArray<T> | undefined, itemEquals: (a: T, b: T) => boolean = (a, b) => a === b): boolean {\n\tif (one === other) {\n\t\treturn true;\n\t}\n\n\tif (!one || !other) {\n\t\treturn false;\n\t}\n\n\tif (one.length !== other.length) {\n\t\treturn false;\n\t}\n\n\tfor (let i = 0, len = one.length; i < len; i++) {\n\t\tif (!itemEquals(one[i], other[i])) {\n\t\t\treturn false;\n\t\t}\n\t}\n\n\treturn true;\n}\n\n/**\n * Remove the element at `index` by replacing it with the last element. This is faster than `splice`\n * but changes the order of the array\n */\nexport function removeFastWithoutKeepingOrder<T>(array: T[], index: number) {\n\tconst last = array.length - 1;\n\tif (index < last) {\n\t\tarray[index] = array[last];\n\t}\n\tarray.pop();\n}\n\n/**\n * Performs a binary search algorithm over a sorted array.\n *\n * @param array The array being searched.\n * @param key The value we search for.\n * @param comparator A function that takes two array elements and returns zero\n *   if they are equal, a negative number if the first element precedes the\n *   second one in the sorting order, or a positive number if the second element\n *   precedes the first one.\n * @return See {@link binarySearch2}\n */\nexport function binarySearch<T>(array: ReadonlyArray<T>, key: T, comparator: (op1: T, op2: T) => number): number {\n\treturn binarySearch2(array.length, i => comparator(array[i], key));\n}\n\n/**\n * Performs a binary search algorithm over a sorted collection. Useful for cases\n * when we need to perform a binary search over something that isn't actually an\n * array, and converting data to an array would defeat the use of binary search\n * in the first place.\n *\n * @param length The collection length.\n * @param compareToKey A function that takes an index of an element in the\n *   collection and returns zero if the value at this index is equal to the\n *   search key, a negative number if the value precedes the search key in the\n *   sorting order, or a positive number if the search key precedes the value.\n * @return A non-negative index of an element, if found. If not found, the\n *   result is -(n+1) (or ~n, using bitwise notation), where n is the index\n *   where the key should be inserted to maintain the sorting order.\n */\nexport function binarySearch2(length: number, compareToKey: (index: number) => number): number {\n\tlet low = 0,\n\t\thigh = length - 1;\n\n\twhile (low <= high) {\n\t\tconst mid = ((low + high) / 2) | 0;\n\t\tconst comp = compareToKey(mid);\n\t\tif (comp < 0) {\n\t\t\tlow = mid + 1;\n\t\t} else if (comp > 0) {\n\t\t\thigh = mid - 1;\n\t\t} else {\n\t\t\treturn mid;\n\t\t}\n\t}\n\treturn -(low + 1);\n}\n\ntype Compare<T> = (a: T, b: T) => number;\n\n\nexport function quickSelect<T>(nth: number, data: T[], compare: Compare<T>): T {\n\n\tnth = nth | 0;\n\n\tif (nth >= data.length) {\n\t\tthrow new TypeError('invalid index');\n\t}\n\n\tconst pivotValue = data[Math.floor(data.length * Math.random())];\n\tconst lower: T[] = [];\n\tconst higher: T[] = [];\n\tconst pivots: T[] = [];\n\n\tfor (const value of data) {\n\t\tconst val = compare(value, pivotValue);\n\t\tif (val < 0) {\n\t\t\tlower.push(value);\n\t\t} else if (val > 0) {\n\t\t\thigher.push(value);\n\t\t} else {\n\t\t\tpivots.push(value);\n\t\t}\n\t}\n\n\tif (nth < lower.length) {\n\t\treturn quickSelect(nth, lower, compare);\n\t} else if (nth < lower.length + pivots.length) {\n\t\treturn pivots[0];\n\t} else {\n\t\treturn quickSelect(nth - (lower.length + pivots.length), higher, compare);\n\t}\n}\n\nexport function groupBy<T>(data: ReadonlyArray<T>, compare: (a: T, b: T) => number): T[][] {\n\tconst result: T[][] = [];\n\tlet currentGroup: T[] | undefined = undefined;\n\tfor (const element of data.slice(0).sort(compare)) {\n\t\tif (!currentGroup || compare(currentGroup[0], element) !== 0) {\n\t\t\tcurrentGroup = [element];\n\t\t\tresult.push(currentGroup);\n\t\t} else {\n\t\t\tcurrentGroup.push(element);\n\t\t}\n\t}\n\treturn result;\n}\n\n/**\n * Splits the given items into a list of (non-empty) groups.\n * `shouldBeGrouped` is used to decide if two consecutive items should be in the same group.\n * The order of the items is preserved.\n */\nexport function* groupAdjacentBy<T>(items: Iterable<T>, shouldBeGrouped: (item1: T, item2: T) => boolean): Iterable<T[]> {\n\tlet currentGroup: T[] | undefined;\n\tlet last: T | undefined;\n\tfor (const item of items) {\n\t\tif (last !== undefined && shouldBeGrouped(last, item)) {\n\t\t\tcurrentGroup!.push(item);\n\t\t} else {\n\t\t\tif (currentGroup) {\n\t\t\t\tyield currentGroup;\n\t\t\t}\n\t\t\tcurrentGroup = [item];\n\t\t}\n\t\tlast = item;\n\t}\n\tif (currentGroup) {\n\t\tyield currentGroup;\n\t}\n}\n\nexport function forEachAdjacent<T>(arr: T[], f: (item1: T | undefined, item2: T | undefined) => void): void {\n\tfor (let i = 0; i <= arr.length; i++) {\n\t\tf(i === 0 ? undefined : arr[i - 1], i === arr.length ? undefined : arr[i]);\n\t}\n}\n\nexport function forEachWithNeighbors<T>(arr: T[], f: (before: T | undefined, element: T, after: T | undefined) => void): void {\n\tfor (let i = 0; i < arr.length; i++) {\n\t\tf(i === 0 ? undefined : arr[i - 1], arr[i], i + 1 === arr.length ? undefined : arr[i + 1]);\n\t}\n}\n\ninterface IMutableSplice<T> extends ISplice<T> {\n\treadonly toInsert: T[];\n\tdeleteCount: number;\n}\n\n/**\n * Diffs two *sorted* arrays and computes the splices which apply the diff.\n */\nexport function sortedDiff<T>(before: ReadonlyArray<T>, after: ReadonlyArray<T>, compare: (a: T, b: T) => number): ISplice<T>[] {\n\tconst result: IMutableSplice<T>[] = [];\n\n\tfunction pushSplice(start: number, deleteCount: number, toInsert: T[]): void {\n\t\tif (deleteCount === 0 && toInsert.length === 0) {\n\t\t\treturn;\n\t\t}\n\n\t\tconst latest = result[result.length - 1];\n\n\t\tif (latest && latest.start + latest.deleteCount === start) {\n\t\t\tlatest.deleteCount += deleteCount;\n\t\t\tlatest.toInsert.push(...toInsert);\n\t\t} else {\n\t\t\tresult.push({ start, deleteCount, toInsert });\n\t\t}\n\t}\n\n\tlet beforeIdx = 0;\n\tlet afterIdx = 0;\n\n\twhile (true) {\n\t\tif (beforeIdx === before.length) {\n\t\t\tpushSplice(beforeIdx, 0, after.slice(afterIdx));\n\t\t\tbreak;\n\t\t}\n\t\tif (afterIdx === after.length) {\n\t\t\tpushSplice(beforeIdx, before.length - beforeIdx, []);\n\t\t\tbreak;\n\t\t}\n\n\t\tconst beforeElement = before[beforeIdx];\n\t\tconst afterElement = after[afterIdx];\n\t\tconst n = compare(beforeElement, afterElement);\n\t\tif (n === 0) {\n\t\t\t// equal\n\t\t\tbeforeIdx += 1;\n\t\t\tafterIdx += 1;\n\t\t} else if (n < 0) {\n\t\t\t// beforeElement is smaller -> before element removed\n\t\t\tpushSplice(beforeIdx, 1, []);\n\t\t\tbeforeIdx += 1;\n\t\t} else if (n > 0) {\n\t\t\t// beforeElement is greater -> after element added\n\t\t\tpushSplice(beforeIdx, 0, [afterElement]);\n\t\t\tafterIdx += 1;\n\t\t}\n\t}\n\n\treturn result;\n}\n\n/**\n * Takes two *sorted* arrays and computes their delta (removed, added elements).\n * Finishes in `Math.min(before.length, after.length)` steps.\n */\nexport function delta<T>(before: ReadonlyArray<T>, after: ReadonlyArray<T>, compare: (a: T, b: T) => number): { removed: T[]; added: T[] } {\n\tconst splices = sortedDiff(before, after, compare);\n\tconst removed: T[] = [];\n\tconst added: T[] = [];\n\n\tfor (const splice of splices) {\n\t\tremoved.push(...before.slice(splice.start, splice.start + splice.deleteCount));\n\t\tadded.push(...splice.toInsert);\n\t}\n\n\treturn { removed, added };\n}\n\n/**\n * Returns the top N elements from the array.\n *\n * Faster than sorting the entire array when the array is a lot larger than N.\n *\n * @param array The unsorted array.\n * @param compare A sort function for the elements.\n * @param n The number of elements to return.\n * @return The first n elements from array when sorted with compare.\n */\nexport function top<T>(array: ReadonlyArray<T>, compare: (a: T, b: T) => number, n: number): T[] {\n\tif (n === 0) {\n\t\treturn [];\n\t}\n\tconst result = array.slice(0, n).sort(compare);\n\ttopStep(array, compare, result, n, array.length);\n\treturn result;\n}\n\n/**\n * Asynchronous variant of `top()` allowing for splitting up work in batches between which the event loop can run.\n *\n * Returns the top N elements from the array.\n *\n * Faster than sorting the entire array when the array is a lot larger than N.\n *\n * @param array The unsorted array.\n * @param compare A sort function for the elements.\n * @param n The number of elements to return.\n * @param batch The number of elements to examine before yielding to the event loop.\n * @return The first n elements from array when sorted with compare.\n */\nexport function topAsync<T>(array: T[], compare: (a: T, b: T) => number, n: number, batch: number, token?: CancellationToken): Promise<T[]> {\n\tif (n === 0) {\n\t\treturn Promise.resolve([]);\n\t}\n\n\treturn new Promise((resolve, reject) => {\n\t\t(async () => {\n\t\t\tconst o = array.length;\n\t\t\tconst result = array.slice(0, n).sort(compare);\n\t\t\tfor (let i = n, m = Math.min(n + batch, o); i < o; i = m, m = Math.min(m + batch, o)) {\n\t\t\t\tif (i > n) {\n\t\t\t\t\tawait new Promise(resolve => setTimeout(resolve)); // any other delay function would starve I/O\n\t\t\t\t}\n\t\t\t\tif (token && token.isCancellationRequested) {\n\t\t\t\t\tthrow new CancellationError();\n\t\t\t\t}\n\t\t\t\ttopStep(array, compare, result, i, m);\n\t\t\t}\n\t\t\treturn result;\n\t\t})()\n\t\t\t.then(resolve, reject);\n\t});\n}\n\nfunction topStep<T>(array: ReadonlyArray<T>, compare: (a: T, b: T) => number, result: T[], i: number, m: number): void {\n\tfor (const n = result.length; i < m; i++) {\n\t\tconst element = array[i];\n\t\tif (compare(element, result[n - 1]) < 0) {\n\t\t\tresult.pop();\n\t\t\tconst j = findFirstIdxMonotonousOrArrLen(result, e => compare(element, e) < 0);\n\t\t\tresult.splice(j, 0, element);\n\t\t}\n\t}\n}\n\n/**\n * @returns New array with all falsy values removed. The original array IS NOT modified.\n */\nexport function coalesce<T>(array: ReadonlyArray<T | undefined | null>): T[] {\n\treturn array.filter((e): e is T => !!e);\n}\n\n/**\n * Remove all falsy values from `array`. The original array IS modified.\n */\nexport function coalesceInPlace<T>(array: Array<T | undefined | null>): asserts array is Array<T> {\n\tlet to = 0;\n\tfor (let i = 0; i < array.length; i++) {\n\t\tif (!!array[i]) {\n\t\t\tarray[to] = array[i];\n\t\t\tto += 1;\n\t\t}\n\t}\n\tarray.length = to;\n}\n\n/**\n * @deprecated Use `Array.copyWithin` instead\n */\nexport function move(array: any[], from: number, to: number): void {\n\tarray.splice(to, 0, array.splice(from, 1)[0]);\n}\n\n/**\n * @returns false if the provided object is an array and not empty.\n */\nexport function isFalsyOrEmpty(obj: any): boolean {\n\treturn !Array.isArray(obj) || obj.length === 0;\n}\n\n/**\n * @returns True if the provided object is an array and has at least one element.\n */\nexport function isNonEmptyArray<T>(obj: T[] | undefined | null): obj is T[];\nexport function isNonEmptyArray<T>(obj: readonly T[] | undefined | null): obj is readonly T[];\nexport function isNonEmptyArray<T>(obj: T[] | readonly T[] | undefined | null): obj is T[] | readonly T[] {\n\treturn Array.isArray(obj) && obj.length > 0;\n}\n\n/**\n * Removes duplicates from the given array. The optional keyFn allows to specify\n * how elements are checked for equality by returning an alternate value for each.\n */\nexport function distinct<T>(array: ReadonlyArray<T>, keyFn: (value: T) => any = value => value): T[] {\n\tconst seen = new Set<any>();\n\n\treturn array.filter(element => {\n\t\tconst key = keyFn!(element);\n\t\tif (seen.has(key)) {\n\t\t\treturn false;\n\t\t}\n\t\tseen.add(key);\n\t\treturn true;\n\t});\n}\n\nexport function uniqueFilter<T, R>(keyFn: (t: T) => R): (t: T) => boolean {\n\tconst seen = new Set<R>();\n\n\treturn element => {\n\t\tconst key = keyFn(element);\n\n\t\tif (seen.has(key)) {\n\t\t\treturn false;\n\t\t}\n\n\t\tseen.add(key);\n\t\treturn true;\n\t};\n}\n\nexport function firstOrDefault<T, NotFound = T>(array: ReadonlyArray<T>, notFoundValue: NotFound): T | NotFound;\nexport function firstOrDefault<T>(array: ReadonlyArray<T>): T | undefined;\nexport function firstOrDefault<T, NotFound = T>(array: ReadonlyArray<T>, notFoundValue?: NotFound): T | NotFound | undefined {\n\treturn array.length > 0 ? array[0] : notFoundValue;\n}\n\nexport function lastOrDefault<T, NotFound = T>(array: ReadonlyArray<T>, notFoundValue: NotFound): T | NotFound;\nexport function lastOrDefault<T>(array: ReadonlyArray<T>): T | undefined;\nexport function lastOrDefault<T, NotFound = T>(array: ReadonlyArray<T>, notFoundValue?: NotFound): T | NotFound | undefined {\n\treturn array.length > 0 ? array[array.length - 1] : notFoundValue;\n}\n\nexport function commonPrefixLength<T>(one: ReadonlyArray<T>, other: ReadonlyArray<T>, equals: (a: T, b: T) => boolean = (a, b) => a === b): number {\n\tlet result = 0;\n\n\tfor (let i = 0, len = Math.min(one.length, other.length); i < len && equals(one[i], other[i]); i++) {\n\t\tresult++;\n\t}\n\n\treturn result;\n}\n\nexport function range(to: number): number[];\nexport function range(from: number, to: number): number[];\nexport function range(arg: number, to?: number): number[] {\n\tlet from = typeof to === 'number' ? arg : 0;\n\n\tif (typeof to === 'number') {\n\t\tfrom = arg;\n\t} else {\n\t\tfrom = 0;\n\t\tto = arg;\n\t}\n\n\tconst result: number[] = [];\n\n\tif (from <= to) {\n\t\tfor (let i = from; i < to; i++) {\n\t\t\tresult.push(i);\n\t\t}\n\t} else {\n\t\tfor (let i = from; i > to; i--) {\n\t\t\tresult.push(i);\n\t\t}\n\t}\n\n\treturn result;\n}\n\nexport function index<T>(array: ReadonlyArray<T>, indexer: (t: T) => string): { [key: string]: T };\nexport function index<T, R>(array: ReadonlyArray<T>, indexer: (t: T) => string, mapper: (t: T) => R): { [key: string]: R };\nexport function index<T, R>(array: ReadonlyArray<T>, indexer: (t: T) => string, mapper?: (t: T) => R): { [key: string]: R } {\n\treturn array.reduce((r, t) => {\n\t\tr[indexer(t)] = mapper ? mapper(t) : t;\n\t\treturn r;\n\t}, Object.create(null));\n}\n\n/**\n * Inserts an element into an array. Returns a function which, when\n * called, will remove that element from the array.\n *\n * @deprecated In almost all cases, use a `Set<T>` instead.\n */\nexport function insert<T>(array: T[], element: T): () => void {\n\tarray.push(element);\n\n\treturn () => remove(array, element);\n}\n\n/**\n * Removes an element from an array if it can be found.\n *\n * @deprecated In almost all cases, use a `Set<T>` instead.\n */\nexport function remove<T>(array: T[], element: T): T | undefined {\n\tconst index = array.indexOf(element);\n\tif (index > -1) {\n\t\tarray.splice(index, 1);\n\n\t\treturn element;\n\t}\n\n\treturn undefined;\n}\n\n/**\n * Insert `insertArr` inside `target` at `insertIndex`.\n * Please don't touch unless you understand https://jsperf.com/inserting-an-array-within-an-array\n */\nexport function arrayInsert<T>(target: T[], insertIndex: number, insertArr: T[]): T[] {\n\tconst before = target.slice(0, insertIndex);\n\tconst after = target.slice(insertIndex);\n\treturn before.concat(insertArr, after);\n}\n\n/**\n * Uses Fisher-Yates shuffle to shuffle the given array\n */\nexport function shuffle<T>(array: T[], _seed?: number): void {\n\tlet rand: () => number;\n\n\tif (typeof _seed === 'number') {\n\t\tlet seed = _seed;\n\t\t// Seeded random number generator in JS. Modified from:\n\t\t// https://stackoverflow.com/questions/521295/seeding-the-random-number-generator-in-javascript\n\t\trand = () => {\n\t\t\tconst x = Math.sin(seed++) * 179426549; // throw away most significant digits and reduce any potential bias\n\t\t\treturn x - Math.floor(x);\n\t\t};\n\t} else {\n\t\trand = Math.random;\n\t}\n\n\tfor (let i = array.length - 1; i > 0; i -= 1) {\n\t\tconst j = Math.floor(rand() * (i + 1));\n\t\tconst temp = array[i];\n\t\tarray[i] = array[j];\n\t\tarray[j] = temp;\n\t}\n}\n\n/**\n * Pushes an element to the start of the array, if found.\n */\nexport function pushToStart<T>(arr: T[], value: T): void {\n\tconst index = arr.indexOf(value);\n\n\tif (index > -1) {\n\t\tarr.splice(index, 1);\n\t\tarr.unshift(value);\n\t}\n}\n\n/**\n * Pushes an element to the end of the array, if found.\n */\nexport function pushToEnd<T>(arr: T[], value: T): void {\n\tconst index = arr.indexOf(value);\n\n\tif (index > -1) {\n\t\tarr.splice(index, 1);\n\t\tarr.push(value);\n\t}\n}\n\nexport function pushMany<T>(arr: T[], items: ReadonlyArray<T>): void {\n\tfor (const item of items) {\n\t\tarr.push(item);\n\t}\n}\n\nexport function mapArrayOrNot<T, U>(items: T | T[], fn: (_: T) => U): U | U[] {\n\treturn Array.isArray(items) ?\n\t\titems.map(fn) :\n\t\tfn(items);\n}\n\nexport function asArray<T>(x: T | T[]): T[];\nexport function asArray<T>(x: T | readonly T[]): readonly T[];\nexport function asArray<T>(x: T | T[]): T[] {\n\treturn Array.isArray(x) ? x : [x];\n}\n\nexport function getRandomElement<T>(arr: T[]): T | undefined {\n\treturn arr[Math.floor(Math.random() * arr.length)];\n}\n\n/**\n * Insert the new items in the array.\n * @param array The original array.\n * @param start The zero-based location in the array from which to start inserting elements.\n * @param newItems The items to be inserted\n */\nexport function insertInto<T>(array: T[], start: number, newItems: T[]): void {\n\tconst startIdx = getActualStartIndex(array, start);\n\tconst originalLength = array.length;\n\tconst newItemsLength = newItems.length;\n\tarray.length = originalLength + newItemsLength;\n\t// Move the items after the start index, start from the end so that we don't overwrite any value.\n\tfor (let i = originalLength - 1; i >= startIdx; i--) {\n\t\tarray[i + newItemsLength] = array[i];\n\t}\n\n\tfor (let i = 0; i < newItemsLength; i++) {\n\t\tarray[i + startIdx] = newItems[i];\n\t}\n}\n\n/**\n * Removes elements from an array and inserts new elements in their place, returning the deleted elements. Alternative to the native Array.splice method, it\n * can only support limited number of items due to the maximum call stack size limit.\n * @param array The original array.\n * @param start The zero-based location in the array from which to start removing elements.\n * @param deleteCount The number of elements to remove.\n * @returns An array containing the elements that were deleted.\n */\nexport function splice<T>(array: T[], start: number, deleteCount: number, newItems: T[]): T[] {\n\tconst index = getActualStartIndex(array, start);\n\tlet result = array.splice(index, deleteCount);\n\tif (result === undefined) {\n\t\t// see https://bugs.webkit.org/show_bug.cgi?id=261140\n\t\tresult = [];\n\t}\n\tinsertInto(array, index, newItems);\n\treturn result;\n}\n\n/**\n * Determine the actual start index (same logic as the native splice() or slice())\n * If greater than the length of the array, start will be set to the length of the array. In this case, no element will be deleted but the method will behave as an adding function, adding as many element as item[n*] provided.\n * If negative, it will begin that many elements from the end of the array. (In this case, the origin -1, meaning -n is the index of the nth last element, and is therefore equivalent to the index of array.length - n.) If array.length + start is less than 0, it will begin from index 0.\n * @param array The target array.\n * @param start The operation index.\n */\nfunction getActualStartIndex<T>(array: T[], start: number): number {\n\treturn start < 0 ? Math.max(start + array.length, 0) : Math.min(start, array.length);\n}\n\n/**\n * When comparing two values,\n * a negative number indicates that the first value is less than the second,\n * a positive number indicates that the first value is greater than the second,\n * and zero indicates that neither is the case.\n*/\nexport type CompareResult = number;\n\nexport namespace CompareResult {\n\texport function isLessThan(result: CompareResult): boolean {\n\t\treturn result < 0;\n\t}\n\n\texport function isLessThanOrEqual(result: CompareResult): boolean {\n\t\treturn result <= 0;\n\t}\n\n\texport function isGreaterThan(result: CompareResult): boolean {\n\t\treturn result > 0;\n\t}\n\n\texport function isNeitherLessOrGreaterThan(result: CompareResult): boolean {\n\t\treturn result === 0;\n\t}\n\n\texport const greaterThan = 1;\n\texport const lessThan = -1;\n\texport const neitherLessOrGreaterThan = 0;\n}\n\n/**\n * A comparator `c` defines a total order `<=` on `T` as following:\n * `c(a, b) <= 0` iff `a` <= `b`.\n * We also have `c(a, b) == 0` iff `c(b, a) == 0`.\n*/\nexport type Comparator<T> = (a: T, b: T) => CompareResult;\n\nexport function compareBy<TItem, TCompareBy>(selector: (item: TItem) => TCompareBy, comparator: Comparator<TCompareBy>): Comparator<TItem> {\n\treturn (a, b) => comparator(selector(a), selector(b));\n}\n\nexport function tieBreakComparators<TItem>(...comparators: Comparator<TItem>[]): Comparator<TItem> {\n\treturn (item1, item2) => {\n\t\tfor (const comparator of comparators) {\n\t\t\tconst result = comparator(item1, item2);\n\t\t\tif (!CompareResult.isNeitherLessOrGreaterThan(result)) {\n\t\t\t\treturn result;\n\t\t\t}\n\t\t}\n\t\treturn CompareResult.neitherLessOrGreaterThan;\n\t};\n}\n\n/**\n * The natural order on numbers.\n*/\nexport const numberComparator: Comparator<number> = (a, b) => a - b;\n\nexport const booleanComparator: Comparator<boolean> = (a, b) => numberComparator(a ? 1 : 0, b ? 1 : 0);\n\nexport function reverseOrder<TItem>(comparator: Comparator<TItem>): Comparator<TItem> {\n\treturn (a, b) => -comparator(a, b);\n}\n\nexport class ArrayQueue<T> {\n\tprivate firstIdx = 0;\n\tprivate lastIdx = this.items.length - 1;\n\n\t/**\n\t * Constructs a queue that is backed by the given array. Runtime is O(1).\n\t*/\n\tconstructor(private readonly items: readonly T[]) { }\n\n\tget length(): number {\n\t\treturn this.lastIdx - this.firstIdx + 1;\n\t}\n\n\t/**\n\t * Consumes elements from the beginning of the queue as long as the predicate returns true.\n\t * If no elements were consumed, `null` is returned. Has a runtime of O(result.length).\n\t*/\n\ttakeWhile(predicate: (value: T) => boolean): T[] | null {\n\t\t// P(k) := k <= this.lastIdx && predicate(this.items[k])\n\t\t// Find s := min { k | k >= this.firstIdx && !P(k) } and return this.data[this.firstIdx...s)\n\n\t\tlet startIdx = this.firstIdx;\n\t\twhile (startIdx < this.items.length && predicate(this.items[startIdx])) {\n\t\t\tstartIdx++;\n\t\t}\n\t\tconst result = startIdx === this.firstIdx ? null : this.items.slice(this.firstIdx, startIdx);\n\t\tthis.firstIdx = startIdx;\n\t\treturn result;\n\t}\n\n\t/**\n\t * Consumes elements from the end of the queue as long as the predicate returns true.\n\t * If no elements were consumed, `null` is returned.\n\t * The result has the same order as the underlying array!\n\t*/\n\ttakeFromEndWhile(predicate: (value: T) => boolean): T[] | null {\n\t\t// P(k) := this.firstIdx >= k && predicate(this.items[k])\n\t\t// Find s := max { k | k <= this.lastIdx && !P(k) } and return this.data(s...this.lastIdx]\n\n\t\tlet endIdx = this.lastIdx;\n\t\twhile (endIdx >= 0 && predicate(this.items[endIdx])) {\n\t\t\tendIdx--;\n\t\t}\n\t\tconst result = endIdx === this.lastIdx ? null : this.items.slice(endIdx + 1, this.lastIdx + 1);\n\t\tthis.lastIdx = endIdx;\n\t\treturn result;\n\t}\n\n\tpeek(): T | undefined {\n\t\tif (this.length === 0) {\n\t\t\treturn undefined;\n\t\t}\n\t\treturn this.items[this.firstIdx];\n\t}\n\n\tpeekLast(): T | undefined {\n\t\tif (this.length === 0) {\n\t\t\treturn undefined;\n\t\t}\n\t\treturn this.items[this.lastIdx];\n\t}\n\n\tdequeue(): T | undefined {\n\t\tconst result = this.items[this.firstIdx];\n\t\tthis.firstIdx++;\n\t\treturn result;\n\t}\n\n\tremoveLast(): T | undefined {\n\t\tconst result = this.items[this.lastIdx];\n\t\tthis.lastIdx--;\n\t\treturn result;\n\t}\n\n\ttakeCount(count: number): T[] {\n\t\tconst result = this.items.slice(this.firstIdx, this.firstIdx + count);\n\t\tthis.firstIdx += count;\n\t\treturn result;\n\t}\n}\n\n/**\n * This class is faster than an iterator and array for lazy computed data.\n*/\nexport class CallbackIterable<T> {\n\tpublic static readonly empty = new CallbackIterable<never>(_callback => { });\n\n\tconstructor(\n\t\t/**\n\t\t * Calls the callback for every item.\n\t\t * Stops when the callback returns false.\n\t\t*/\n\t\tpublic readonly iterate: (callback: (item: T) => boolean) => void\n\t) {\n\t}\n\n\tforEach(handler: (item: T) => void) {\n\t\tthis.iterate(item => { handler(item); return true; });\n\t}\n\n\ttoArray(): T[] {\n\t\tconst result: T[] = [];\n\t\tthis.iterate(item => { result.push(item); return true; });\n\t\treturn result;\n\t}\n\n\tfilter(predicate: (item: T) => boolean): CallbackIterable<T> {\n\t\treturn new CallbackIterable(cb => this.iterate(item => predicate(item) ? cb(item) : true));\n\t}\n\n\tmap<TResult>(mapFn: (item: T) => TResult): CallbackIterable<TResult> {\n\t\treturn new CallbackIterable<TResult>(cb => this.iterate(item => cb(mapFn(item))));\n\t}\n\n\tsome(predicate: (item: T) => boolean): boolean {\n\t\tlet result = false;\n\t\tthis.iterate(item => { result = predicate(item); return !result; });\n\t\treturn result;\n\t}\n\n\tfindFirst(predicate: (item: T) => boolean): T | undefined {\n\t\tlet result: T | undefined;\n\t\tthis.iterate(item => {\n\t\t\tif (predicate(item)) {\n\t\t\t\tresult = item;\n\t\t\t\treturn false;\n\t\t\t}\n\t\t\treturn true;\n\t\t});\n\t\treturn result;\n\t}\n\n\tfindLast(predicate: (item: T) => boolean): T | undefined {\n\t\tlet result: T | undefined;\n\t\tthis.iterate(item => {\n\t\t\tif (predicate(item)) {\n\t\t\t\tresult = item;\n\t\t\t}\n\t\t\treturn true;\n\t\t});\n\t\treturn result;\n\t}\n\n\tfindLastMaxBy(comparator: Comparator<T>): T | undefined {\n\t\tlet result: T | undefined;\n\t\tlet first = true;\n\t\tthis.iterate(item => {\n\t\t\tif (first || CompareResult.isGreaterThan(comparator(item, result!))) {\n\t\t\t\tfirst = false;\n\t\t\t\tresult = item;\n\t\t\t}\n\t\t\treturn true;\n\t\t});\n\t\treturn result;\n\t}\n}\n\n/**\n * Represents a re-arrangement of items in an array.\n */\nexport class Permutation {\n\tconstructor(private readonly _indexMap: readonly number[]) { }\n\n\t/**\n\t * Returns a permutation that sorts the given array according to the given compare function.\n\t */\n\tpublic static createSortPermutation<T>(arr: readonly T[], compareFn: (a: T, b: T) => number): Permutation {\n\t\tconst sortIndices = Array.from(arr.keys()).sort((index1, index2) => compareFn(arr[index1], arr[index2]));\n\t\treturn new Permutation(sortIndices);\n\t}\n\n\t/**\n\t * Returns a new array with the elements of the given array re-arranged according to this permutation.\n\t */\n\tapply<T>(arr: readonly T[]): T[] {\n\t\treturn arr.map((_, index) => arr[this._indexMap[index]]);\n\t}\n\n\t/**\n\t * Returns a new permutation that undoes the re-arrangement of this permutation.\n\t*/\n\tinverse(): Permutation {\n\t\tconst inverseIndexMap = this._indexMap.slice();\n\t\tfor (let i = 0; i < this._indexMap.length; i++) {\n\t\t\tinverseIndexMap[this._indexMap[i]] = i;\n\t\t}\n\t\treturn new Permutation(inverseIndexMap);\n\t}\n}\n", "/*---------------------------------------------------------------------------------------------\n *  Copyright (c) Microsoft Corporation. All rights reserved.\n *  Licensed under the MIT License. See License.txt in the project root for license information.\n *--------------------------------------------------------------------------------------------*/\n\n/**\n * An interface for a JavaScript object that\n * acts a dictionary. The keys are strings.\n */\nexport type IStringDictionary<V> = Record<string, V>;\n\n/**\n * An interface for a JavaScript object that\n * acts a dictionary. The keys are numbers.\n */\nexport type INumberDictionary<V> = Record<number, V>;\n\n/**\n * Groups the collection into a dictionary based on the provided\n * group function.\n */\nexport function groupBy<K extends string | number | symbol, V>(data: V[], groupFn: (element: V) => K): Record<K, V[]> {\n\tconst result: Record<K, V[]> = Object.create(null);\n\tfor (const element of data) {\n\t\tconst key = groupFn(element);\n\t\tlet target = result[key];\n\t\tif (!target) {\n\t\t\ttarget = result[key] = [];\n\t\t}\n\t\ttarget.push(element);\n\t}\n\treturn result;\n}\n\nexport function diffSets<T>(before: Set<T>, after: Set<T>): { removed: T[]; added: T[] } {\n\tconst removed: T[] = [];\n\tconst added: T[] = [];\n\tfor (const element of before) {\n\t\tif (!after.has(element)) {\n\t\t\tremoved.push(element);\n\t\t}\n\t}\n\tfor (const element of after) {\n\t\tif (!before.has(element)) {\n\t\t\tadded.push(element);\n\t\t}\n\t}\n\treturn { removed, added };\n}\n\nexport function diffMaps<K, V>(before: Map<K, V>, after: Map<K, V>): { removed: V[]; added: V[] } {\n\tconst removed: V[] = [];\n\tconst added: V[] = [];\n\tfor (const [index, value] of before) {\n\t\tif (!after.has(index)) {\n\t\t\tremoved.push(value);\n\t\t}\n\t}\n\tfor (const [index, value] of after) {\n\t\tif (!before.has(index)) {\n\t\t\tadded.push(value);\n\t\t}\n\t}\n\treturn { removed, added };\n}\n\n/**\n * Computes the intersection of two sets.\n *\n * @param setA - The first set.\n * @param setB - The second iterable.\n * @returns A new set containing the elements that are in both `setA` and `setB`.\n */\nexport function intersection<T>(setA: Set<T>, setB: Iterable<T>): Set<T> {\n\tconst result = new Set<T>();\n\tfor (const elem of setB) {\n\t\tif (setA.has(elem)) {\n\t\t\tresult.add(elem);\n\t\t}\n\t}\n\treturn result;\n}\n\nexport class SetWithKey<T> implements Set<T> {\n\tprivate _map = new Map<any, T>();\n\n\tconstructor(values: T[], private toKey: (t: T) => any) {\n\t\tfor (const value of values) {\n\t\t\tthis.add(value);\n\t\t}\n\t}\n\n\tget size(): number {\n\t\treturn this._map.size;\n\t}\n\n\tadd(value: T): this {\n\t\tconst key = this.toKey(value);\n\t\tthis._map.set(key, value);\n\t\treturn this;\n\t}\n\n\tdelete(value: T): boolean {\n\t\treturn this._map.delete(this.toKey(value));\n\t}\n\n\thas(value: T): boolean {\n\t\treturn this._map.has(this.toKey(value));\n\t}\n\n\t*entries(): IterableIterator<[T, T]> {\n\t\tfor (const entry of this._map.values()) {\n\t\t\tyield [entry, entry];\n\t\t}\n\t}\n\n\tkeys(): IterableIterator<T> {\n\t\treturn this.values();\n\t}\n\n\t*values(): IterableIterator<T> {\n\t\tfor (const entry of this._map.values()) {\n\t\t\tyield entry;\n\t\t}\n\t}\n\n\tclear(): void {\n\t\tthis._map.clear();\n\t}\n\n\tforEach(callbackfn: (value: T, value2: T, set: Set<T>) => void, thisArg?: any): void {\n\t\tthis._map.forEach(entry => callbackfn.call(thisArg, entry, entry, this));\n\t}\n\n\t[Symbol.iterator](): IterableIterator<T> {\n\t\treturn this.values();\n\t}\n\n\t[Symbol.toStringTag]: string = 'SetWithKey';\n}\n", "/*---------------------------------------------------------------------------------------------\n *  Copyright (c) Microsoft Corporation. All rights reserved.\n *  Licensed under the MIT License. See License.txt in the project root for license information.\n *--------------------------------------------------------------------------------------------*/\n\nexport function getOrSet<K, V>(map: Map<K, V>, key: K, value: V): V {\n\tlet result = map.get(key);\n\tif (result === undefined) {\n\t\tresult = value;\n\t\tmap.set(key, result);\n\t}\n\n\treturn result;\n}\n\nexport function mapToString<K, V>(map: Map<K, V>): string {\n\tconst entries: string[] = [];\n\tmap.forEach((value, key) => {\n\t\tentries.push(`${key} => ${value}`);\n\t});\n\n\treturn `Map(${map.size}) {${entries.join(', ')}}`;\n}\n\nexport function setToString<K>(set: Set<K>): string {\n\tconst entries: K[] = [];\n\tset.forEach(value => {\n\t\tentries.push(value);\n\t});\n\n\treturn `Set(${set.size}) {${entries.join(', ')}}`;\n}\n\nexport const enum Touch {\n\tNone = 0,\n\tAsOld = 1,\n\tAsNew = 2\n}\n\nexport class CounterSet<T> {\n\n\tprivate map = new Map<T, number>();\n\n\tadd(value: T): CounterSet<T> {\n\t\tthis.map.set(value, (this.map.get(value) || 0) + 1);\n\t\treturn this;\n\t}\n\n\tdelete(value: T): boolean {\n\t\tlet counter = this.map.get(value) || 0;\n\n\t\tif (counter === 0) {\n\t\t\treturn false;\n\t\t}\n\n\t\tcounter--;\n\n\t\tif (counter === 0) {\n\t\t\tthis.map.delete(value);\n\t\t} else {\n\t\t\tthis.map.set(value, counter);\n\t\t}\n\n\t\treturn true;\n\t}\n\n\thas(value: T): boolean {\n\t\treturn this.map.has(value);\n\t}\n}\n\n/**\n * A map that allows access both by keys and values.\n * **NOTE**: values need to be unique.\n */\nexport class BidirectionalMap<K, V> {\n\n\tprivate readonly _m1 = new Map<K, V>();\n\tprivate readonly _m2 = new Map<V, K>();\n\n\tconstructor(entries?: readonly (readonly [K, V])[]) {\n\t\tif (entries) {\n\t\t\tfor (const [key, value] of entries) {\n\t\t\t\tthis.set(key, value);\n\t\t\t}\n\t\t}\n\t}\n\n\tclear(): void {\n\t\tthis._m1.clear();\n\t\tthis._m2.clear();\n\t}\n\n\tset(key: K, value: V): void {\n\t\tthis._m1.set(key, value);\n\t\tthis._m2.set(value, key);\n\t}\n\n\tget(key: K): V | undefined {\n\t\treturn this._m1.get(key);\n\t}\n\n\tgetKey(value: V): K | undefined {\n\t\treturn this._m2.get(value);\n\t}\n\n\tdelete(key: K): boolean {\n\t\tconst value = this._m1.get(key);\n\t\tif (value === undefined) {\n\t\t\treturn false;\n\t\t}\n\t\tthis._m1.delete(key);\n\t\tthis._m2.delete(value);\n\t\treturn true;\n\t}\n\n\tforEach(callbackfn: (value: V, key: K, map: BidirectionalMap<K, V>) => void, thisArg?: any): void {\n\t\tthis._m1.forEach((value, key) => {\n\t\t\tcallbackfn.call(thisArg, value, key, this);\n\t\t});\n\t}\n\n\tkeys(): IterableIterator<K> {\n\t\treturn this._m1.keys();\n\t}\n\n\tvalues(): IterableIterator<V> {\n\t\treturn this._m1.values();\n\t}\n}\n\nexport class SetMap<K, V> {\n\n\tprivate map = new Map<K, Set<V>>();\n\n\tadd(key: K, value: V): void {\n\t\tlet values = this.map.get(key);\n\n\t\tif (!values) {\n\t\t\tvalues = new Set<V>();\n\t\t\tthis.map.set(key, values);\n\t\t}\n\n\t\tvalues.add(value);\n\t}\n\n\tdelete(key: K, value: V): void {\n\t\tconst values = this.map.get(key);\n\n\t\tif (!values) {\n\t\t\treturn;\n\t\t}\n\n\t\tvalues.delete(value);\n\n\t\tif (values.size === 0) {\n\t\t\tthis.map.delete(key);\n\t\t}\n\t}\n\n\tforEach(key: K, fn: (value: V) => void): void {\n\t\tconst values = this.map.get(key);\n\n\t\tif (!values) {\n\t\t\treturn;\n\t\t}\n\n\t\tvalues.forEach(fn);\n\t}\n\n\tget(key: K): ReadonlySet<V> {\n\t\tconst values = this.map.get(key);\n\t\tif (!values) {\n\t\t\treturn new Set<V>();\n\t\t}\n\t\treturn values;\n\t}\n}\n\nexport function mapsStrictEqualIgnoreOrder(a: Map<unknown, unknown>, b: Map<unknown, unknown>): boolean {\n\tif (a === b) {\n\t\treturn true;\n\t}\n\n\tif (a.size !== b.size) {\n\t\treturn false;\n\t}\n\n\tfor (const [key, value] of a) {\n\t\tif (!b.has(key) || b.get(key) !== value) {\n\t\t\treturn false;\n\t\t}\n\t}\n\n\tfor (const [key] of b) {\n\t\tif (!a.has(key)) {\n\t\t\treturn false;\n\t\t}\n\t}\n\n\treturn true;\n}\n", "/*---------------------------------------------------------------------------------------------\n *  Copyright (c) Microsoft Corporation. All rights reserved.\n *  Licensed under the MIT License. See License.txt in the project root for license information.\n *--------------------------------------------------------------------------------------------*/\n\nexport namespace Iterable {\n\n\texport function is<T = any>(thing: any): thing is Iterable<T> {\n\t\treturn thing && typeof thing === 'object' && typeof thing[Symbol.iterator] === 'function';\n\t}\n\n\tconst _empty: Iterable<any> = Object.freeze([]);\n\texport function empty<T = any>(): Iterable<T> {\n\t\treturn _empty;\n\t}\n\n\texport function* single<T>(element: T): Iterable<T> {\n\t\tyield element;\n\t}\n\n\texport function wrap<T>(iterableOrElement: Iterable<T> | T): Iterable<T> {\n\t\tif (is(iterableOrElement)) {\n\t\t\treturn iterableOrElement;\n\t\t} else {\n\t\t\treturn single(iterableOrElement);\n\t\t}\n\t}\n\n\texport function from<T>(iterable: Iterable<T> | undefined | null): Iterable<T> {\n\t\treturn iterable || _empty;\n\t}\n\n\texport function* reverse<T>(array: Array<T>): Iterable<T> {\n\t\tfor (let i = array.length - 1; i >= 0; i--) {\n\t\t\tyield array[i];\n\t\t}\n\t}\n\n\texport function isEmpty<T>(iterable: Iterable<T> | undefined | null): boolean {\n\t\treturn !iterable || iterable[Symbol.iterator]().next().done === true;\n\t}\n\n\texport function first<T>(iterable: Iterable<T>): T | undefined {\n\t\treturn iterable[Symbol.iterator]().next().value;\n\t}\n\n\texport function some<T>(iterable: Iterable<T>, predicate: (t: T, i: number) => unknown): boolean {\n\t\tlet i = 0;\n\t\tfor (const element of iterable) {\n\t\t\tif (predicate(element, i++)) {\n\t\t\t\treturn true;\n\t\t\t}\n\t\t}\n\t\treturn false;\n\t}\n\n\texport function find<T, R extends T>(iterable: Iterable<T>, predicate: (t: T) => t is R): R | undefined;\n\texport function find<T>(iterable: Iterable<T>, predicate: (t: T) => boolean): T | undefined;\n\texport function find<T>(iterable: Iterable<T>, predicate: (t: T) => boolean): T | undefined {\n\t\tfor (const element of iterable) {\n\t\t\tif (predicate(element)) {\n\t\t\t\treturn element;\n\t\t\t}\n\t\t}\n\n\t\treturn undefined;\n\t}\n\n\texport function filter<T, R extends T>(iterable: Iterable<T>, predicate: (t: T) => t is R): Iterable<R>;\n\texport function filter<T>(iterable: Iterable<T>, predicate: (t: T) => boolean): Iterable<T>;\n\texport function* filter<T>(iterable: Iterable<T>, predicate: (t: T) => boolean): Iterable<T> {\n\t\tfor (const element of iterable) {\n\t\t\tif (predicate(element)) {\n\t\t\t\tyield element;\n\t\t\t}\n\t\t}\n\t}\n\n\texport function* map<T, R>(iterable: Iterable<T>, fn: (t: T, index: number) => R): Iterable<R> {\n\t\tlet index = 0;\n\t\tfor (const element of iterable) {\n\t\t\tyield fn(element, index++);\n\t\t}\n\t}\n\n\texport function* flatMap<T, R>(iterable: Iterable<T>, fn: (t: T, index: number) => Iterable<R>): Iterable<R> {\n\t\tlet index = 0;\n\t\tfor (const element of iterable) {\n\t\t\tyield* fn(element, index++);\n\t\t}\n\t}\n\n\texport function* concat<T>(...iterables: Iterable<T>[]): Iterable<T> {\n\t\tfor (const iterable of iterables) {\n\t\t\tyield* iterable;\n\t\t}\n\t}\n\n\texport function reduce<T, R>(iterable: Iterable<T>, reducer: (previousValue: R, currentValue: T) => R, initialValue: R): R {\n\t\tlet value = initialValue;\n\t\tfor (const element of iterable) {\n\t\t\tvalue = reducer(value, element);\n\t\t}\n\t\treturn value;\n\t}\n\n\t/**\n\t * Returns an iterable slice of the array, with the same semantics as `array.slice()`.\n\t */\n\texport function* slice<T>(arr: ReadonlyArray<T>, from: number, to = arr.length): Iterable<T> {\n\t\tif (from < 0) {\n\t\t\tfrom += arr.length;\n\t\t}\n\n\t\tif (to < 0) {\n\t\t\tto += arr.length;\n\t\t} else if (to > arr.length) {\n\t\t\tto = arr.length;\n\t\t}\n\n\t\tfor (; from < to; from++) {\n\t\t\tyield arr[from];\n\t\t}\n\t}\n\n\t/**\n\t * Consumes `atMost` elements from iterable and returns the consumed elements,\n\t * and an iterable for the rest of the elements.\n\t */\n\texport function consume<T>(iterable: Iterable<T>, atMost: number = Number.POSITIVE_INFINITY): [T[], Iterable<T>] {\n\t\tconst consumed: T[] = [];\n\n\t\tif (atMost === 0) {\n\t\t\treturn [consumed, iterable];\n\t\t}\n\n\t\tconst iterator = iterable[Symbol.iterator]();\n\n\t\tfor (let i = 0; i < atMost; i++) {\n\t\t\tconst next = iterator.next();\n\n\t\t\tif (next.done) {\n\t\t\t\treturn [consumed, Iterable.empty()];\n\t\t\t}\n\n\t\t\tconsumed.push(next.value);\n\t\t}\n\n\t\treturn [consumed, { [Symbol.iterator]() { return iterator; } }];\n\t}\n\n\texport async function asyncToArray<T>(iterable: AsyncIterable<T>): Promise<T[]> {\n\t\tconst result: T[] = [];\n\t\tfor await (const item of iterable) {\n\t\t\tresult.push(item);\n\t\t}\n\t\treturn Promise.resolve(result);\n\t}\n}\n", "/*---------------------------------------------------------------------------------------------\n *  Copyright (c) Microsoft Corporation. All rights reserved.\n *  Licensed under the MIT License. See License.txt in the project root for license information.\n *--------------------------------------------------------------------------------------------*/\n\nimport { compareBy, numberComparator } from 'vs/base/common/arrays';\nimport { groupBy } from 'vs/base/common/collections';\nimport { SetMap } from './map';\nimport { createSingleCallFunction } from 'vs/base/common/functional';\nimport { Iterable } from 'vs/base/common/iterator';\n\n// #region Disposable Tracking\n\n/**\n * Enables logging of potentially leaked disposables.\n *\n * A disposable is considered leaked if it is not disposed or not registered as the child of\n * another disposable. This tracking is very simple an only works for classes that either\n * extend Disposable or use a DisposableStore. This means there are a lot of false positives.\n */\nconst TRACK_DISPOSABLES = false;\nlet disposableTracker: IDisposableTracker | null = null;\n\nexport interface IDisposableTracker {\n\t/**\n\t * Is called on construction of a disposable.\n\t*/\n\ttrackDisposable(disposable: IDisposable): void;\n\n\t/**\n\t * Is called when a disposable is registered as child of another disposable (e.g. {@link DisposableStore}).\n\t * If parent is `null`, the disposable is removed from its former parent.\n\t*/\n\tsetParent(child: IDisposable, parent: IDisposable | null): void;\n\n\t/**\n\t * Is called after a disposable is disposed.\n\t*/\n\tmarkAsDisposed(disposable: IDisposable): void;\n\n\t/**\n\t * Indicates that the given object is a singleton which does not need to be disposed.\n\t*/\n\tmarkAsSingleton(disposable: IDisposable): void;\n}\n\nexport interface DisposableInfo {\n\tvalue: IDisposable;\n\tsource: string | null;\n\tparent: IDisposable | null;\n\tisSingleton: boolean;\n\tidx: number;\n}\n\nexport class DisposableTracker implements IDisposableTracker {\n\tprivate static idx = 0;\n\n\tprivate readonly livingDisposables = new Map<IDisposable, DisposableInfo>();\n\n\tprivate getDisposableData(d: IDisposable): DisposableInfo {\n\t\tlet val = this.livingDisposables.get(d);\n\t\tif (!val) {\n\t\t\tval = { parent: null, source: null, isSingleton: false, value: d, idx: DisposableTracker.idx++ };\n\t\t\tthis.livingDisposables.set(d, val);\n\t\t}\n\t\treturn val;\n\t}\n\n\ttrackDisposable(d: IDisposable): void {\n\t\tconst data = this.getDisposableData(d);\n\t\tif (!data.source) {\n\t\t\tdata.source =\n\t\t\t\tnew Error().stack!;\n\t\t}\n\t}\n\n\tsetParent(child: IDisposable, parent: IDisposable | null): void {\n\t\tconst data = this.getDisposableData(child);\n\t\tdata.parent = parent;\n\t}\n\n\tmarkAsDisposed(x: IDisposable): void {\n\t\tthis.livingDisposables.delete(x);\n\t}\n\n\tmarkAsSingleton(disposable: IDisposable): void {\n\t\tthis.getDisposableData(disposable).isSingleton = true;\n\t}\n\n\tprivate getRootParent(data: DisposableInfo, cache: Map<DisposableInfo, DisposableInfo>): DisposableInfo {\n\t\tconst cacheValue = cache.get(data);\n\t\tif (cacheValue) {\n\t\t\treturn cacheValue;\n\t\t}\n\n\t\tconst result = data.parent ? this.getRootParent(this.getDisposableData(data.parent), cache) : data;\n\t\tcache.set(data, result);\n\t\treturn result;\n\t}\n\n\tgetTrackedDisposables(): IDisposable[] {\n\t\tconst rootParentCache = new Map<DisposableInfo, DisposableInfo>();\n\n\t\tconst leaking = [...this.livingDisposables.entries()]\n\t\t\t.filter(([, v]) => v.source !== null && !this.getRootParent(v, rootParentCache).isSingleton)\n\t\t\t.flatMap(([k]) => k);\n\n\t\treturn leaking;\n\t}\n\n\tcomputeLeakingDisposables(maxReported = 10, preComputedLeaks?: DisposableInfo[]): { leaks: DisposableInfo[]; details: string } | undefined {\n\t\tlet uncoveredLeakingObjs: DisposableInfo[] | undefined;\n\t\tif (preComputedLeaks) {\n\t\t\tuncoveredLeakingObjs = preComputedLeaks;\n\t\t} else {\n\t\t\tconst rootParentCache = new Map<DisposableInfo, DisposableInfo>();\n\n\t\t\tconst leakingObjects = [...this.livingDisposables.values()]\n\t\t\t\t.filter((info) => info.source !== null && !this.getRootParent(info, rootParentCache).isSingleton);\n\n\t\t\tif (leakingObjects.length === 0) {\n\t\t\t\treturn;\n\t\t\t}\n\t\t\tconst leakingObjsSet = new Set(leakingObjects.map(o => o.value));\n\n\t\t\t// Remove all objects that are a child of other leaking objects. Assumes there are no cycles.\n\t\t\tuncoveredLeakingObjs = leakingObjects.filter(l => {\n\t\t\t\treturn !(l.parent && leakingObjsSet.has(l.parent));\n\t\t\t});\n\n\t\t\tif (uncoveredLeakingObjs.length === 0) {\n\t\t\t\tthrow new Error('There are cyclic diposable chains!');\n\t\t\t}\n\t\t}\n\n\t\tif (!uncoveredLeakingObjs) {\n\t\t\treturn undefined;\n\t\t}\n\n\t\tfunction getStackTracePath(leaking: DisposableInfo): string[] {\n\t\t\tfunction removePrefix(array: string[], linesToRemove: (string | RegExp)[]) {\n\t\t\t\twhile (array.length > 0 && linesToRemove.some(regexp => typeof regexp === 'string' ? regexp === array[0] : array[0].match(regexp))) {\n\t\t\t\t\tarray.shift();\n\t\t\t\t}\n\t\t\t}\n\n\t\t\tconst lines = leaking.source!.split('\\n').map(p => p.trim().replace('at ', '')).filter(l => l !== '');\n\t\t\tremovePrefix(lines, ['Error', /^trackDisposable \\(.*\\)$/, /^DisposableTracker.trackDisposable \\(.*\\)$/]);\n\t\t\treturn lines.reverse();\n\t\t}\n\n\t\tconst stackTraceStarts = new SetMap<string, DisposableInfo>();\n\t\tfor (const leaking of uncoveredLeakingObjs) {\n\t\t\tconst stackTracePath = getStackTracePath(leaking);\n\t\t\tfor (let i = 0; i <= stackTracePath.length; i++) {\n\t\t\t\tstackTraceStarts.add(stackTracePath.slice(0, i).join('\\n'), leaking);\n\t\t\t}\n\t\t}\n\n\t\t// Put earlier leaks first\n\t\tuncoveredLeakingObjs.sort(compareBy(l => l.idx, numberComparator));\n\n\t\tlet message = '';\n\n\t\tlet i = 0;\n\t\tfor (const leaking of uncoveredLeakingObjs.slice(0, maxReported)) {\n\t\t\ti++;\n\t\t\tconst stackTracePath = getStackTracePath(leaking);\n\t\t\tconst stackTraceFormattedLines = [];\n\n\t\t\tfor (let i = 0; i < stackTracePath.length; i++) {\n\t\t\t\tlet line = stackTracePath[i];\n\t\t\t\tconst starts = stackTraceStarts.get(stackTracePath.slice(0, i + 1).join('\\n'));\n\t\t\t\tline = `(shared with ${starts.size}/${uncoveredLeakingObjs.length} leaks) at ${line}`;\n\n\t\t\t\tconst prevStarts = stackTraceStarts.get(stackTracePath.slice(0, i).join('\\n'));\n\t\t\t\tconst continuations = groupBy([...prevStarts].map(d => getStackTracePath(d)[i]), v => v);\n\t\t\t\tdelete continuations[stackTracePath[i]];\n\t\t\t\tfor (const [cont, set] of Object.entries(continuations)) {\n\t\t\t\t\tstackTraceFormattedLines.unshift(`    - stacktraces of ${set.length} other leaks continue with ${cont}`);\n\t\t\t\t}\n\n\t\t\t\tstackTraceFormattedLines.unshift(line);\n\t\t\t}\n\n\t\t\tmessage += `\\n\\n\\n==================== Leaking disposable ${i}/${uncoveredLeakingObjs.length}: ${leaking.value.constructor.name} ====================\\n${stackTraceFormattedLines.join('\\n')}\\n============================================================\\n\\n`;\n\t\t}\n\n\t\tif (uncoveredLeakingObjs.length > maxReported) {\n\t\t\tmessage += `\\n\\n\\n... and ${uncoveredLeakingObjs.length - maxReported} more leaking disposables\\n\\n`;\n\t\t}\n\n\t\treturn { leaks: uncoveredLeakingObjs, details: message };\n\t}\n}\n\nexport function setDisposableTracker(tracker: IDisposableTracker | null): void {\n\tdisposableTracker = tracker;\n}\n\nif (TRACK_DISPOSABLES) {\n\tconst __is_disposable_tracked__ = '__is_disposable_tracked__';\n\tsetDisposableTracker(new class implements IDisposableTracker {\n\t\ttrackDisposable(x: IDisposable): void {\n\t\t\tconst stack = new Error('Potentially leaked disposable').stack!;\n\t\t\tsetTimeout(() => {\n\t\t\t\tif (!(x as any)[__is_disposable_tracked__]) {\n\t\t\t\t\tconsole.log(stack);\n\t\t\t\t}\n\t\t\t}, 3000);\n\t\t}\n\n\t\tsetParent(child: IDisposable, parent: IDisposable | null): void {\n\t\t\tif (child && child !== Disposable.None) {\n\t\t\t\ttry {\n\t\t\t\t\t(child as any)[__is_disposable_tracked__] = true;\n\t\t\t\t} catch {\n\t\t\t\t\t// noop\n\t\t\t\t}\n\t\t\t}\n\t\t}\n\n\t\tmarkAsDisposed(disposable: IDisposable): void {\n\t\t\tif (disposable && disposable !== Disposable.None) {\n\t\t\t\ttry {\n\t\t\t\t\t(disposable as any)[__is_disposable_tracked__] = true;\n\t\t\t\t} catch {\n\t\t\t\t\t// noop\n\t\t\t\t}\n\t\t\t}\n\t\t}\n\t\tmarkAsSingleton(disposable: IDisposable): void { }\n\t});\n}\n\nexport function trackDisposable<T extends IDisposable>(x: T): T {\n\tdisposableTracker?.trackDisposable(x);\n\treturn x;\n}\n\nexport function markAsDisposed(disposable: IDisposable): void {\n\tdisposableTracker?.markAsDisposed(disposable);\n}\n\nfunction setParentOfDisposable(child: IDisposable, parent: IDisposable | null): void {\n\tdisposableTracker?.setParent(child, parent);\n}\n\nfunction setParentOfDisposables(children: IDisposable[], parent: IDisposable | null): void {\n\tif (!disposableTracker) {\n\t\treturn;\n\t}\n\tfor (const child of children) {\n\t\tdisposableTracker.setParent(child, parent);\n\t}\n}\n\n/**\n * Indicates that the given object is a singleton which does not need to be disposed.\n*/\nexport function markAsSingleton<T extends IDisposable>(singleton: T): T {\n\tdisposableTracker?.markAsSingleton(singleton);\n\treturn singleton;\n}\n\n// #endregion\n\n/**\n * An object that performs a cleanup operation when `.dispose()` is called.\n *\n * Some examples of how disposables are used:\n *\n * - An event listener that removes itself when `.dispose()` is called.\n * - A resource such as a file system watcher that cleans up the resource when `.dispose()` is called.\n * - The return value from registering a provider. When `.dispose()` is called, the provider is unregistered.\n */\nexport interface IDisposable {\n\tdispose(): void;\n}\n\n/**\n * Check if `thing` is {@link IDisposable disposable}.\n */\nexport function isDisposable<E extends any>(thing: E): thing is E & IDisposable {\n\treturn typeof thing === 'object' && thing !== null && typeof (<IDisposable><any>thing).dispose === 'function' && (<IDisposable><any>thing).dispose.length === 0;\n}\n\n/**\n * Disposes of the value(s) passed in.\n */\nexport function dispose<T extends IDisposable>(disposable: T): T;\nexport function dispose<T extends IDisposable>(disposable: T | undefined): T | undefined;\nexport function dispose<T extends IDisposable, A extends Iterable<T> = Iterable<T>>(disposables: A): A;\nexport function dispose<T extends IDisposable>(disposables: Array<T>): Array<T>;\nexport function dispose<T extends IDisposable>(disposables: ReadonlyArray<T>): ReadonlyArray<T>;\nexport function dispose<T extends IDisposable>(arg: T | Iterable<T> | undefined): any {\n\tif (Iterable.is(arg)) {\n\t\tconst errors: any[] = [];\n\n\t\tfor (const d of arg) {\n\t\t\tif (d) {\n\t\t\t\ttry {\n\t\t\t\t\td.dispose();\n\t\t\t\t} catch (e) {\n\t\t\t\t\terrors.push(e);\n\t\t\t\t}\n\t\t\t}\n\t\t}\n\n\t\tif (errors.length === 1) {\n\t\t\tthrow errors[0];\n\t\t} else if (errors.length > 1) {\n\t\t\tthrow new AggregateError(errors, 'Encountered errors while disposing of store');\n\t\t}\n\n\t\treturn Array.isArray(arg) ? [] : arg;\n\t} else if (arg) {\n\t\targ.dispose();\n\t\treturn arg;\n\t}\n}\n\nexport function disposeIfDisposable<T extends IDisposable | object>(disposables: Array<T>): Array<T> {\n\tfor (const d of disposables) {\n\t\tif (isDisposable(d)) {\n\t\t\td.dispose();\n\t\t}\n\t}\n\treturn [];\n}\n\n/**\n * Combine multiple disposable values into a single {@link IDisposable}.\n */\nexport function combinedDisposable(...disposables: IDisposable[]): IDisposable {\n\tconst parent = toDisposable(() => dispose(disposables));\n\tsetParentOfDisposables(disposables, parent);\n\treturn parent;\n}\n\n/**\n * Turn a function that implements dispose into an {@link IDisposable}.\n *\n * @param fn Clean up function, guaranteed to be called only **once**.\n */\nexport function toDisposable(fn: () => void): IDisposable {\n\tconst self = trackDisposable({\n\t\tdispose: createSingleCallFunction(() => {\n\t\t\tmarkAsDisposed(self);\n\t\t\tfn();\n\t\t})\n\t});\n\treturn self;\n}\n\n/**\n * Manages a collection of disposable values.\n *\n * This is the preferred way to manage multiple disposables. A `DisposableStore` is safer to work with than an\n * `IDisposable[]` as it considers edge cases, such as registering the same value multiple times or adding an item to a\n * store that has already been disposed of.\n */\nexport class DisposableStore implements IDisposable {\n\n\tstatic DISABLE_DISPOSED_WARNING = false;\n\n\tprivate readonly _toDispose = new Set<IDisposable>();\n\tprivate _isDisposed = false;\n\n\tconstructor() {\n\t\ttrackDisposable(this);\n\t}\n\n\t/**\n\t * Dispose of all registered disposables and mark this object as disposed.\n\t *\n\t * Any future disposables added to this object will be disposed of on `add`.\n\t */\n\tpublic dispose(): void {\n\t\tif (this._isDisposed) {\n\t\t\treturn;\n\t\t}\n\n\t\tmarkAsDisposed(this);\n\t\tthis._isDisposed = true;\n\t\tthis.clear();\n\t}\n\n\t/**\n\t * @return `true` if this object has been disposed of.\n\t */\n\tpublic get isDisposed(): boolean {\n\t\treturn this._isDisposed;\n\t}\n\n\t/**\n\t * Dispose of all registered disposables but do not mark this object as disposed.\n\t */\n\tpublic clear(): void {\n\t\tif (this._toDispose.size === 0) {\n\t\t\treturn;\n\t\t}\n\n\t\ttry {\n\t\t\tdispose(this._toDispose);\n\t\t} finally {\n\t\t\tthis._toDispose.clear();\n\t\t}\n\t}\n\n\t/**\n\t * Add a new {@link IDisposable disposable} to the collection.\n\t */\n\tpublic add<T extends IDisposable>(o: T): T {\n\t\tif (!o) {\n\t\t\treturn o;\n\t\t}\n\t\tif ((o as unknown as DisposableStore) === this) {\n\t\t\tthrow new Error('Cannot register a disposable on itself!');\n\t\t}\n\n\t\tsetParentOfDisposable(o, this);\n\t\tif (this._isDisposed) {\n\t\t\tif (!DisposableStore.DISABLE_DISPOSED_WARNING) {\n\t\t\t\tconsole.warn(new Error('Trying to add a disposable to a DisposableStore that has already been disposed of. The added object will be leaked!').stack);\n\t\t\t}\n\t\t} else {\n\t\t\tthis._toDispose.add(o);\n\t\t}\n\n\t\treturn o;\n\t}\n\n\t/**\n\t * Deletes a disposable from store and disposes of it. This will not throw or warn and proceed to dispose the\n\t * disposable even when the disposable is not part in the store.\n\t */\n\tpublic delete<T extends IDisposable>(o: T): void {\n\t\tif (!o) {\n\t\t\treturn;\n\t\t}\n\t\tif ((o as unknown as DisposableStore) === this) {\n\t\t\tthrow new Error('Cannot dispose a disposable on itself!');\n\t\t}\n\t\tthis._toDispose.delete(o);\n\t\to.dispose();\n\t}\n\n\t/**\n\t * Deletes the value from the store, but does not dispose it.\n\t */\n\tpublic deleteAndLeak<T extends IDisposable>(o: T): void {\n\t\tif (!o) {\n\t\t\treturn;\n\t\t}\n\t\tif (this._toDispose.has(o)) {\n\t\t\tthis._toDispose.delete(o);\n\t\t\tsetParentOfDisposable(o, null);\n\t\t}\n\t}\n}\n\n/**\n * Abstract base class for a {@link IDisposable disposable} object.\n *\n * Subclasses can {@linkcode _register} disposables that will be automatically cleaned up when this object is disposed of.\n */\nexport abstract class Disposable implements IDisposable {\n\n\t/**\n\t * A disposable that does nothing when it is disposed of.\n\t *\n\t * TODO: This should not be a static property.\n\t */\n\tstatic readonly None = Object.freeze<IDisposable>({ dispose() { } });\n\n\tprotected readonly _store = new DisposableStore();\n\n\tconstructor() {\n\t\ttrackDisposable(this);\n\t\tsetParentOfDisposable(this._store, this);\n\t}\n\n\tpublic dispose(): void {\n\t\tmarkAsDisposed(this);\n\n\t\tthis._store.dispose();\n\t}\n\n\t/**\n\t * Adds `o` to the collection of disposables managed by this object.\n\t */\n\tprotected _register<T extends IDisposable>(o: T): T {\n\t\tif ((o as unknown as Disposable) === this) {\n\t\t\tthrow new Error('Cannot register a disposable on itself!');\n\t\t}\n\t\treturn this._store.add(o);\n\t}\n}\n\n/**\n * Manages the lifecycle of a disposable value that may be changed.\n *\n * This ensures that when the disposable value is changed, the previously held disposable is disposed of. You can\n * also register a `MutableDisposable` on a `Disposable` to ensure it is automatically cleaned up.\n */\nexport class MutableDisposable<T extends IDisposable> implements IDisposable {\n\tprivate _value?: T;\n\tprivate _isDisposed = false;\n\n\tconstructor() {\n\t\ttrackDisposable(this);\n\t}\n\n\tget value(): T | undefined {\n\t\treturn this._isDisposed ? undefined : this._value;\n\t}\n\n\tset value(value: T | undefined) {\n\t\tif (this._isDisposed || value === this._value) {\n\t\t\treturn;\n\t\t}\n\n\t\tthis._value?.dispose();\n\t\tif (value) {\n\t\t\tsetParentOfDisposable(value, this);\n\t\t}\n\t\tthis._value = value;\n\t}\n\n\t/**\n\t * Resets the stored value and disposed of the previously stored value.\n\t */\n\tclear(): void {\n\t\tthis.value = undefined;\n\t}\n\n\tdispose(): void {\n\t\tthis._isDisposed = true;\n\t\tmarkAsDisposed(this);\n\t\tthis._value?.dispose();\n\t\tthis._value = undefined;\n\t}\n\n\t/**\n\t * Clears the value, but does not dispose it.\n\t * The old value is returned.\n\t*/\n\tclearAndLeak(): T | undefined {\n\t\tconst oldValue = this._value;\n\t\tthis._value = undefined;\n\t\tif (oldValue) {\n\t\t\tsetParentOfDisposable(oldValue, null);\n\t\t}\n\t\treturn oldValue;\n\t}\n}\n\n/**\n * Manages the lifecycle of a disposable value that may be changed like {@link MutableDisposable}, but the value must\n * exist and cannot be undefined.\n */\nexport class MandatoryMutableDisposable<T extends IDisposable> implements IDisposable {\n\tprivate readonly _disposable = new MutableDisposable<T>();\n\tprivate _isDisposed = false;\n\n\tconstructor(initialValue: T) {\n\t\tthis._disposable.value = initialValue;\n\t}\n\n\tget value(): T {\n\t\treturn this._disposable.value!;\n\t}\n\n\tset value(value: T) {\n\t\tif (this._isDisposed || value === this._disposable.value) {\n\t\t\treturn;\n\t\t}\n\t\tthis._disposable.value = value;\n\t}\n\n\tdispose() {\n\t\tthis._isDisposed = true;\n\t\tthis._disposable.dispose();\n\t}\n}\n\nexport class RefCountedDisposable {\n\n\tprivate _counter: number = 1;\n\n\tconstructor(\n\t\tprivate readonly _disposable: IDisposable,\n\t) { }\n\n\tacquire() {\n\t\tthis._counter++;\n\t\treturn this;\n\t}\n\n\trelease() {\n\t\tif (--this._counter === 0) {\n\t\t\tthis._disposable.dispose();\n\t\t}\n\t\treturn this;\n\t}\n}\n\n/**\n * A safe disposable can be `unset` so that a leaked reference (listener)\n * can be cut-off.\n */\nexport class SafeDisposable implements IDisposable {\n\n\tdispose: () => void = () => { };\n\tunset: () => void = () => { };\n\tisset: () => boolean = () => false;\n\n\tconstructor() {\n\t\ttrackDisposable(this);\n\t}\n\n\tset(fn: Function) {\n\t\tlet callback: Function | undefined = fn;\n\t\tthis.unset = () => callback = undefined;\n\t\tthis.isset = () => callback !== undefined;\n\t\tthis.dispose = () => {\n\t\t\tif (callback) {\n\t\t\t\tcallback();\n\t\t\t\tcallback = undefined;\n\t\t\t\tmarkAsDisposed(this);\n\t\t\t}\n\t\t};\n\t\treturn this;\n\t}\n}\n\nexport interface IReference<T> extends IDisposable {\n\treadonly object: T;\n}\n\nexport abstract class ReferenceCollection<T> {\n\n\tprivate readonly references: Map<string, { readonly object: T; counter: number }> = new Map();\n\n\tacquire(key: string, ...args: any[]): IReference<T> {\n\t\tlet reference = this.references.get(key);\n\n\t\tif (!reference) {\n\t\t\treference = { counter: 0, object: this.createReferencedObject(key, ...args) };\n\t\t\tthis.references.set(key, reference);\n\t\t}\n\n\t\tconst { object } = reference;\n\t\tconst dispose = createSingleCallFunction(() => {\n\t\t\tif (--reference.counter === 0) {\n\t\t\t\tthis.destroyReferencedObject(key, reference.object);\n\t\t\t\tthis.references.delete(key);\n\t\t\t}\n\t\t});\n\n\t\treference.counter++;\n\n\t\treturn { object, dispose };\n\t}\n\n\tprotected abstract createReferencedObject(key: string, ...args: any[]): T;\n\tprotected abstract destroyReferencedObject(key: string, object: T): void;\n}\n\n/**\n * Unwraps a reference collection of promised values. Makes sure\n * references are disposed whenever promises get rejected.\n */\nexport class AsyncReferenceCollection<T> {\n\n\tconstructor(private referenceCollection: ReferenceCollection<Promise<T>>) { }\n\n\tasync acquire(key: string, ...args: any[]): Promise<IReference<T>> {\n\t\tconst ref = this.referenceCollection.acquire(key, ...args);\n\n\t\ttry {\n\t\t\tconst object = await ref.object;\n\n\t\t\treturn {\n\t\t\t\tobject,\n\t\t\t\tdispose: () => ref.dispose()\n\t\t\t};\n\t\t} catch (error) {\n\t\t\tref.dispose();\n\t\t\tthrow error;\n\t\t}\n\t}\n}\n\nexport class ImmortalReference<T> implements IReference<T> {\n\tconstructor(public object: T) { }\n\tdispose(): void { /* noop */ }\n}\n\nexport function disposeOnReturn(fn: (store: DisposableStore) => void): void {\n\tconst store = new DisposableStore();\n\ttry {\n\t\tfn(store);\n\t} finally {\n\t\tstore.dispose();\n\t}\n}\n\n/**\n * A map the manages the lifecycle of the values that it stores.\n */\nexport class DisposableMap<K, V extends IDisposable = IDisposable> implements IDisposable {\n\n\tprivate readonly _store = new Map<K, V>();\n\tprivate _isDisposed = false;\n\n\tconstructor() {\n\t\ttrackDisposable(this);\n\t}\n\n\t/**\n\t * Disposes of all stored values and mark this object as disposed.\n\t *\n\t * Trying to use this object after it has been disposed of is an error.\n\t */\n\tdispose(): void {\n\t\tmarkAsDisposed(this);\n\t\tthis._isDisposed = true;\n\t\tthis.clearAndDisposeAll();\n\t}\n\n\t/**\n\t * Disposes of all stored values and clear the map, but DO NOT mark this object as disposed.\n\t */\n\tclearAndDisposeAll(): void {\n\t\tif (!this._store.size) {\n\t\t\treturn;\n\t\t}\n\n\t\ttry {\n\t\t\tdispose(this._store.values());\n\t\t} finally {\n\t\t\tthis._store.clear();\n\t\t}\n\t}\n\n\thas(key: K): boolean {\n\t\treturn this._store.has(key);\n\t}\n\n\tget size(): number {\n\t\treturn this._store.size;\n\t}\n\n\tget(key: K): V | undefined {\n\t\treturn this._store.get(key);\n\t}\n\n\tset(key: K, value: V, skipDisposeOnOverwrite = false): void {\n\t\tif (this._isDisposed) {\n\t\t\tconsole.warn(new Error('Trying to add a disposable to a DisposableMap that has already been disposed of. The added object will be leaked!').stack);\n\t\t}\n\n\t\tif (!skipDisposeOnOverwrite) {\n\t\t\tthis._store.get(key)?.dispose();\n\t\t}\n\n\t\tthis._store.set(key, value);\n\t}\n\n\t/**\n\t * Delete the value stored for `key` from this map and also dispose of it.\n\t */\n\tdeleteAndDispose(key: K): void {\n\t\tthis._store.get(key)?.dispose();\n\t\tthis._store.delete(key);\n\t}\n\n\t/**\n\t * Delete the value stored for `key` from this map but return it. The caller is\n\t * responsible for disposing of the value.\n\t */\n\tdeleteAndLeak(key: K): V | undefined {\n\t\tconst value = this._store.get(key);\n\t\tthis._store.delete(key);\n\t\treturn value;\n\t}\n\n\tkeys(): IterableIterator<K> {\n\t\treturn this._store.keys();\n\t}\n\n\tvalues(): IterableIterator<V> {\n\t\treturn this._store.values();\n\t}\n\n\t[Symbol.iterator](): IterableIterator<[K, V]> {\n\t\treturn this._store[Symbol.iterator]();\n\t}\n}\n", "/*---------------------------------------------------------------------------------------------\n *  Copyright (c) Microsoft Corporation. All rights reserved.\n *  Licensed under the MIT License. See License.txt in the project root for license information.\n *--------------------------------------------------------------------------------------------*/\n\nclass Node<E> {\n\n\tstatic readonly Undefined = new Node<any>(undefined);\n\n\telement: E;\n\tnext: Node<E>;\n\tprev: Node<E>;\n\n\tconstructor(element: E) {\n\t\tthis.element = element;\n\t\tthis.next = Node.Undefined;\n\t\tthis.prev = Node.Undefined;\n\t}\n}\n\nexport class LinkedList<E> {\n\n\tprivate _first: Node<E> = Node.Undefined;\n\tprivate _last: Node<E> = Node.Undefined;\n\tprivate _size: number = 0;\n\n\tget size(): number {\n\t\treturn this._size;\n\t}\n\n\tisEmpty(): boolean {\n\t\treturn this._first === Node.Undefined;\n\t}\n\n\tclear(): void {\n\t\tlet node = this._first;\n\t\twhile (node !== Node.Undefined) {\n\t\t\tconst next = node.next;\n\t\t\tnode.prev = Node.Undefined;\n\t\t\tnode.next = Node.Undefined;\n\t\t\tnode = next;\n\t\t}\n\n\t\tthis._first = Node.Undefined;\n\t\tthis._last = Node.Undefined;\n\t\tthis._size = 0;\n\t}\n\n\tunshift(element: E): () => void {\n\t\treturn this._insert(element, false);\n\t}\n\n\tpush(element: E): () => void {\n\t\treturn this._insert(element, true);\n\t}\n\n\tprivate _insert(element: E, atTheEnd: boolean): () => void {\n\t\tconst newNode = new Node(element);\n\t\tif (this._first === Node.Undefined) {\n\t\t\tthis._first = newNode;\n\t\t\tthis._last = newNode;\n\n\t\t} else if (atTheEnd) {\n\t\t\t// push\n\t\t\tconst oldLast = this._last;\n\t\t\tthis._last = newNode;\n\t\t\tnewNode.prev = oldLast;\n\t\t\toldLast.next = newNode;\n\n\t\t} else {\n\t\t\t// unshift\n\t\t\tconst oldFirst = this._first;\n\t\t\tthis._first = newNode;\n\t\t\tnewNode.next = oldFirst;\n\t\t\toldFirst.prev = newNode;\n\t\t}\n\t\tthis._size += 1;\n\n\t\tlet didRemove = false;\n\t\treturn () => {\n\t\t\tif (!didRemove) {\n\t\t\t\tdidRemove = true;\n\t\t\t\tthis._remove(newNode);\n\t\t\t}\n\t\t};\n\t}\n\n\tshift(): E | undefined {\n\t\tif (this._first === Node.Undefined) {\n\t\t\treturn undefined;\n\t\t} else {\n\t\t\tconst res = this._first.element;\n\t\t\tthis._remove(this._first);\n\t\t\treturn res;\n\t\t}\n\t}\n\n\tpop(): E | undefined {\n\t\tif (this._last === Node.Undefined) {\n\t\t\treturn undefined;\n\t\t} else {\n\t\t\tconst res = this._last.element;\n\t\t\tthis._remove(this._last);\n\t\t\treturn res;\n\t\t}\n\t}\n\n\tprivate _remove(node: Node<E>): void {\n\t\tif (node.prev !== Node.Undefined && node.next !== Node.Undefined) {\n\t\t\t// middle\n\t\t\tconst anchor = node.prev;\n\t\t\tanchor.next = node.next;\n\t\t\tnode.next.prev = anchor;\n\n\t\t} else if (node.prev === Node.Undefined && node.next === Node.Undefined) {\n\t\t\t// only node\n\t\t\tthis._first = Node.Undefined;\n\t\t\tthis._last = Node.Undefined;\n\n\t\t} else if (node.next === Node.Undefined) {\n\t\t\t// last\n\t\t\tthis._last = this._last.prev!;\n\t\t\tthis._last.next = Node.Undefined;\n\n\t\t} else if (node.prev === Node.Undefined) {\n\t\t\t// first\n\t\t\tthis._first = this._first.next!;\n\t\t\tthis._first.prev = Node.Undefined;\n\t\t}\n\n\t\t// done\n\t\tthis._size -= 1;\n\t}\n\n\t*[Symbol.iterator](): Iterator<E> {\n\t\tlet node = this._first;\n\t\twhile (node !== Node.Undefined) {\n\t\t\tyield node.element;\n\t\t\tnode = node.next;\n\t\t}\n\t}\n}\n", "/*---------------------------------------------------------------------------------------------\n *  Copyright (c) Microsoft Corporation. All rights reserved.\n *  Licensed under the MIT License. See License.txt in the project root for license information.\n *--------------------------------------------------------------------------------------------*/\n\n// fake definition so that the valid layers check won't trip on this\ndeclare const globalThis: { performance?: { now(): number } };\n\nconst hasPerformanceNow = (globalThis.performance && typeof globalThis.performance.now === 'function');\n\nexport class StopWatch {\n\n\tprivate _startTime: number;\n\tprivate _stopTime: number;\n\n\tprivate readonly _now: () => number;\n\n\tpublic static create(highResolution?: boolean): StopWatch {\n\t\treturn new StopWatch(highResolution);\n\t}\n\n\tconstructor(highResolution?: boolean) {\n\t\tthis._now = hasPerformanceNow && highResolution === false ? Date.now : globalThis.performance!.now.bind(globalThis.performance);\n\t\tthis._startTime = this._now();\n\t\tthis._stopTime = -1;\n\t}\n\n\tpublic stop(): void {\n\t\tthis._stopTime = this._now();\n\t}\n\n\tpublic reset(): void {\n\t\tthis._startTime = this._now();\n\t\tthis._stopTime = -1;\n\t}\n\n\tpublic elapsed(): number {\n\t\tif (this._stopTime !== -1) {\n\t\t\treturn this._stopTime - this._startTime;\n\t\t}\n\t\treturn this._now() - this._startTime;\n\t}\n}\n", "/*---------------------------------------------------------------------------------------------\n *  Copyright (c) Microsoft Corporation. All rights reserved.\n *  Licensed under the MIT License. See License.txt in the project root for license information.\n *--------------------------------------------------------------------------------------------*/\n\nimport { CancellationToken } from 'vs/base/common/cancellation';\nimport { onUnexpectedError } from 'vs/base/common/errors';\nimport { createSingleCallFunction } from 'vs/base/common/functional';\nimport { combinedDisposable, Disposable, DisposableMap, DisposableStore, IDisposable, toDisposable } from 'vs/base/common/lifecycle';\nimport { LinkedList } from 'vs/base/common/linkedList';\nimport { IObservable, IObserver } from 'vs/base/common/observable';\nimport { StopWatch } from 'vs/base/common/stopwatch';\nimport { MicrotaskDelay } from 'vs/base/common/symbols';\n\n\n// -----------------------------------------------------------------------------------------------------------------------\n// Uncomment the next line to print warnings whenever a listener is GC'ed without having been disposed. This is a LEAK.\n// -----------------------------------------------------------------------------------------------------------------------\nconst _enableListenerGCedWarning = false\n\t// || Boolean(\"TRUE\") // causes a linter warning so that it cannot be pushed\n\t;\n\n// -----------------------------------------------------------------------------------------------------------------------\n// Uncomment the next line to print warnings whenever an emitter with listeners is disposed. That is a sign of code smell.\n// -----------------------------------------------------------------------------------------------------------------------\nconst _enableDisposeWithListenerWarning = false\n\t// || Boolean(\"TRUE\") // causes a linter warning so that it cannot be pushed\n\t;\n\n\n// -----------------------------------------------------------------------------------------------------------------------\n// Uncomment the next line to print warnings whenever a snapshotted event is used repeatedly without cleanup.\n// See https://github.com/microsoft/vscode/issues/142851\n// -----------------------------------------------------------------------------------------------------------------------\nconst _enableSnapshotPotentialLeakWarning = false\n\t// || Boolean(\"TRUE\") // causes a linter warning so that it cannot be pushed\n\t;\n\n/**\n * An event with zero or one parameters that can be subscribed to. The event is a function itself.\n */\nexport interface Event<T> {\n\t(listener: (e: T) => any, thisArgs?: any, disposables?: IDisposable[] | DisposableStore): IDisposable;\n}\n\nexport namespace Event {\n\texport const None: Event<any> = () => Disposable.None;\n\n\tfunction _addLeakageTraceLogic(options: EmitterOptions) {\n\t\tif (_enableSnapshotPotentialLeakWarning) {\n\t\t\tconst { onDidAddListener: origListenerDidAdd } = options;\n\t\t\tconst stack = Stacktrace.create();\n\t\t\tlet count = 0;\n\t\t\toptions.onDidAddListener = () => {\n\t\t\t\tif (++count === 2) {\n\t\t\t\t\tconsole.warn('snapshotted emitter LIKELY used public and SHOULD HAVE BEEN created with DisposableStore. snapshotted here');\n\t\t\t\t\tstack.print();\n\t\t\t\t}\n\t\t\t\torigListenerDidAdd?.();\n\t\t\t};\n\t\t}\n\t}\n\n\t/**\n\t * Given an event, returns another event which debounces calls and defers the listeners to a later task via a shared\n\t * `setTimeout`. The event is converted into a signal (`Event<void>`) to avoid additional object creation as a\n\t * result of merging events and to try prevent race conditions that could arise when using related deferred and\n\t * non-deferred events.\n\t *\n\t * This is useful for deferring non-critical work (eg. general UI updates) to ensure it does not block critical work\n\t * (eg. latency of keypress to text rendered).\n\t *\n\t * *NOTE* that this function returns an `Event` and it MUST be called with a `DisposableStore` whenever the returned\n\t * event is accessible to \"third parties\", e.g the event is a public property. Otherwise a leaked listener on the\n\t * returned event causes this utility to leak a listener on the original event.\n\t *\n\t * @param event The event source for the new event.\n\t * @param disposable A disposable store to add the new EventEmitter to.\n\t */\n\texport function defer(event: Event<unknown>, disposable?: DisposableStore): Event<void> {\n\t\treturn debounce<unknown, void>(event, () => void 0, 0, undefined, true, undefined, disposable);\n\t}\n\n\t/**\n\t * Given an event, returns another event which only fires once.\n\t *\n\t * @param event The event source for the new event.\n\t */\n\texport function once<T>(event: Event<T>): Event<T> {\n\t\treturn (listener, thisArgs = null, disposables?) => {\n\t\t\t// we need this, in case the event fires during the listener call\n\t\t\tlet didFire = false;\n\t\t\tlet result: IDisposable | undefined = undefined;\n\t\t\tresult = event(e => {\n\t\t\t\tif (didFire) {\n\t\t\t\t\treturn;\n\t\t\t\t} else if (result) {\n\t\t\t\t\tresult.dispose();\n\t\t\t\t} else {\n\t\t\t\t\tdidFire = true;\n\t\t\t\t}\n\n\t\t\t\treturn listener.call(thisArgs, e);\n\t\t\t}, null, disposables);\n\n\t\t\tif (didFire) {\n\t\t\t\tresult.dispose();\n\t\t\t}\n\n\t\t\treturn result;\n\t\t};\n\t}\n\n\t/**\n\t * Maps an event of one type into an event of another type using a mapping function, similar to how\n\t * `Array.prototype.map` works.\n\t *\n\t * *NOTE* that this function returns an `Event` and it MUST be called with a `DisposableStore` whenever the returned\n\t * event is accessible to \"third parties\", e.g the event is a public property. Otherwise a leaked listener on the\n\t * returned event causes this utility to leak a listener on the original event.\n\t *\n\t * @param event The event source for the new event.\n\t * @param map The mapping function.\n\t * @param disposable A disposable store to add the new EventEmitter to.\n\t */\n\texport function map<I, O>(event: Event<I>, map: (i: I) => O, disposable?: DisposableStore): Event<O> {\n\t\treturn snapshot((listener, thisArgs = null, disposables?) => event(i => listener.call(thisArgs, map(i)), null, disposables), disposable);\n\t}\n\n\t/**\n\t * Wraps an event in another event that performs some function on the event object before firing.\n\t *\n\t * *NOTE* that this function returns an `Event` and it MUST be called with a `DisposableStore` whenever the returned\n\t * event is accessible to \"third parties\", e.g the event is a public property. Otherwise a leaked listener on the\n\t * returned event causes this utility to leak a listener on the original event.\n\t *\n\t * @param event The event source for the new event.\n\t * @param each The function to perform on the event object.\n\t * @param disposable A disposable store to add the new EventEmitter to.\n\t */\n\texport function forEach<I>(event: Event<I>, each: (i: I) => void, disposable?: DisposableStore): Event<I> {\n\t\treturn snapshot((listener, thisArgs = null, disposables?) => event(i => { each(i); listener.call(thisArgs, i); }, null, disposables), disposable);\n\t}\n\n\t/**\n\t * Wraps an event in another event that fires only when some condition is met.\n\t *\n\t * *NOTE* that this function returns an `Event` and it MUST be called with a `DisposableStore` whenever the returned\n\t * event is accessible to \"third parties\", e.g the event is a public property. Otherwise a leaked listener on the\n\t * returned event causes this utility to leak a listener on the original event.\n\t *\n\t * @param event The event source for the new event.\n\t * @param filter The filter function that defines the condition. The event will fire for the object if this function\n\t * returns true.\n\t * @param disposable A disposable store to add the new EventEmitter to.\n\t */\n\texport function filter<T, U>(event: Event<T | U>, filter: (e: T | U) => e is T, disposable?: DisposableStore): Event<T>;\n\texport function filter<T>(event: Event<T>, filter: (e: T) => boolean, disposable?: DisposableStore): Event<T>;\n\texport function filter<T, R>(event: Event<T | R>, filter: (e: T | R) => e is R, disposable?: DisposableStore): Event<R>;\n\texport function filter<T>(event: Event<T>, filter: (e: T) => boolean, disposable?: DisposableStore): Event<T> {\n\t\treturn snapshot((listener, thisArgs = null, disposables?) => event(e => filter(e) && listener.call(thisArgs, e), null, disposables), disposable);\n\t}\n\n\t/**\n\t * Given an event, returns the same event but typed as `Event<void>`.\n\t */\n\texport function signal<T>(event: Event<T>): Event<void> {\n\t\treturn event as Event<any> as Event<void>;\n\t}\n\n\t/**\n\t * Given a collection of events, returns a single event which emits whenever any of the provided events emit.\n\t */\n\texport function any<T>(...events: Event<T>[]): Event<T>;\n\texport function any(...events: Event<any>[]): Event<void>;\n\texport function any<T>(...events: Event<T>[]): Event<T> {\n\t\treturn (listener, thisArgs = null, disposables?) => {\n\t\t\tconst disposable = combinedDisposable(...events.map(event => event(e => listener.call(thisArgs, e))));\n\t\t\treturn addAndReturnDisposable(disposable, disposables);\n\t\t};\n\t}\n\n\t/**\n\t * *NOTE* that this function returns an `Event` and it MUST be called with a `DisposableStore` whenever the returned\n\t * event is accessible to \"third parties\", e.g the event is a public property. Otherwise a leaked listener on the\n\t * returned event causes this utility to leak a listener on the original event.\n\t */\n\texport function reduce<I, O>(event: Event<I>, merge: (last: O | undefined, event: I) => O, initial?: O, disposable?: DisposableStore): Event<O> {\n\t\tlet output: O | undefined = initial;\n\n\t\treturn map<I, O>(event, e => {\n\t\t\toutput = merge(output, e);\n\t\t\treturn output;\n\t\t}, disposable);\n\t}\n\n\tfunction snapshot<T>(event: Event<T>, disposable: DisposableStore | undefined): Event<T> {\n\t\tlet listener: IDisposable | undefined;\n\n\t\tconst options: EmitterOptions | undefined = {\n\t\t\tonWillAddFirstListener() {\n\t\t\t\tlistener = event(emitter.fire, emitter);\n\t\t\t},\n\t\t\tonDidRemoveLastListener() {\n\t\t\t\tlistener?.dispose();\n\t\t\t}\n\t\t};\n\n\t\tif (!disposable) {\n\t\t\t_addLeakageTraceLogic(options);\n\t\t}\n\n\t\tconst emitter = new Emitter<T>(options);\n\n\t\tdisposable?.add(emitter);\n\n\t\treturn emitter.event;\n\t}\n\n\t/**\n\t * Adds the IDisposable to the store if it's set, and returns it. Useful to\n\t * Event function implementation.\n\t */\n\tfunction addAndReturnDisposable<T extends IDisposable>(d: T, store: DisposableStore | IDisposable[] | undefined): T {\n\t\tif (store instanceof Array) {\n\t\t\tstore.push(d);\n\t\t} else if (store) {\n\t\t\tstore.add(d);\n\t\t}\n\t\treturn d;\n\t}\n\n\t/**\n\t * Given an event, creates a new emitter that event that will debounce events based on {@link delay} and give an\n\t * array event object of all events that fired.\n\t *\n\t * *NOTE* that this function returns an `Event` and it MUST be called with a `DisposableStore` whenever the returned\n\t * event is accessible to \"third parties\", e.g the event is a public property. Otherwise a leaked listener on the\n\t * returned event causes this utility to leak a listener on the original event.\n\t *\n\t * @param event The original event to debounce.\n\t * @param merge A function that reduces all events into a single event.\n\t * @param delay The number of milliseconds to debounce.\n\t * @param leading Whether to fire a leading event without debouncing.\n\t * @param flushOnListenerRemove Whether to fire all debounced events when a listener is removed. If this is not\n\t * specified, some events could go missing. Use this if it's important that all events are processed, even if the\n\t * listener gets disposed before the debounced event fires.\n\t * @param leakWarningThreshold See {@link EmitterOptions.leakWarningThreshold}.\n\t * @param disposable A disposable store to register the debounce emitter to.\n\t */\n\texport function debounce<T>(event: Event<T>, merge: (last: T | undefined, event: T) => T, delay?: number | typeof MicrotaskDelay, leading?: boolean, flushOnListenerRemove?: boolean, leakWarningThreshold?: number, disposable?: DisposableStore): Event<T>;\n\texport function debounce<I, O>(event: Event<I>, merge: (last: O | undefined, event: I) => O, delay?: number | typeof MicrotaskDelay, leading?: boolean, flushOnListenerRemove?: boolean, leakWarningThreshold?: number, disposable?: DisposableStore): Event<O>;\n\texport function debounce<I, O>(event: Event<I>, merge: (last: O | undefined, event: I) => O, delay: number | typeof MicrotaskDelay = 100, leading = false, flushOnListenerRemove = false, leakWarningThreshold?: number, disposable?: DisposableStore): Event<O> {\n\t\tlet subscription: IDisposable;\n\t\tlet output: O | undefined = undefined;\n\t\tlet handle: any = undefined;\n\t\tlet numDebouncedCalls = 0;\n\t\tlet doFire: (() => void) | undefined;\n\n\t\tconst options: EmitterOptions | undefined = {\n\t\t\tleakWarningThreshold,\n\t\t\tonWillAddFirstListener() {\n\t\t\t\tsubscription = event(cur => {\n\t\t\t\t\tnumDebouncedCalls++;\n\t\t\t\t\toutput = merge(output, cur);\n\n\t\t\t\t\tif (leading && !handle) {\n\t\t\t\t\t\temitter.fire(output);\n\t\t\t\t\t\toutput = undefined;\n\t\t\t\t\t}\n\n\t\t\t\t\tdoFire = () => {\n\t\t\t\t\t\tconst _output = output;\n\t\t\t\t\t\toutput = undefined;\n\t\t\t\t\t\thandle = undefined;\n\t\t\t\t\t\tif (!leading || numDebouncedCalls > 1) {\n\t\t\t\t\t\t\temitter.fire(_output!);\n\t\t\t\t\t\t}\n\t\t\t\t\t\tnumDebouncedCalls = 0;\n\t\t\t\t\t};\n\n\t\t\t\t\tif (typeof delay === 'number') {\n\t\t\t\t\t\tclearTimeout(handle);\n\t\t\t\t\t\thandle = setTimeout(doFire, delay);\n\t\t\t\t\t} else {\n\t\t\t\t\t\tif (handle === undefined) {\n\t\t\t\t\t\t\thandle = 0;\n\t\t\t\t\t\t\tqueueMicrotask(doFire);\n\t\t\t\t\t\t}\n\t\t\t\t\t}\n\t\t\t\t});\n\t\t\t},\n\t\t\tonWillRemoveListener() {\n\t\t\t\tif (flushOnListenerRemove && numDebouncedCalls > 0) {\n\t\t\t\t\tdoFire?.();\n\t\t\t\t}\n\t\t\t},\n\t\t\tonDidRemoveLastListener() {\n\t\t\t\tdoFire = undefined;\n\t\t\t\tsubscription.dispose();\n\t\t\t}\n\t\t};\n\n\t\tif (!disposable) {\n\t\t\t_addLeakageTraceLogic(options);\n\t\t}\n\n\t\tconst emitter = new Emitter<O>(options);\n\n\t\tdisposable?.add(emitter);\n\n\t\treturn emitter.event;\n\t}\n\n\t/**\n\t * Debounces an event, firing after some delay (default=0) with an array of all event original objects.\n\t *\n\t * *NOTE* that this function returns an `Event` and it MUST be called with a `DisposableStore` whenever the returned\n\t * event is accessible to \"third parties\", e.g the event is a public property. Otherwise a leaked listener on the\n\t * returned event causes this utility to leak a listener on the original event.\n\t */\n\texport function accumulate<T>(event: Event<T>, delay: number = 0, disposable?: DisposableStore): Event<T[]> {\n\t\treturn Event.debounce<T, T[]>(event, (last, e) => {\n\t\t\tif (!last) {\n\t\t\t\treturn [e];\n\t\t\t}\n\t\t\tlast.push(e);\n\t\t\treturn last;\n\t\t}, delay, undefined, true, undefined, disposable);\n\t}\n\n\t/**\n\t * Filters an event such that some condition is _not_ met more than once in a row, effectively ensuring duplicate\n\t * event objects from different sources do not fire the same event object.\n\t *\n\t * *NOTE* that this function returns an `Event` and it MUST be called with a `DisposableStore` whenever the returned\n\t * event is accessible to \"third parties\", e.g the event is a public property. Otherwise a leaked listener on the\n\t * returned event causes this utility to leak a listener on the original event.\n\t *\n\t * @param event The event source for the new event.\n\t * @param equals The equality condition.\n\t * @param disposable A disposable store to add the new EventEmitter to.\n\t *\n\t * @example\n\t * ```\n\t * // Fire only one time when a single window is opened or focused\n\t * Event.latch(Event.any(onDidOpenWindow, onDidFocusWindow))\n\t * ```\n\t */\n\texport function latch<T>(event: Event<T>, equals: (a: T, b: T) => boolean = (a, b) => a === b, disposable?: DisposableStore): Event<T> {\n\t\tlet firstCall = true;\n\t\tlet cache: T;\n\n\t\treturn filter(event, value => {\n\t\t\tconst shouldEmit = firstCall || !equals(value, cache);\n\t\t\tfirstCall = false;\n\t\t\tcache = value;\n\t\t\treturn shouldEmit;\n\t\t}, disposable);\n\t}\n\n\t/**\n\t * Splits an event whose parameter is a union type into 2 separate events for each type in the union.\n\t *\n\t * *NOTE* that this function returns an `Event` and it MUST be called with a `DisposableStore` whenever the returned\n\t * event is accessible to \"third parties\", e.g the event is a public property. Otherwise a leaked listener on the\n\t * returned event causes this utility to leak a listener on the original event.\n\t *\n\t * @example\n\t * ```\n\t * const event = new EventEmitter<number | undefined>().event;\n\t * const [numberEvent, undefinedEvent] = Event.split(event, isUndefined);\n\t * ```\n\t *\n\t * @param event The event source for the new event.\n\t * @param isT A function that determines what event is of the first type.\n\t * @param disposable A disposable store to add the new EventEmitter to.\n\t */\n\texport function split<T, U>(event: Event<T | U>, isT: (e: T | U) => e is T, disposable?: DisposableStore): [Event<T>, Event<U>] {\n\t\treturn [\n\t\t\tEvent.filter(event, isT, disposable),\n\t\t\tEvent.filter(event, e => !isT(e), disposable) as Event<U>,\n\t\t];\n\t}\n\n\t/**\n\t * Buffers an event until it has a listener attached.\n\t *\n\t * *NOTE* that this function returns an `Event` and it MUST be called with a `DisposableStore` whenever the returned\n\t * event is accessible to \"third parties\", e.g the event is a public property. Otherwise a leaked listener on the\n\t * returned event causes this utility to leak a listener on the original event.\n\t *\n\t * @param event The event source for the new event.\n\t * @param flushAfterTimeout Determines whether to flush the buffer after a timeout immediately or after a\n\t * `setTimeout` when the first event listener is added.\n\t * @param _buffer Internal: A source event array used for tests.\n\t *\n\t * @example\n\t * ```\n\t * // Start accumulating events, when the first listener is attached, flush\n\t * // the event after a timeout such that multiple listeners attached before\n\t * // the timeout would receive the event\n\t * this.onInstallExtension = Event.buffer(service.onInstallExtension, true);\n\t * ```\n\t */\n\texport function buffer<T>(event: Event<T>, flushAfterTimeout = false, _buffer: T[] = [], disposable?: DisposableStore): Event<T> {\n\t\tlet buffer: T[] | null = _buffer.slice();\n\n\t\tlet listener: IDisposable | null = event(e => {\n\t\t\tif (buffer) {\n\t\t\t\tbuffer.push(e);\n\t\t\t} else {\n\t\t\t\temitter.fire(e);\n\t\t\t}\n\t\t});\n\n\t\tif (disposable) {\n\t\t\tdisposable.add(listener);\n\t\t}\n\n\t\tconst flush = () => {\n\t\t\tbuffer?.forEach(e => emitter.fire(e));\n\t\t\tbuffer = null;\n\t\t};\n\n\t\tconst emitter = new Emitter<T>({\n\t\t\tonWillAddFirstListener() {\n\t\t\t\tif (!listener) {\n\t\t\t\t\tlistener = event(e => emitter.fire(e));\n\t\t\t\t\tif (disposable) {\n\t\t\t\t\t\tdisposable.add(listener);\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t},\n\n\t\t\tonDidAddFirstListener() {\n\t\t\t\tif (buffer) {\n\t\t\t\t\tif (flushAfterTimeout) {\n\t\t\t\t\t\tsetTimeout(flush);\n\t\t\t\t\t} else {\n\t\t\t\t\t\tflush();\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t},\n\n\t\t\tonDidRemoveLastListener() {\n\t\t\t\tif (listener) {\n\t\t\t\t\tlistener.dispose();\n\t\t\t\t}\n\t\t\t\tlistener = null;\n\t\t\t}\n\t\t});\n\n\t\tif (disposable) {\n\t\t\tdisposable.add(emitter);\n\t\t}\n\n\t\treturn emitter.event;\n\t}\n\t/**\n\t * Wraps the event in an {@link IChainableEvent}, allowing a more functional programming style.\n\t *\n\t * @example\n\t * ```\n\t * // Normal\n\t * const onEnterPressNormal = Event.filter(\n\t *   Event.map(onKeyPress.event, e => new StandardKeyboardEvent(e)),\n\t *   e.keyCode === KeyCode.Enter\n\t * ).event;\n\t *\n\t * // Using chain\n\t * const onEnterPressChain = Event.chain(onKeyPress.event, $ => $\n\t *   .map(e => new StandardKeyboardEvent(e))\n\t *   .filter(e => e.keyCode === KeyCode.Enter)\n\t * );\n\t * ```\n\t */\n\texport function chain<T, R>(event: Event<T>, sythensize: ($: IChainableSythensis<T>) => IChainableSythensis<R>): Event<R> {\n\t\tconst fn: Event<R> = (listener, thisArgs, disposables) => {\n\t\t\tconst cs = sythensize(new ChainableSynthesis()) as ChainableSynthesis;\n\t\t\treturn event(function (value) {\n\t\t\t\tconst result = cs.evaluate(value);\n\t\t\t\tif (result !== HaltChainable) {\n\t\t\t\t\tlistener.call(thisArgs, result);\n\t\t\t\t}\n\t\t\t}, undefined, disposables);\n\t\t};\n\n\t\treturn fn;\n\t}\n\n\tconst HaltChainable = Symbol('HaltChainable');\n\n\tclass ChainableSynthesis implements IChainableSythensis<any> {\n\t\tprivate readonly steps: ((input: any) => any)[] = [];\n\n\t\tmap<O>(fn: (i: any) => O): this {\n\t\t\tthis.steps.push(fn);\n\t\t\treturn this;\n\t\t}\n\n\t\tforEach(fn: (i: any) => void): this {\n\t\t\tthis.steps.push(v => {\n\t\t\t\tfn(v);\n\t\t\t\treturn v;\n\t\t\t});\n\t\t\treturn this;\n\t\t}\n\n\t\tfilter(fn: (e: any) => boolean): this {\n\t\t\tthis.steps.push(v => fn(v) ? v : HaltChainable);\n\t\t\treturn this;\n\t\t}\n\n\t\treduce<R>(merge: (last: R | undefined, event: any) => R, initial?: R | undefined): this {\n\t\t\tlet last = initial;\n\t\t\tthis.steps.push(v => {\n\t\t\t\tlast = merge(last, v);\n\t\t\t\treturn last;\n\t\t\t});\n\t\t\treturn this;\n\t\t}\n\n\t\tlatch(equals: (a: any, b: any) => boolean = (a, b) => a === b): ChainableSynthesis {\n\t\t\tlet firstCall = true;\n\t\t\tlet cache: any;\n\t\t\tthis.steps.push(value => {\n\t\t\t\tconst shouldEmit = firstCall || !equals(value, cache);\n\t\t\t\tfirstCall = false;\n\t\t\t\tcache = value;\n\t\t\t\treturn shouldEmit ? value : HaltChainable;\n\t\t\t});\n\n\t\t\treturn this;\n\t\t}\n\n\t\tpublic evaluate(value: any) {\n\t\t\tfor (const step of this.steps) {\n\t\t\t\tvalue = step(value);\n\t\t\t\tif (value === HaltChainable) {\n\t\t\t\t\tbreak;\n\t\t\t\t}\n\t\t\t}\n\n\t\t\treturn value;\n\t\t}\n\t}\n\n\texport interface IChainableSythensis<T> {\n\t\tmap<O>(fn: (i: T) => O): IChainableSythensis<O>;\n\t\tforEach(fn: (i: T) => void): IChainableSythensis<T>;\n\t\tfilter<R extends T>(fn: (e: T) => e is R): IChainableSythensis<R>;\n\t\tfilter(fn: (e: T) => boolean): IChainableSythensis<T>;\n\t\treduce<R>(merge: (last: R, event: T) => R, initial: R): IChainableSythensis<R>;\n\t\treduce<R>(merge: (last: R | undefined, event: T) => R): IChainableSythensis<R>;\n\t\tlatch(equals?: (a: T, b: T) => boolean): IChainableSythensis<T>;\n\t}\n\n\texport interface NodeEventEmitter {\n\t\ton(event: string | symbol, listener: Function): unknown;\n\t\tremoveListener(event: string | symbol, listener: Function): unknown;\n\t}\n\n\t/**\n\t * Creates an {@link Event} from a node event emitter.\n\t */\n\texport function fromNodeEventEmitter<T>(emitter: NodeEventEmitter, eventName: string, map: (...args: any[]) => T = id => id): Event<T> {\n\t\tconst fn = (...args: any[]) => result.fire(map(...args));\n\t\tconst onFirstListenerAdd = () => emitter.on(eventName, fn);\n\t\tconst onLastListenerRemove = () => emitter.removeListener(eventName, fn);\n\t\tconst result = new Emitter<T>({ onWillAddFirstListener: onFirstListenerAdd, onDidRemoveLastListener: onLastListenerRemove });\n\n\t\treturn result.event;\n\t}\n\n\texport interface DOMEventEmitter {\n\t\taddEventListener(event: string | symbol, listener: Function): void;\n\t\tremoveEventListener(event: string | symbol, listener: Function): void;\n\t}\n\n\t/**\n\t * Creates an {@link Event} from a DOM event emitter.\n\t */\n\texport function fromDOMEventEmitter<T>(emitter: DOMEventEmitter, eventName: string, map: (...args: any[]) => T = id => id): Event<T> {\n\t\tconst fn = (...args: any[]) => result.fire(map(...args));\n\t\tconst onFirstListenerAdd = () => emitter.addEventListener(eventName, fn);\n\t\tconst onLastListenerRemove = () => emitter.removeEventListener(eventName, fn);\n\t\tconst result = new Emitter<T>({ onWillAddFirstListener: onFirstListenerAdd, onDidRemoveLastListener: onLastListenerRemove });\n\n\t\treturn result.event;\n\t}\n\n\t/**\n\t * Creates a promise out of an event, using the {@link Event.once} helper.\n\t */\n\texport function toPromise<T>(event: Event<T>): Promise<T> {\n\t\treturn new Promise(resolve => once(event)(resolve));\n\t}\n\n\t/**\n\t * Creates an event out of a promise that fires once when the promise is\n\t * resolved with the result of the promise or `undefined`.\n\t */\n\texport function fromPromise<T>(promise: Promise<T>): Event<T | undefined> {\n\t\tconst result = new Emitter<T | undefined>();\n\n\t\tpromise.then(res => {\n\t\t\tresult.fire(res);\n\t\t}, () => {\n\t\t\tresult.fire(undefined);\n\t\t}).finally(() => {\n\t\t\tresult.dispose();\n\t\t});\n\n\t\treturn result.event;\n\t}\n\n\t/**\n\t * A convenience function for forwarding an event to another emitter which\n\t * improves readability.allows Event.forward(event, emitter) instead of `event(e => emitter.fire(e))`.\n\t * @param from The event to forward.\n\t * @param to The emitter to forward the event to.\n\t * @example\n\t * Event.forward(event, emitter);\n\t * // equivalent to\n\t * event(e => emitter.fire(e));\n\t * // equivalent to\n\t * event(emitter.fire, emitter);\n\t */\n\texport function forward<T>(from: Event<T>, to: Emitter<T>): IDisposable {\n\t\treturn from(e => to.fire(e));\n\t}\n\n\t/**\n\t * Adds a listener to an event and calls the listener immediately with undefined as the event object.\n\t *\n\t * @example\n\t * ```\n\t * // Initialize the UI and update it when dataChangeEvent fires\n\t * runAndSubscribe(dataChangeEvent, () => this._updateUI());\n\t * ```\n\t */\n\texport function runAndSubscribe<T>(event: Event<T>, handler: (e: T) => any, initial: T): IDisposable;\n\texport function runAndSubscribe<T>(event: Event<T>, handler: (e: T | undefined) => any): IDisposable;\n\texport function runAndSubscribe<T>(event: Event<T>, handler: (e: T | undefined) => any, initial?: T): IDisposable {\n\t\thandler(initial);\n\t\treturn event(e => handler(e));\n\t}\n\n\tclass EmitterObserver<T> implements IObserver {\n\n\t\treadonly emitter: Emitter<T>;\n\n\t\tprivate _counter = 0;\n\t\tprivate _hasChanged = false;\n\n\t\tconstructor(readonly _observable: IObservable<T, any>, store: DisposableStore | undefined) {\n\t\t\tconst options: EmitterOptions = {\n\t\t\t\tonWillAddFirstListener: () => {\n\t\t\t\t\t_observable.addObserver(this);\n\t\t\t\t},\n\t\t\t\tonDidRemoveLastListener: () => {\n\t\t\t\t\t_observable.removeObserver(this);\n\t\t\t\t}\n\t\t\t};\n\t\t\tif (!store) {\n\t\t\t\t_addLeakageTraceLogic(options);\n\t\t\t}\n\t\t\tthis.emitter = new Emitter<T>(options);\n\t\t\tif (store) {\n\t\t\t\tstore.add(this.emitter);\n\t\t\t}\n\t\t}\n\n\t\tbeginUpdate<T>(_observable: IObservable<T, void>): void {\n\t\t\t// assert(_observable === this.obs);\n\t\t\tthis._counter++;\n\t\t}\n\n\t\thandlePossibleChange<T>(_observable: IObservable<T, unknown>): void {\n\t\t\t// assert(_observable === this.obs);\n\t\t}\n\n\t\thandleChange<T, TChange>(_observable: IObservable<T, TChange>, _change: TChange): void {\n\t\t\t// assert(_observable === this.obs);\n\t\t\tthis._hasChanged = true;\n\t\t}\n\n\t\tendUpdate<T>(_observable: IObservable<T, void>): void {\n\t\t\t// assert(_observable === this.obs);\n\t\t\tthis._counter--;\n\t\t\tif (this._counter === 0) {\n\t\t\t\tthis._observable.reportChanges();\n\t\t\t\tif (this._hasChanged) {\n\t\t\t\t\tthis._hasChanged = false;\n\t\t\t\t\tthis.emitter.fire(this._observable.get());\n\t\t\t\t}\n\t\t\t}\n\t\t}\n\t}\n\n\t/**\n\t * Creates an event emitter that is fired when the observable changes.\n\t * Each listeners subscribes to the emitter.\n\t */\n\texport function fromObservable<T>(obs: IObservable<T, any>, store?: DisposableStore): Event<T> {\n\t\tconst observer = new EmitterObserver(obs, store);\n\t\treturn observer.emitter.event;\n\t}\n\n\t/**\n\t * Each listener is attached to the observable directly.\n\t */\n\texport function fromObservableLight(observable: IObservable<any>): Event<void> {\n\t\treturn (listener, thisArgs, disposables) => {\n\t\t\tlet count = 0;\n\t\t\tlet didChange = false;\n\t\t\tconst observer: IObserver = {\n\t\t\t\tbeginUpdate() {\n\t\t\t\t\tcount++;\n\t\t\t\t},\n\t\t\t\tendUpdate() {\n\t\t\t\t\tcount--;\n\t\t\t\t\tif (count === 0) {\n\t\t\t\t\t\tobservable.reportChanges();\n\t\t\t\t\t\tif (didChange) {\n\t\t\t\t\t\t\tdidChange = false;\n\t\t\t\t\t\t\tlistener.call(thisArgs);\n\t\t\t\t\t\t}\n\t\t\t\t\t}\n\t\t\t\t},\n\t\t\t\thandlePossibleChange() {\n\t\t\t\t\t// noop\n\t\t\t\t},\n\t\t\t\thandleChange() {\n\t\t\t\t\tdidChange = true;\n\t\t\t\t}\n\t\t\t};\n\t\t\tobservable.addObserver(observer);\n\t\t\tobservable.reportChanges();\n\t\t\tconst disposable = {\n\t\t\t\tdispose() {\n\t\t\t\t\tobservable.removeObserver(observer);\n\t\t\t\t}\n\t\t\t};\n\n\t\t\tif (disposables instanceof DisposableStore) {\n\t\t\t\tdisposables.add(disposable);\n\t\t\t} else if (Array.isArray(disposables)) {\n\t\t\t\tdisposables.push(disposable);\n\t\t\t}\n\n\t\t\treturn disposable;\n\t\t};\n\t}\n}\n\nexport interface EmitterOptions {\n\t/**\n\t * Optional function that's called *before* the very first listener is added\n\t */\n\tonWillAddFirstListener?: Function;\n\t/**\n\t * Optional function that's called *after* the very first listener is added\n\t */\n\tonDidAddFirstListener?: Function;\n\t/**\n\t * Optional function that's called after a listener is added\n\t */\n\tonDidAddListener?: Function;\n\t/**\n\t * Optional function that's called *after* remove the very last listener\n\t */\n\tonDidRemoveLastListener?: Function;\n\t/**\n\t * Optional function that's called *before* a listener is removed\n\t */\n\tonWillRemoveListener?: Function;\n\t/**\n\t * Optional function that's called when a listener throws an error. Defaults to\n\t * {@link onUnexpectedError}\n\t */\n\tonListenerError?: (e: any) => void;\n\t/**\n\t * Number of listeners that are allowed before assuming a leak. Default to\n\t * a globally configured value\n\t *\n\t * @see setGlobalLeakWarningThreshold\n\t */\n\tleakWarningThreshold?: number;\n\t/**\n\t * Pass in a delivery queue, which is useful for ensuring\n\t * in order event delivery across multiple emitters.\n\t */\n\tdeliveryQueue?: EventDeliveryQueue;\n\n\t/** ONLY enable this during development */\n\t_profName?: string;\n}\n\n\nexport class EventProfiling {\n\n\tstatic readonly all = new Set<EventProfiling>();\n\n\tprivate static _idPool = 0;\n\n\treadonly name: string;\n\tpublic listenerCount: number = 0;\n\tpublic invocationCount = 0;\n\tpublic elapsedOverall = 0;\n\tpublic durations: number[] = [];\n\n\tprivate _stopWatch?: StopWatch;\n\n\tconstructor(name: string) {\n\t\tthis.name = `${name}_${EventProfiling._idPool++}`;\n\t\tEventProfiling.all.add(this);\n\t}\n\n\tstart(listenerCount: number): void {\n\t\tthis._stopWatch = new StopWatch();\n\t\tthis.listenerCount = listenerCount;\n\t}\n\n\tstop(): void {\n\t\tif (this._stopWatch) {\n\t\t\tconst elapsed = this._stopWatch.elapsed();\n\t\t\tthis.durations.push(elapsed);\n\t\t\tthis.elapsedOverall += elapsed;\n\t\t\tthis.invocationCount += 1;\n\t\t\tthis._stopWatch = undefined;\n\t\t}\n\t}\n}\n\nlet _globalLeakWarningThreshold = -1;\nexport function setGlobalLeakWarningThreshold(n: number): IDisposable {\n\tconst oldValue = _globalLeakWarningThreshold;\n\t_globalLeakWarningThreshold = n;\n\treturn {\n\t\tdispose() {\n\t\t\t_globalLeakWarningThreshold = oldValue;\n\t\t}\n\t};\n}\n\nclass LeakageMonitor {\n\n\tprivate static _idPool = 1;\n\n\tprivate _stacks: Map<string, number> | undefined;\n\tprivate _warnCountdown: number = 0;\n\n\tconstructor(\n\t\tprivate readonly _errorHandler: (err: Error) => void,\n\t\treadonly threshold: number,\n\t\treadonly name: string = (LeakageMonitor._idPool++).toString(16).padStart(3, '0')\n\t) { }\n\n\tdispose(): void {\n\t\tthis._stacks?.clear();\n\t}\n\n\tcheck(stack: Stacktrace, listenerCount: number): undefined | (() => void) {\n\n\t\tconst threshold = this.threshold;\n\t\tif (threshold <= 0 || listenerCount < threshold) {\n\t\t\treturn undefined;\n\t\t}\n\n\t\tif (!this._stacks) {\n\t\t\tthis._stacks = new Map();\n\t\t}\n\t\tconst count = (this._stacks.get(stack.value) || 0);\n\t\tthis._stacks.set(stack.value, count + 1);\n\t\tthis._warnCountdown -= 1;\n\n\t\tif (this._warnCountdown <= 0) {\n\t\t\t// only warn on first exceed and then every time the limit\n\t\t\t// is exceeded by 50% again\n\t\t\tthis._warnCountdown = threshold * 0.5;\n\n\t\t\tconst [topStack, topCount] = this.getMostFrequentStack()!;\n\t\t\tconst message = `[${this.name}] potential listener LEAK detected, having ${listenerCount} listeners already. MOST frequent listener (${topCount}):`;\n\t\t\tconsole.warn(message);\n\t\t\tconsole.warn(topStack!);\n\n\t\t\tconst error = new ListenerLeakError(message, topStack);\n\t\t\tthis._errorHandler(error);\n\t\t}\n\n\t\treturn () => {\n\t\t\tconst count = (this._stacks!.get(stack.value) || 0);\n\t\t\tthis._stacks!.set(stack.value, count - 1);\n\t\t};\n\t}\n\n\tgetMostFrequentStack(): [string, number] | undefined {\n\t\tif (!this._stacks) {\n\t\t\treturn undefined;\n\t\t}\n\t\tlet topStack: [string, number] | undefined;\n\t\tlet topCount: number = 0;\n\t\tfor (const [stack, count] of this._stacks) {\n\t\t\tif (!topStack || topCount < count) {\n\t\t\t\ttopStack = [stack, count];\n\t\t\t\ttopCount = count;\n\t\t\t}\n\t\t}\n\t\treturn topStack;\n\t}\n}\n\nclass Stacktrace {\n\n\tstatic create() {\n\t\tconst err = new Error();\n\t\treturn new Stacktrace(err.stack ?? '');\n\t}\n\n\tprivate constructor(readonly value: string) { }\n\n\tprint() {\n\t\tconsole.warn(this.value.split('\\n').slice(2).join('\\n'));\n\t}\n}\n\n// error that is logged when going over the configured listener threshold\nexport class ListenerLeakError extends Error {\n\tconstructor(message: string, stack: string) {\n\t\tsuper(message);\n\t\tthis.name = 'ListenerLeakError';\n\t\tthis.stack = stack;\n\t}\n}\n\n// SEVERE error that is logged when having gone way over the configured listener\n// threshold so that the emitter refuses to accept more listeners\nexport class ListenerRefusalError extends Error {\n\tconstructor(message: string, stack: string) {\n\t\tsuper(message);\n\t\tthis.name = 'ListenerRefusalError';\n\t\tthis.stack = stack;\n\t}\n}\n\nlet id = 0;\nclass UniqueContainer<T> {\n\tstack?: Stacktrace;\n\tpublic id = id++;\n\tconstructor(public readonly value: T) { }\n}\nconst compactionThreshold = 2;\n\ntype ListenerContainer<T> = UniqueContainer<(data: T) => void>;\ntype ListenerOrListeners<T> = (ListenerContainer<T> | undefined)[] | ListenerContainer<T>;\n\nconst forEachListener = <T>(listeners: ListenerOrListeners<T>, fn: (c: ListenerContainer<T>) => void) => {\n\tif (listeners instanceof UniqueContainer) {\n\t\tfn(listeners);\n\t} else {\n\t\tfor (let i = 0; i < listeners.length; i++) {\n\t\t\tconst l = listeners[i];\n\t\t\tif (l) {\n\t\t\t\tfn(l);\n\t\t\t}\n\t\t}\n\t}\n};\n\n\nlet _listenerFinalizers: FinalizationRegistry<string> | undefined;\n\nif (_enableListenerGCedWarning) {\n\tconst leaks: string[] = [];\n\n\tsetInterval(() => {\n\t\tif (leaks.length === 0) {\n\t\t\treturn;\n\t\t}\n\t\tconsole.warn('[LEAKING LISTENERS] GC\\'ed these listeners that were NOT yet disposed:');\n\t\tconsole.warn(leaks.join('\\n'));\n\t\tleaks.length = 0;\n\t}, 3000);\n\n\t_listenerFinalizers = new FinalizationRegistry(heldValue => {\n\t\tif (typeof heldValue === 'string') {\n\t\t\tleaks.push(heldValue);\n\t\t}\n\t});\n}\n\n/**\n * The Emitter can be used to expose an Event to the public\n * to fire it from the insides.\n * Sample:\n\tclass Document {\n\n\t\tprivate readonly _onDidChange = new Emitter<(value:string)=>any>();\n\n\t\tpublic onDidChange = this._onDidChange.event;\n\n\t\t// getter-style\n\t\t// get onDidChange(): Event<(value:string)=>any> {\n\t\t// \treturn this._onDidChange.event;\n\t\t// }\n\n\t\tprivate _doIt() {\n\t\t\t//...\n\t\t\tthis._onDidChange.fire(value);\n\t\t}\n\t}\n */\nexport class Emitter<T> {\n\n\tprivate readonly _options?: EmitterOptions;\n\tprivate readonly _leakageMon?: LeakageMonitor;\n\tprivate readonly _perfMon?: EventProfiling;\n\tprivate _disposed?: true;\n\tprivate _event?: Event<T>;\n\n\t/**\n\t * A listener, or list of listeners. A single listener is the most common\n\t * for event emitters (#185789), so we optimize that special case to avoid\n\t * wrapping it in an array (just like Node.js itself.)\n\t *\n\t * A list of listeners never 'downgrades' back to a plain function if\n\t * listeners are removed, for two reasons:\n\t *\n\t *  1. That's complicated (especially with the deliveryQueue)\n\t *  2. A listener with >1 listener is likely to have >1 listener again at\n\t *     some point, and swapping between arrays and functions may[citation needed]\n\t *     introduce unnecessary work and garbage.\n\t *\n\t * The array listeners can be 'sparse', to avoid reallocating the array\n\t * whenever any listener is added or removed. If more than `1 / compactionThreshold`\n\t * of the array is empty, only then is it resized.\n\t */\n\tprotected _listeners?: ListenerOrListeners<T>;\n\n\t/**\n\t * Always to be defined if _listeners is an array. It's no longer a true\n\t * queue, but holds the dispatching 'state'. If `fire()` is called on an\n\t * emitter, any work left in the _deliveryQueue is finished first.\n\t */\n\tprivate _deliveryQueue?: EventDeliveryQueuePrivate;\n\tprotected _size = 0;\n\n\tconstructor(options?: EmitterOptions) {\n\t\tthis._options = options;\n\t\tthis._leakageMon = (_globalLeakWarningThreshold > 0 || this._options?.leakWarningThreshold)\n\t\t\t? new LeakageMonitor(options?.onListenerError ?? onUnexpectedError, this._options?.leakWarningThreshold ?? _globalLeakWarningThreshold) :\n\t\t\tundefined;\n\t\tthis._perfMon = this._options?._profName ? new EventProfiling(this._options._profName) : undefined;\n\t\tthis._deliveryQueue = this._options?.deliveryQueue as EventDeliveryQueuePrivate | undefined;\n\t}\n\n\tdispose() {\n\t\tif (!this._disposed) {\n\t\t\tthis._disposed = true;\n\n\t\t\t// It is bad to have listeners at the time of disposing an emitter, it is worst to have listeners keep the emitter\n\t\t\t// alive via the reference that's embedded in their disposables. Therefore we loop over all remaining listeners and\n\t\t\t// unset their subscriptions/disposables. Looping and blaming remaining listeners is done on next tick because the\n\t\t\t// the following programming pattern is very popular:\n\t\t\t//\n\t\t\t// const someModel = this._disposables.add(new ModelObject()); // (1) create and register model\n\t\t\t// this._disposables.add(someModel.onDidChange(() => { ... }); // (2) subscribe and register model-event listener\n\t\t\t// ...later...\n\t\t\t// this._disposables.dispose(); disposes (1) then (2): don't warn after (1) but after the \"overall dispose\" is done\n\n\t\t\tif (this._deliveryQueue?.current === this) {\n\t\t\t\tthis._deliveryQueue.reset();\n\t\t\t}\n\t\t\tif (this._listeners) {\n\t\t\t\tif (_enableDisposeWithListenerWarning) {\n\t\t\t\t\tconst listeners = this._listeners;\n\t\t\t\t\tqueueMicrotask(() => {\n\t\t\t\t\t\tforEachListener(listeners, l => l.stack?.print());\n\t\t\t\t\t});\n\t\t\t\t}\n\n\t\t\t\tthis._listeners = undefined;\n\t\t\t\tthis._size = 0;\n\t\t\t}\n\t\t\tthis._options?.onDidRemoveLastListener?.();\n\t\t\tthis._leakageMon?.dispose();\n\t\t}\n\t}\n\n\t/**\n\t * For the public to allow to subscribe\n\t * to events from this Emitter\n\t */\n\tget event(): Event<T> {\n\t\tthis._event ??= (callback: (e: T) => any, thisArgs?: any, disposables?: IDisposable[] | DisposableStore) => {\n\t\t\tif (this._leakageMon && this._size > this._leakageMon.threshold ** 2) {\n\t\t\t\tconst message = `[${this._leakageMon.name}] REFUSES to accept new listeners because it exceeded its threshold by far (${this._size} vs ${this._leakageMon.threshold})`;\n\t\t\t\tconsole.warn(message);\n\n\t\t\t\tconst tuple = this._leakageMon.getMostFrequentStack() ?? ['UNKNOWN stack', -1];\n\t\t\t\tconst error = new ListenerRefusalError(`${message}. HINT: Stack shows most frequent listener (${tuple[1]}-times)`, tuple[0]);\n\t\t\t\tconst errorHandler = this._options?.onListenerError || onUnexpectedError;\n\t\t\t\terrorHandler(error);\n\n\t\t\t\treturn Disposable.None;\n\t\t\t}\n\n\t\t\tif (this._disposed) {\n\t\t\t\t// todo: should we warn if a listener is added to a disposed emitter? This happens often\n\t\t\t\treturn Disposable.None;\n\t\t\t}\n\n\t\t\tif (thisArgs) {\n\t\t\t\tcallback = callback.bind(thisArgs);\n\t\t\t}\n\n\t\t\tconst contained = new UniqueContainer(callback);\n\n\t\t\tlet removeMonitor: Function | undefined;\n\t\t\tlet stack: Stacktrace | undefined;\n\t\t\tif (this._leakageMon && this._size >= Math.ceil(this._leakageMon.threshold * 0.2)) {\n\t\t\t\t// check and record this emitter for potential leakage\n\t\t\t\tcontained.stack = Stacktrace.create();\n\t\t\t\tremoveMonitor = this._leakageMon.check(contained.stack, this._size + 1);\n\t\t\t}\n\n\t\t\tif (_enableDisposeWithListenerWarning) {\n\t\t\t\tcontained.stack = stack ?? Stacktrace.create();\n\t\t\t}\n\n\t\t\tif (!this._listeners) {\n\t\t\t\tthis._options?.onWillAddFirstListener?.(this);\n\t\t\t\tthis._listeners = contained;\n\t\t\t\tthis._options?.onDidAddFirstListener?.(this);\n\t\t\t} else if (this._listeners instanceof UniqueContainer) {\n\t\t\t\tthis._deliveryQueue ??= new EventDeliveryQueuePrivate();\n\t\t\t\tthis._listeners = [this._listeners, contained];\n\t\t\t} else {\n\t\t\t\tthis._listeners.push(contained);\n\t\t\t}\n\n\t\t\tthis._size++;\n\n\n\t\t\tconst result = toDisposable(() => {\n\t\t\t\t_listenerFinalizers?.unregister(result);\n\t\t\t\tremoveMonitor?.();\n\t\t\t\tthis._removeListener(contained);\n\t\t\t});\n\t\t\tif (disposables instanceof DisposableStore) {\n\t\t\t\tdisposables.add(result);\n\t\t\t} else if (Array.isArray(disposables)) {\n\t\t\t\tdisposables.push(result);\n\t\t\t}\n\n\t\t\tif (_listenerFinalizers) {\n\t\t\t\tconst stack = new Error().stack!.split('\\n').slice(2, 3).join('\\n').trim();\n\t\t\t\tconst match = /(file:|vscode-file:\\/\\/vscode-app)?(\\/[^:]*:\\d+:\\d+)/.exec(stack);\n\t\t\t\t_listenerFinalizers.register(result, match?.[2] ?? stack, result);\n\t\t\t}\n\n\t\t\treturn result;\n\t\t};\n\n\t\treturn this._event;\n\t}\n\n\tprivate _removeListener(listener: ListenerContainer<T>) {\n\t\tthis._options?.onWillRemoveListener?.(this);\n\n\t\tif (!this._listeners) {\n\t\t\treturn; // expected if a listener gets disposed\n\t\t}\n\n\t\tif (this._size === 1) {\n\t\t\tthis._listeners = undefined;\n\t\t\tthis._options?.onDidRemoveLastListener?.(this);\n\t\t\tthis._size = 0;\n\t\t\treturn;\n\t\t}\n\n\t\t// size > 1 which requires that listeners be a list:\n\t\tconst listeners = this._listeners as (ListenerContainer<T> | undefined)[];\n\n\t\tconst index = listeners.indexOf(listener);\n\t\tif (index === -1) {\n\t\t\tconsole.log('disposed?', this._disposed);\n\t\t\tconsole.log('size?', this._size);\n\t\t\tconsole.log('arr?', JSON.stringify(this._listeners));\n\t\t\tthrow new Error('Attempted to dispose unknown listener');\n\t\t}\n\n\t\tthis._size--;\n\t\tlisteners[index] = undefined;\n\n\t\tconst adjustDeliveryQueue = this._deliveryQueue!.current === this;\n\t\tif (this._size * compactionThreshold <= listeners.length) {\n\t\t\tlet n = 0;\n\t\t\tfor (let i = 0; i < listeners.length; i++) {\n\t\t\t\tif (listeners[i]) {\n\t\t\t\t\tlisteners[n++] = listeners[i];\n\t\t\t\t} else if (adjustDeliveryQueue) {\n\t\t\t\t\tthis._deliveryQueue!.end--;\n\t\t\t\t\tif (n < this._deliveryQueue!.i) {\n\t\t\t\t\t\tthis._deliveryQueue!.i--;\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t}\n\t\t\tlisteners.length = n;\n\t\t}\n\t}\n\n\tprivate _deliver(listener: undefined | UniqueContainer<(value: T) => void>, value: T) {\n\t\tif (!listener) {\n\t\t\treturn;\n\t\t}\n\n\t\tconst errorHandler = this._options?.onListenerError || onUnexpectedError;\n\t\tif (!errorHandler) {\n\t\t\tlistener.value(value);\n\t\t\treturn;\n\t\t}\n\n\t\ttry {\n\t\t\tlistener.value(value);\n\t\t} catch (e) {\n\t\t\terrorHandler(e);\n\t\t}\n\t}\n\n\t/** Delivers items in the queue. Assumes the queue is ready to go. */\n\tprivate _deliverQueue(dq: EventDeliveryQueuePrivate) {\n\t\tconst listeners = dq.current!._listeners! as (ListenerContainer<T> | undefined)[];\n\t\twhile (dq.i < dq.end) {\n\t\t\t// important: dq.i is incremented before calling deliver() because it might reenter deliverQueue()\n\t\t\tthis._deliver(listeners[dq.i++], dq.value as T);\n\t\t}\n\t\tdq.reset();\n\t}\n\n\t/**\n\t * To be kept private to fire an event to\n\t * subscribers\n\t */\n\tfire(event: T): void {\n\t\tif (this._deliveryQueue?.current) {\n\t\t\tthis._deliverQueue(this._deliveryQueue);\n\t\t\tthis._perfMon?.stop(); // last fire() will have starting perfmon, stop it before starting the next dispatch\n\t\t}\n\n\t\tthis._perfMon?.start(this._size);\n\n\t\tif (!this._listeners) {\n\t\t\t// no-op\n\t\t} else if (this._listeners instanceof UniqueContainer) {\n\t\t\tthis._deliver(this._listeners, event);\n\t\t} else {\n\t\t\tconst dq = this._deliveryQueue!;\n\t\t\tdq.enqueue(this, event, this._listeners.length);\n\t\t\tthis._deliverQueue(dq);\n\t\t}\n\n\t\tthis._perfMon?.stop();\n\t}\n\n\thasListeners(): boolean {\n\t\treturn this._size > 0;\n\t}\n}\n\nexport interface EventDeliveryQueue {\n\t_isEventDeliveryQueue: true;\n}\n\nexport const createEventDeliveryQueue = (): EventDeliveryQueue => new EventDeliveryQueuePrivate();\n\nclass EventDeliveryQueuePrivate implements EventDeliveryQueue {\n\tdeclare _isEventDeliveryQueue: true;\n\n\t/**\n\t * Index in current's listener list.\n\t */\n\tpublic i = -1;\n\n\t/**\n\t * The last index in the listener's list to deliver.\n\t */\n\tpublic end = 0;\n\n\t/**\n\t * Emitter currently being dispatched on. Emitter._listeners is always an array.\n\t */\n\tpublic current?: Emitter<any>;\n\t/**\n\t * Currently emitting value. Defined whenever `current` is.\n\t */\n\tpublic value?: unknown;\n\n\tpublic enqueue<T>(emitter: Emitter<T>, value: T, end: number) {\n\t\tthis.i = 0;\n\t\tthis.end = end;\n\t\tthis.current = emitter;\n\t\tthis.value = value;\n\t}\n\n\tpublic reset() {\n\t\tthis.i = this.end; // force any current emission loop to stop, mainly for during dispose\n\t\tthis.current = undefined;\n\t\tthis.value = undefined;\n\t}\n}\n\nexport interface IWaitUntil {\n\ttoken: CancellationToken;\n\twaitUntil(thenable: Promise<unknown>): void;\n}\n\nexport type IWaitUntilData<T> = Omit<Omit<T, 'waitUntil'>, 'token'>;\n\nexport class AsyncEmitter<T extends IWaitUntil> extends Emitter<T> {\n\n\tprivate _asyncDeliveryQueue?: LinkedList<[(ev: T) => void, IWaitUntilData<T>]>;\n\n\tasync fireAsync(data: IWaitUntilData<T>, token: CancellationToken, promiseJoin?: (p: Promise<unknown>, listener: Function) => Promise<unknown>): Promise<void> {\n\t\tif (!this._listeners) {\n\t\t\treturn;\n\t\t}\n\n\t\tif (!this._asyncDeliveryQueue) {\n\t\t\tthis._asyncDeliveryQueue = new LinkedList();\n\t\t}\n\n\t\tforEachListener(this._listeners, listener => this._asyncDeliveryQueue!.push([listener.value, data]));\n\n\t\twhile (this._asyncDeliveryQueue.size > 0 && !token.isCancellationRequested) {\n\n\t\t\tconst [listener, data] = this._asyncDeliveryQueue.shift()!;\n\t\t\tconst thenables: Promise<unknown>[] = [];\n\n\t\t\tconst event = <T>{\n\t\t\t\t...data,\n\t\t\t\ttoken,\n\t\t\t\twaitUntil: (p: Promise<unknown>): void => {\n\t\t\t\t\tif (Object.isFrozen(thenables)) {\n\t\t\t\t\t\tthrow new Error('waitUntil can NOT be called asynchronous');\n\t\t\t\t\t}\n\t\t\t\t\tif (promiseJoin) {\n\t\t\t\t\t\tp = promiseJoin(p, listener);\n\t\t\t\t\t}\n\t\t\t\t\tthenables.push(p);\n\t\t\t\t}\n\t\t\t};\n\n\t\t\ttry {\n\t\t\t\tlistener(event);\n\t\t\t} catch (e) {\n\t\t\t\tonUnexpectedError(e);\n\t\t\t\tcontinue;\n\t\t\t}\n\n\t\t\t// freeze thenables-collection to enforce sync-calls to\n\t\t\t// wait until and then wait for all thenables to resolve\n\t\t\tObject.freeze(thenables);\n\n\t\t\tawait Promise.allSettled(thenables).then(values => {\n\t\t\t\tfor (const value of values) {\n\t\t\t\t\tif (value.status === 'rejected') {\n\t\t\t\t\t\tonUnexpectedError(value.reason);\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t});\n\t\t}\n\t}\n}\n\n\nexport class PauseableEmitter<T> extends Emitter<T> {\n\n\tprivate _isPaused = 0;\n\tprotected _eventQueue = new LinkedList<T>();\n\tprivate _mergeFn?: (input: T[]) => T;\n\n\tpublic get isPaused(): boolean {\n\t\treturn this._isPaused !== 0;\n\t}\n\n\tconstructor(options?: EmitterOptions & { merge?: (input: T[]) => T }) {\n\t\tsuper(options);\n\t\tthis._mergeFn = options?.merge;\n\t}\n\n\tpause(): void {\n\t\tthis._isPaused++;\n\t}\n\n\tresume(): void {\n\t\tif (this._isPaused !== 0 && --this._isPaused === 0) {\n\t\t\tif (this._mergeFn) {\n\t\t\t\t// use the merge function to create a single composite\n\t\t\t\t// event. make a copy in case firing pauses this emitter\n\t\t\t\tif (this._eventQueue.size > 0) {\n\t\t\t\t\tconst events = Array.from(this._eventQueue);\n\t\t\t\t\tthis._eventQueue.clear();\n\t\t\t\t\tsuper.fire(this._mergeFn(events));\n\t\t\t\t}\n\n\t\t\t} else {\n\t\t\t\t// no merging, fire each event individually and test\n\t\t\t\t// that this emitter isn't paused halfway through\n\t\t\t\twhile (!this._isPaused && this._eventQueue.size !== 0) {\n\t\t\t\t\tsuper.fire(this._eventQueue.shift()!);\n\t\t\t\t}\n\t\t\t}\n\t\t}\n\t}\n\n\toverride fire(event: T): void {\n\t\tif (this._size) {\n\t\t\tif (this._isPaused !== 0) {\n\t\t\t\tthis._eventQueue.push(event);\n\t\t\t} else {\n\t\t\t\tsuper.fire(event);\n\t\t\t}\n\t\t}\n\t}\n}\n\nexport class DebounceEmitter<T> extends PauseableEmitter<T> {\n\n\tprivate readonly _delay: number;\n\tprivate _handle: any | undefined;\n\n\tconstructor(options: EmitterOptions & { merge: (input: T[]) => T; delay?: number }) {\n\t\tsuper(options);\n\t\tthis._delay = options.delay ?? 100;\n\t}\n\n\toverride fire(event: T): void {\n\t\tif (!this._handle) {\n\t\t\tthis.pause();\n\t\t\tthis._handle = setTimeout(() => {\n\t\t\t\tthis._handle = undefined;\n\t\t\t\tthis.resume();\n\t\t\t}, this._delay);\n\t\t}\n\t\tsuper.fire(event);\n\t}\n}\n\n/**\n * An emitter which queue all events and then process them at the\n * end of the event loop.\n */\nexport class MicrotaskEmitter<T> extends Emitter<T> {\n\tprivate _queuedEvents: T[] = [];\n\tprivate _mergeFn?: (input: T[]) => T;\n\n\tconstructor(options?: EmitterOptions & { merge?: (input: T[]) => T }) {\n\t\tsuper(options);\n\t\tthis._mergeFn = options?.merge;\n\t}\n\toverride fire(event: T): void {\n\n\t\tif (!this.hasListeners()) {\n\t\t\treturn;\n\t\t}\n\n\t\tthis._queuedEvents.push(event);\n\t\tif (this._queuedEvents.length === 1) {\n\t\t\tqueueMicrotask(() => {\n\t\t\t\tif (this._mergeFn) {\n\t\t\t\t\tsuper.fire(this._mergeFn(this._queuedEvents));\n\t\t\t\t} else {\n\t\t\t\t\tthis._queuedEvents.forEach(e => super.fire(e));\n\t\t\t\t}\n\t\t\t\tthis._queuedEvents = [];\n\t\t\t});\n\t\t}\n\t}\n}\n\n/**\n * An event emitter that multiplexes many events into a single event.\n *\n * @example Listen to the `onData` event of all `Thing`s, dynamically adding and removing `Thing`s\n * to the multiplexer as needed.\n *\n * ```typescript\n * const anythingDataMultiplexer = new EventMultiplexer<{ data: string }>();\n *\n * const thingListeners = DisposableMap<Thing, IDisposable>();\n *\n * thingService.onDidAddThing(thing => {\n *   thingListeners.set(thing, anythingDataMultiplexer.add(thing.onData);\n * });\n * thingService.onDidRemoveThing(thing => {\n *   thingListeners.deleteAndDispose(thing);\n * });\n *\n * anythingDataMultiplexer.event(e => {\n *   console.log('Something fired data ' + e.data)\n * });\n * ```\n */\nexport class EventMultiplexer<T> implements IDisposable {\n\n\tprivate readonly emitter: Emitter<T>;\n\tprivate hasListeners = false;\n\tprivate events: { event: Event<T>; listener: IDisposable | null }[] = [];\n\n\tconstructor() {\n\t\tthis.emitter = new Emitter<T>({\n\t\t\tonWillAddFirstListener: () => this.onFirstListenerAdd(),\n\t\t\tonDidRemoveLastListener: () => this.onLastListenerRemove()\n\t\t});\n\t}\n\n\tget event(): Event<T> {\n\t\treturn this.emitter.event;\n\t}\n\n\tadd(event: Event<T>): IDisposable {\n\t\tconst e = { event: event, listener: null };\n\t\tthis.events.push(e);\n\n\t\tif (this.hasListeners) {\n\t\t\tthis.hook(e);\n\t\t}\n\n\t\tconst dispose = () => {\n\t\t\tif (this.hasListeners) {\n\t\t\t\tthis.unhook(e);\n\t\t\t}\n\n\t\t\tconst idx = this.events.indexOf(e);\n\t\t\tthis.events.splice(idx, 1);\n\t\t};\n\n\t\treturn toDisposable(createSingleCallFunction(dispose));\n\t}\n\n\tprivate onFirstListenerAdd(): void {\n\t\tthis.hasListeners = true;\n\t\tthis.events.forEach(e => this.hook(e));\n\t}\n\n\tprivate onLastListenerRemove(): void {\n\t\tthis.hasListeners = false;\n\t\tthis.events.forEach(e => this.unhook(e));\n\t}\n\n\tprivate hook(e: { event: Event<T>; listener: IDisposable | null }): void {\n\t\te.listener = e.event(r => this.emitter.fire(r));\n\t}\n\n\tprivate unhook(e: { event: Event<T>; listener: IDisposable | null }): void {\n\t\te.listener?.dispose();\n\t\te.listener = null;\n\t}\n\n\tdispose(): void {\n\t\tthis.emitter.dispose();\n\n\t\tfor (const e of this.events) {\n\t\t\te.listener?.dispose();\n\t\t}\n\t\tthis.events = [];\n\t}\n}\n\nexport interface IDynamicListEventMultiplexer<TEventType> extends IDisposable {\n\treadonly event: Event<TEventType>;\n}\nexport class DynamicListEventMultiplexer<TItem, TEventType> implements IDynamicListEventMultiplexer<TEventType> {\n\tprivate readonly _store = new DisposableStore();\n\n\treadonly event: Event<TEventType>;\n\n\tconstructor(\n\t\titems: TItem[],\n\t\tonAddItem: Event<TItem>,\n\t\tonRemoveItem: Event<TItem>,\n\t\tgetEvent: (item: TItem) => Event<TEventType>\n\t) {\n\t\tconst multiplexer = this._store.add(new EventMultiplexer<TEventType>());\n\t\tconst itemListeners = this._store.add(new DisposableMap<TItem, IDisposable>());\n\n\t\tfunction addItem(instance: TItem) {\n\t\t\titemListeners.set(instance, multiplexer.add(getEvent(instance)));\n\t\t}\n\n\t\t// Existing items\n\t\tfor (const instance of items) {\n\t\t\taddItem(instance);\n\t\t}\n\n\t\t// Added items\n\t\tthis._store.add(onAddItem(instance => {\n\t\t\taddItem(instance);\n\t\t}));\n\n\t\t// Removed items\n\t\tthis._store.add(onRemoveItem(instance => {\n\t\t\titemListeners.deleteAndDispose(instance);\n\t\t}));\n\n\t\tthis.event = multiplexer.event;\n\t}\n\n\tdispose() {\n\t\tthis._store.dispose();\n\t}\n}\n\n/**\n * The EventBufferer is useful in situations in which you want\n * to delay firing your events during some code.\n * You can wrap that code and be sure that the event will not\n * be fired during that wrap.\n *\n * ```\n * const emitter: Emitter;\n * const delayer = new EventDelayer();\n * const delayedEvent = delayer.wrapEvent(emitter.event);\n *\n * delayedEvent(console.log);\n *\n * delayer.bufferEvents(() => {\n *   emitter.fire(); // event will not be fired yet\n * });\n *\n * // event will only be fired at this point\n * ```\n */\nexport class EventBufferer {\n\n\tprivate data: { buffers: Function[] }[] = [];\n\n\twrapEvent<T>(event: Event<T>): Event<T>;\n\twrapEvent<T>(event: Event<T>, reduce: (last: T | undefined, event: T) => T): Event<T>;\n\twrapEvent<T, O>(event: Event<T>, reduce: (last: O | undefined, event: T) => O, initial: O): Event<O>;\n\twrapEvent<T, O>(event: Event<T>, reduce?: (last: T | O | undefined, event: T) => T | O, initial?: O): Event<O | T> {\n\t\treturn (listener, thisArgs?, disposables?) => {\n\t\t\treturn event(i => {\n\t\t\t\tconst data = this.data[this.data.length - 1];\n\n\t\t\t\t// Non-reduce scenario\n\t\t\t\tif (!reduce) {\n\t\t\t\t\t// Buffering case\n\t\t\t\t\tif (data) {\n\t\t\t\t\t\tdata.buffers.push(() => listener.call(thisArgs, i));\n\t\t\t\t\t} else {\n\t\t\t\t\t\t// Not buffering case\n\t\t\t\t\t\tlistener.call(thisArgs, i);\n\t\t\t\t\t}\n\t\t\t\t\treturn;\n\t\t\t\t}\n\n\t\t\t\t// Reduce scenario\n\t\t\t\tconst reduceData = data as typeof data & {\n\t\t\t\t\t/**\n\t\t\t\t\t * The accumulated items that will be reduced.\n\t\t\t\t\t */\n\t\t\t\t\titems?: T[];\n\t\t\t\t\t/**\n\t\t\t\t\t * The reduced result cached to be shared with other listeners.\n\t\t\t\t\t */\n\t\t\t\t\treducedResult?: T | O;\n\t\t\t\t};\n\n\t\t\t\t// Not buffering case\n\t\t\t\tif (!reduceData) {\n\t\t\t\t\t// TODO: Is there a way to cache this reduce call for all listeners?\n\t\t\t\t\tlistener.call(thisArgs, reduce(initial, i));\n\t\t\t\t\treturn;\n\t\t\t\t}\n\n\t\t\t\t// Buffering case\n\t\t\t\treduceData.items ??= [];\n\t\t\t\treduceData.items.push(i);\n\t\t\t\tif (reduceData.buffers.length === 0) {\n\t\t\t\t\t// Include a single buffered function that will reduce all events when we're done buffering events\n\t\t\t\t\tdata.buffers.push(() => {\n\t\t\t\t\t\t// cache the reduced result so that the value can be shared across all listeners\n\t\t\t\t\t\treduceData.reducedResult ??= initial\n\t\t\t\t\t\t\t? reduceData.items!.reduce(reduce as (last: O | undefined, event: T) => O, initial)\n\t\t\t\t\t\t\t: reduceData.items!.reduce(reduce as (last: T | undefined, event: T) => T);\n\t\t\t\t\t\tlistener.call(thisArgs, reduceData.reducedResult);\n\t\t\t\t\t});\n\t\t\t\t}\n\t\t\t}, undefined, disposables);\n\t\t};\n\t}\n\n\tbufferEvents<R = void>(fn: () => R): R {\n\t\tconst data = { buffers: new Array<Function>() };\n\t\tthis.data.push(data);\n\t\tconst r = fn();\n\t\tthis.data.pop();\n\t\tdata.buffers.forEach(flush => flush());\n\t\treturn r;\n\t}\n}\n\n/**\n * A Relay is an event forwarder which functions as a replugabble event pipe.\n * Once created, you can connect an input event to it and it will simply forward\n * events from that input event through its own `event` property. The `input`\n * can be changed at any point in time.\n */\nexport class Relay<T> implements IDisposable {\n\n\tprivate listening = false;\n\tprivate inputEvent: Event<T> = Event.None;\n\tprivate inputEventListener: IDisposable = Disposable.None;\n\n\tprivate readonly emitter = new Emitter<T>({\n\t\tonDidAddFirstListener: () => {\n\t\t\tthis.listening = true;\n\t\t\tthis.inputEventListener = this.inputEvent(this.emitter.fire, this.emitter);\n\t\t},\n\t\tonDidRemoveLastListener: () => {\n\t\t\tthis.listening = false;\n\t\t\tthis.inputEventListener.dispose();\n\t\t}\n\t});\n\n\treadonly event: Event<T> = this.emitter.event;\n\n\tset input(event: Event<T>) {\n\t\tthis.inputEvent = event;\n\n\t\tif (this.listening) {\n\t\t\tthis.inputEventListener.dispose();\n\t\t\tthis.inputEventListener = event(this.emitter.fire, this.emitter);\n\t\t}\n\t}\n\n\tdispose() {\n\t\tthis.inputEventListener.dispose();\n\t\tthis.emitter.dispose();\n\t}\n}\n\nexport interface IValueWithChangeEvent<T> {\n\treadonly onDidChange: Event<void>;\n\tget value(): T;\n}\n\nexport class ValueWithChangeEvent<T> implements IValueWithChangeEvent<T> {\n\tpublic static const<T>(value: T): IValueWithChangeEvent<T> {\n\t\treturn new ConstValueWithChangeEvent(value);\n\t}\n\n\tprivate readonly _onDidChange = new Emitter<void>();\n\treadonly onDidChange: Event<void> = this._onDidChange.event;\n\n\tconstructor(private _value: T) { }\n\n\tget value(): T {\n\t\treturn this._value;\n\t}\n\n\tset value(value: T) {\n\t\tif (value !== this._value) {\n\t\t\tthis._value = value;\n\t\t\tthis._onDidChange.fire(undefined);\n\t\t}\n\t}\n}\n\nclass ConstValueWithChangeEvent<T> implements IValueWithChangeEvent<T> {\n\tpublic readonly onDidChange: Event<void> = Event.None;\n\n\tconstructor(readonly value: T) { }\n}\n", "/**\n * Copyright (c) 2019 The xterm.js authors. All rights reserved.\n * @license MIT\n */\n\nimport { UnicodeV6 } from 'common/input/UnicodeV6';\nimport { IUnicodeService, IUnicodeVersionProvider, UnicodeCharProperties, UnicodeCharWidth } from 'common/services/Services';\nimport { Emitter } from 'vs/base/common/event';\n\nexport class UnicodeService implements IUnicodeService {\n  public serviceBrand: any;\n\n  private _providers: {[key: string]: IUnicodeVersionProvider} = Object.create(null);\n  private _active: string = '';\n  private _activeProvider: IUnicodeVersionProvider;\n\n  private readonly _onChange = new Emitter<string>();\n  public readonly onChange = this._onChange.event;\n\n  public static extractShouldJoin(value: UnicodeCharProperties): boolean {\n    return (value & 1) !== 0;\n  }\n  public static extractWidth(value: UnicodeCharProperties): UnicodeCharWidth {\n    return ((value >> 1) & 0x3) as UnicodeCharWidth;\n  }\n  public static extractCharKind(value: UnicodeCharProperties): number {\n    return value >> 3;\n  }\n  public static createPropertyValue(state: number, width: number, shouldJoin: boolean = false): UnicodeCharProperties {\n    return ((state & 0xffffff) << 3) | ((width & 3) << 1) | (shouldJoin?1:0);\n  }\n\n  constructor() {\n    const defaultProvider = new UnicodeV6();\n    this.register(defaultProvider);\n    this._active = defaultProvider.version;\n    this._activeProvider = defaultProvider;\n  }\n\n  public dispose(): void {\n    this._onChange.dispose();\n  }\n\n  public get versions(): string[] {\n    return Object.keys(this._providers);\n  }\n\n  public get activeVersion(): string {\n    return this._active;\n  }\n\n  public set activeVersion(version: string) {\n    if (!this._providers[version]) {\n      throw new Error(`unknown Unicode version \"${version}\"`);\n    }\n    this._active = version;\n    this._activeProvider = this._providers[version];\n    this._onChange.fire(version);\n  }\n\n  public register(provider: IUnicodeVersionProvider): void {\n    this._providers[provider.version] = provider;\n  }\n\n  /**\n   * Unicode version dependent interface.\n   */\n  public wcwidth(num: number): UnicodeCharWidth {\n    return this._activeProvider.wcwidth(num);\n  }\n\n  public getStringCellWidth(s: string): number {\n    let result = 0;\n    let precedingInfo = 0;\n    const length = s.length;\n    for (let i = 0; i < length; ++i) {\n      let code = s.charCodeAt(i);\n      // surrogate pair first\n      if (0xD800 <= code && code <= 0xDBFF) {\n        if (++i >= length) {\n          // this should not happen with strings retrieved from\n          // Buffer.translateToString as it converts from UTF-32\n          // and therefore always should contain the second part\n          // for any other string we still have to handle it somehow:\n          // simply treat the lonely surrogate first as a single char (UCS-2 behavior)\n          return result + this.wcwidth(code);\n        }\n        const second = s.charCodeAt(i);\n        // convert surrogate pair to high codepoint only for valid second part (UTF-16)\n        // otherwise treat them independently (UCS-2 behavior)\n        if (0xDC00 <= second && second <= 0xDFFF) {\n          code = (code - 0xD800) * 0x400 + second - 0xDC00 + 0x10000;\n        } else {\n          result += this.wcwidth(second);\n        }\n      }\n      const currentInfo = this.charProperties(code, precedingInfo);\n      let chWidth = UnicodeService.extractWidth(currentInfo);\n      if (UnicodeService.extractShouldJoin(currentInfo)) {\n        chWidth -= UnicodeService.extractWidth(precedingInfo);\n      }\n      result += chWidth;\n      precedingInfo = currentInfo;\n    }\n    return result;\n  }\n\n  public charProperties(codepoint: number, preceding: UnicodeCharProperties): UnicodeCharProperties {\n    return this._activeProvider.charProperties(codepoint, preceding);\n  }\n}\n", "/**\n * Copyright (c) 2019 The xterm.js authors. All rights reserved.\n * @license MIT\n */\n\nimport { IUnicodeVersionProvider } from '@xterm/xterm';\nimport { UnicodeCharProperties, UnicodeCharWidth } from 'common/services/Services';\nimport { UnicodeService } from 'common/services/UnicodeService';\n\nconst BMP_COMBINING = [\n  [0x0300, 0x036F], [0x0483, 0x0489], [0x0591, 0x05BD],\n  [0x05BF, 0x05BF], [0x05C1, 0x05C2], [0x05C4, 0x05C5],\n  [0x05C7, 0x05C7], [0x0600, 0x0605], [0x0610, 0x061A],\n  [0x061C, 0x061C], [0x064B, 0x065F], [0x0670, 0x0670],\n  [0x06D6, 0x06DD], [0x06DF, 0x06E4], [0x06E7, 0x06E8],\n  [0x06EA, 0x06ED], [0x070F, 0x070F], [0x0711, 0x0711],\n  [0x0730, 0x074A], [0x07A6, 0x07B0], [0x07EB, 0x07F3],\n  [0x07FD, 0x07FD], [0x0816, 0x0819], [0x081B, 0x0823],\n  [0x0825, 0x0827], [0x0829, 0x082D], [0x0859, 0x085B],\n  [0x08D3, 0x0902], [0x093A, 0x093A], [0x093C, 0x093C],\n  [0x0941, 0x0948], [0x094D, 0x094D], [0x0951, 0x0957],\n  [0x0962, 0x0963], [0x0981, 0x0981], [0x09BC, 0x09BC],\n  [0x09C1, 0x09C4], [0x09CD, 0x09CD], [0x09E2, 0x09E3],\n  [0x09FE, 0x09FE], [0x0A01, 0x0A02], [0x0A3C, 0x0A3C],\n  [0x0A41, 0x0A42], [0x0A47, 0x0A48], [0x0A4B, 0x0A4D],\n  [0x0A51, 0x0A51], [0x0A70, 0x0A71], [0x0A75, 0x0A75],\n  [0x0A81, 0x0A82], [0x0ABC, 0x0ABC], [0x0AC1, 0x0AC5],\n  [0x0AC7, 0x0AC8], [0x0ACD, 0x0ACD], [0x0AE2, 0x0AE3],\n  [0x0AFA, 0x0AFF], [0x0B01, 0x0B01], [0x0B3C, 0x0B3C],\n  [0x0B3F, 0x0B3F], [0x0B41, 0x0B44], [0x0B4D, 0x0B4D],\n  [0x0B56, 0x0B56], [0x0B62, 0x0B63], [0x0B82, 0x0B82],\n  [0x0BC0, 0x0BC0], [0x0BCD, 0x0BCD], [0x0C00, 0x0C00],\n  [0x0C04, 0x0C04], [0x0C3E, 0x0C40], [0x0C46, 0x0C48],\n  [0x0C4A, 0x0C4D], [0x0C55, 0x0C56], [0x0C62, 0x0C63],\n  [0x0C81, 0x0C81], [0x0CBC, 0x0CBC], [0x0CBF, 0x0CBF],\n  [0x0CC6, 0x0CC6], [0x0CCC, 0x0CCD], [0x0CE2, 0x0CE3],\n  [0x0D00, 0x0D01], [0x0D3B, 0x0D3C], [0x0D41, 0x0D44],\n  [0x0D4D, 0x0D4D], [0x0D62, 0x0D63], [0x0DCA, 0x0DCA],\n  [0x0DD2, 0x0DD4], [0x0DD6, 0x0DD6], [0x0E31, 0x0E31],\n  [0x0E34, 0x0E3A], [0x0E47, 0x0E4E], [0x0EB1, 0x0EB1],\n  [0x0EB4, 0x0EBC], [0x0EC8, 0x0ECD], [0x0F18, 0x0F19],\n  [0x0F35, 0x0F35], [0x0F37, 0x0F37], [0x0F39, 0x0F39],\n  [0x0F71, 0x0F7E], [0x0F80, 0x0F84], [0x0F86, 0x0F87],\n  [0x0F8D, 0x0F97], [0x0F99, 0x0FBC], [0x0FC6, 0x0FC6],\n  [0x102D, 0x1030], [0x1032, 0x1037], [0x1039, 0x103A],\n  [0x103D, 0x103E], [0x1058, 0x1059], [0x105E, 0x1060],\n  [0x1071, 0x1074], [0x1082, 0x1082], [0x1085, 0x1086],\n  [0x108D, 0x108D], [0x109D, 0x109D], [0x1160, 0x11FF],\n  [0x135D, 0x135F], [0x1712, 0x1714], [0x1732, 0x1734],\n  [0x1752, 0x1753], [0x1772, 0x1773], [0x17B4, 0x17B5],\n  [0x17B7, 0x17BD], [0x17C6, 0x17C6], [0x17C9, 0x17D3],\n  [0x17DD, 0x17DD], [0x180B, 0x180E], [0x1885, 0x1886],\n  [0x18A9, 0x18A9], [0x1920, 0x1922], [0x1927, 0x1928],\n  [0x1932, 0x1932], [0x1939, 0x193B], [0x1A17, 0x1A18],\n  [0x1A1B, 0x1A1B], [0x1A56, 0x1A56], [0x1A58, 0x1A5E],\n  [0x1A60, 0x1A60], [0x1A62, 0x1A62], [0x1A65, 0x1A6C],\n  [0x1A73, 0x1A7C], [0x1A7F, 0x1A7F], [0x1AB0, 0x1ABE],\n  [0x1B00, 0x1B03], [0x1B34, 0x1B34], [0x1B36, 0x1B3A],\n  [0x1B3C, 0x1B3C], [0x1B42, 0x1B42], [0x1B6B, 0x1B73],\n  [0x1B80, 0x1B81], [0x1BA2, 0x1BA5], [0x1BA8, 0x1BA9],\n  [0x1BAB, 0x1BAD], [0x1BE6, 0x1BE6], [0x1BE8, 0x1BE9],\n  [0x1BED, 0x1BED], [0x1BEF, 0x1BF1], [0x1C2C, 0x1C33],\n  [0x1C36, 0x1C37], [0x1CD0, 0x1CD2], [0x1CD4, 0x1CE0],\n  [0x1CE2, 0x1CE8], [0x1CED, 0x1CED], [0x1CF4, 0x1CF4],\n  [0x1CF8, 0x1CF9], [0x1DC0, 0x1DF9], [0x1DFB, 0x1DFF],\n  [0x200B, 0x200F], [0x202A, 0x202E], [0x2060, 0x2064],\n  [0x2066, 0x206F], [0x20D0, 0x20F0], [0x2CEF, 0x2CF1],\n  [0x2D7F, 0x2D7F], [0x2DE0, 0x2DFF], [0x302A, 0x302D],\n  [0x3099, 0x309A], [0xA66F, 0xA672], [0xA674, 0xA67D],\n  [0xA69E, 0xA69F], [0xA6F0, 0xA6F1], [0xA802, 0xA802],\n  [0xA806, 0xA806], [0xA80B, 0xA80B], [0xA825, 0xA826],\n  [0xA8C4, 0xA8C5], [0xA8E0, 0xA8F1], [0xA8FF, 0xA8FF],\n  [0xA926, 0xA92D], [0xA947, 0xA951], [0xA980, 0xA982],\n  [0xA9B3, 0xA9B3], [0xA9B6, 0xA9B9], [0xA9BC, 0xA9BD],\n  [0xA9E5, 0xA9E5], [0xAA29, 0xAA2E], [0xAA31, 0xAA32],\n  [0xAA35, 0xAA36], [0xAA43, 0xAA43], [0xAA4C, 0xAA4C],\n  [0xAA7C, 0xAA7C], [0xAAB0, 0xAAB0], [0xAAB2, 0xAAB4],\n  [0xAAB7, 0xAAB8], [0xAABE, 0xAABF], [0xAAC1, 0xAAC1],\n  [0xAAEC, 0xAAED], [0xAAF6, 0xAAF6], [0xABE5, 0xABE5],\n  [0xABE8, 0xABE8], [0xABED, 0xABED], [0xFB1E, 0xFB1E],\n  [0xFE00, 0xFE0F], [0xFE20, 0xFE2F], [0xFEFF, 0xFEFF],\n  [0xFFF9, 0xFFFB]\n];\nconst HIGH_COMBINING = [\n  [0x101FD, 0x101FD], [0x102E0, 0x102E0],\n  [0x10376, 0x1037A], [0x10A01, 0x10A03], [0x10A05, 0x10A06],\n  [0x10A0C, 0x10A0F], [0x10A38, 0x10A3A], [0x10A3F, 0x10A3F],\n  [0x10AE5, 0x10AE6], [0x10D24, 0x10D27], [0x10F46, 0x10F50],\n  [0x11001, 0x11001], [0x11038, 0x11046], [0x1107F, 0x11081],\n  [0x110B3, 0x110B6], [0x110B9, 0x110BA], [0x110BD, 0x110BD],\n  [0x110CD, 0x110CD], [0x11100, 0x11102], [0x11127, 0x1112B],\n  [0x1112D, 0x11134], [0x11173, 0x11173], [0x11180, 0x11181],\n  [0x111B6, 0x111BE], [0x111C9, 0x111CC], [0x1122F, 0x11231],\n  [0x11234, 0x11234], [0x11236, 0x11237], [0x1123E, 0x1123E],\n  [0x112DF, 0x112DF], [0x112E3, 0x112EA], [0x11300, 0x11301],\n  [0x1133B, 0x1133C], [0x11340, 0x11340], [0x11366, 0x1136C],\n  [0x11370, 0x11374], [0x11438, 0x1143F], [0x11442, 0x11444],\n  [0x11446, 0x11446], [0x1145E, 0x1145E], [0x114B3, 0x114B8],\n  [0x114BA, 0x114BA], [0x114BF, 0x114C0], [0x114C2, 0x114C3],\n  [0x115B2, 0x115B5], [0x115BC, 0x115BD], [0x115BF, 0x115C0],\n  [0x115DC, 0x115DD], [0x11633, 0x1163A], [0x1163D, 0x1163D],\n  [0x1163F, 0x11640], [0x116AB, 0x116AB], [0x116AD, 0x116AD],\n  [0x116B0, 0x116B5], [0x116B7, 0x116B7], [0x1171D, 0x1171F],\n  [0x11722, 0x11725], [0x11727, 0x1172B], [0x1182F, 0x11837],\n  [0x11839, 0x1183A], [0x119D4, 0x119D7], [0x119DA, 0x119DB],\n  [0x119E0, 0x119E0], [0x11A01, 0x11A0A], [0x11A33, 0x11A38],\n  [0x11A3B, 0x11A3E], [0x11A47, 0x11A47], [0x11A51, 0x11A56],\n  [0x11A59, 0x11A5B], [0x11A8A, 0x11A96], [0x11A98, 0x11A99],\n  [0x11C30, 0x11C36], [0x11C38, 0x11C3D], [0x11C3F, 0x11C3F],\n  [0x11C92, 0x11CA7], [0x11CAA, 0x11CB0], [0x11CB2, 0x11CB3],\n  [0x11CB5, 0x11CB6], [0x11D31, 0x11D36], [0x11D3A, 0x11D3A],\n  [0x11D3C, 0x11D3D], [0x11D3F, 0x11D45], [0x11D47, 0x11D47],\n  [0x11D90, 0x11D91], [0x11D95, 0x11D95], [0x11D97, 0x11D97],\n  [0x11EF3, 0x11EF4], [0x13430, 0x13438], [0x16AF0, 0x16AF4],\n  [0x16B30, 0x16B36], [0x16F4F, 0x16F4F], [0x16F8F, 0x16F92],\n  [0x1BC9D, 0x1BC9E], [0x1BCA0, 0x1BCA3], [0x1D167, 0x1D169],\n  [0x1D173, 0x1D182], [0x1D185, 0x1D18B], [0x1D1AA, 0x1D1AD],\n  [0x1D242, 0x1D244], [0x1DA00, 0x1DA36], [0x1DA3B, 0x1DA6C],\n  [0x1DA75, 0x1DA75], [0x1DA84, 0x1DA84], [0x1DA9B, 0x1DA9F],\n  [0x1DAA1, 0x1DAAF], [0x1E000, 0x1E006], [0x1E008, 0x1E018],\n  [0x1E01B, 0x1E021], [0x1E023, 0x1E024], [0x1E026, 0x1E02A],\n  [0x1E130, 0x1E136], [0x1E2EC, 0x1E2EF], [0x1E8D0, 0x1E8D6],\n  [0x1E944, 0x1E94A], [0xE0001, 0xE0001], [0xE0020, 0xE007F],\n  [0xE0100, 0xE01EF]\n];\nconst BMP_WIDE = [\n  [0x1100, 0x115F], [0x231A, 0x231B], [0x2329, 0x232A],\n  [0x23E9, 0x23EC], [0x23F0, 0x23F0], [0x23F3, 0x23F3],\n  [0x25FD, 0x25FE], [0x2614, 0x2615], [0x2648, 0x2653],\n  [0x267F, 0x267F], [0x2693, 0x2693], [0x26A1, 0x26A1],\n  [0x26AA, 0x26AB], [0x26BD, 0x26BE], [0x26C4, 0x26C5],\n  [0x26CE, 0x26CE], [0x26D4, 0x26D4], [0x26EA, 0x26EA],\n  [0x26F2, 0x26F3], [0x26F5, 0x26F5], [0x26FA, 0x26FA],\n  [0x26FD, 0x26FD], [0x2705, 0x2705], [0x270A, 0x270B],\n  [0x2728, 0x2728], [0x274C, 0x274C], [0x274E, 0x274E],\n  [0x2753, 0x2755], [0x2757, 0x2757], [0x2795, 0x2797],\n  [0x27B0, 0x27B0], [0x27BF, 0x27BF], [0x2B1B, 0x2B1C],\n  [0x2B50, 0x2B50], [0x2B55, 0x2B55], [0x2E80, 0x2E99],\n  [0x2E9B, 0x2EF3], [0x2F00, 0x2FD5], [0x2FF0, 0x2FFB],\n  [0x3000, 0x3029], [0x302E, 0x303E], [0x3041, 0x3096],\n  [0x309B, 0x30FF], [0x3105, 0x312F], [0x3131, 0x318E],\n  [0x3190, 0x31BA], [0x31C0, 0x31E3], [0x31F0, 0x321E],\n  [0x3220, 0x3247], [0x3250, 0x4DBF], [0x4E00, 0xA48C],\n  [0xA490, 0xA4C6], [0xA960, 0xA97C], [0xAC00, 0xD7A3],\n  [0xF900, 0xFAFF], [0xFE10, 0xFE19], [0xFE30, 0xFE52],\n  [0xFE54, 0xFE66], [0xFE68, 0xFE6B], [0xFF01, 0xFF60],\n  [0xFFE0, 0xFFE6]\n];\nconst HIGH_WIDE = [\n  [0x16FE0, 0x16FE3], [0x17000, 0x187F7],\n  [0x18800, 0x18AF2], [0x1B000, 0x1B11E], [0x1B150, 0x1B152],\n  [0x1B164, 0x1B167], [0x1B170, 0x1B2FB], [0x1F004, 0x1F004],\n  [0x1F0CF, 0x1F0CF], [0x1F18E, 0x1F18E], [0x1F191, 0x1F19A],\n  [0x1F200, 0x1F202], [0x1F210, 0x1F23B], [0x1F240, 0x1F248],\n  [0x1F250, 0x1F251], [0x1F260, 0x1F265], [0x1F300, 0x1F320],\n  [0x1F32D, 0x1F335], [0x1F337, 0x1F37C], [0x1F37E, 0x1F393],\n  [0x1F3A0, 0x1F3CA], [0x1F3CF, 0x1F3D3], [0x1F3E0, 0x1F3F0],\n  [0x1F3F4, 0x1F3F4], [0x1F3F8, 0x1F43E], [0x1F440, 0x1F440],\n  [0x1F442, 0x1F4FC], [0x1F4FF, 0x1F53D], [0x1F54B, 0x1F54E],\n  [0x1F550, 0x1F567], [0x1F57A, 0x1F57A], [0x1F595, 0x1F596],\n  [0x1F5A4, 0x1F5A4], [0x1F5FB, 0x1F64F], [0x1F680, 0x1F6C5],\n  [0x1F6CC, 0x1F6CC], [0x1F6D0, 0x1F6D2], [0x1F6D5, 0x1F6D5],\n  [0x1F6EB, 0x1F6EC], [0x1F6F4, 0x1F6FA], [0x1F7E0, 0x1F7EB],\n  [0x1F90D, 0x1F971], [0x1F973, 0x1F976], [0x1F97A, 0x1F9A2],\n  [0x1F9A5, 0x1F9AA], [0x1F9AE, 0x1F9CA], [0x1F9CD, 0x1F9FF],\n  [0x1FA70, 0x1FA73], [0x1FA78, 0x1FA7A], [0x1FA80, 0x1FA82],\n  [0x1FA90, 0x1FA95], [0x20000, 0x2FFFD], [0x30000, 0x3FFFD]\n];\n\n// BMP lookup table, lazy initialized during first addon loading\nlet table: Uint8Array;\n\nfunction bisearch(ucs: number, data: number[][]): boolean {\n  let min = 0;\n  let max = data.length - 1;\n  let mid;\n  if (ucs < data[0][0] || ucs > data[max][1]) {\n    return false;\n  }\n  while (max >= min) {\n    mid = (min + max) >> 1;\n    if (ucs > data[mid][1]) {\n      min = mid + 1;\n    } else if (ucs < data[mid][0]) {\n      max = mid - 1;\n    } else {\n      return true;\n    }\n  }\n  return false;\n}\n\n\nexport class UnicodeV11 implements IUnicodeVersionProvider {\n  public readonly version = '11';\n\n  constructor() {\n    if (!table) {\n      table = new Uint8Array(65536);\n      table.fill(1);\n      table[0] = 0;\n      table.fill(0, 1, 32);\n      table.fill(0, 0x7f, 0xa0);\n      for (let r = 0; r < BMP_COMBINING.length; ++r) {\n        table.fill(0, BMP_COMBINING[r][0], BMP_COMBINING[r][1] + 1);\n      }\n      for (let r = 0; r < BMP_WIDE.length; ++r) {\n        table.fill(2, BMP_WIDE[r][0], BMP_WIDE[r][1] + 1);\n      }\n    }\n  }\n\n  public wcwidth(num: number): UnicodeCharWidth {\n    if (num < 32) return 0;\n    if (num < 127) return 1;\n    if (num < 65536) return table[num] as UnicodeCharWidth;\n    if (bisearch(num, HIGH_COMBINING)) return 0;\n    if (bisearch(num, HIGH_WIDE)) return 2;\n    return 1;\n  }\n\n  public charProperties(codepoint: number, preceding: UnicodeCharProperties): UnicodeCharProperties {\n    let width = this.wcwidth(codepoint);\n    let shouldJoin = width === 0 && preceding !== 0;\n    if (shouldJoin) {\n      const oldWidth = UnicodeService.extractWidth(preceding);\n      if (oldWidth === 0) {\n        shouldJoin = false;\n      } else if (oldWidth > width) {\n        width = oldWidth;\n      }\n    }\n    return UnicodeService.createPropertyValue(0, width, shouldJoin);\n  }\n}\n", "/**\n * Copyright (c) 2019 The xterm.js authors. All rights reserved.\n * @license MIT\n *\n * UnicodeVersionProvider for V11.\n */\n\nimport type { Terminal, ITerminalAddon } from '@xterm/xterm';\nimport type { Unicode11Addon as IUnicode11Api } from '@xterm/addon-unicode11';\nimport { UnicodeV11 } from './UnicodeV11';\n\nexport class Unicode11Addon implements ITerminalAddon , IUnicode11Api {\n  public activate(terminal: Terminal): void {\n    terminal.unicode.register(new UnicodeV11());\n  }\n  public dispose(): void { }\n}\n"], "mappings": ";;;;;;;;;;;;;;;;AAOA,IAAMA,GAAgB,CACpB,CAAC,IAAQ,GAAM,EAAG,CAAC,KAAQ,IAAM,EAAG,CAAC,KAAQ,IAAM,EACnD,CAAC,KAAQ,IAAM,EAAG,CAAC,KAAQ,IAAM,EAAG,CAAC,KAAQ,IAAM,EACnD,CAAC,KAAQ,IAAM,EAAG,CAAC,KAAQ,IAAM,EAAG,CAAC,KAAQ,IAAM,EACnD,CAAC,KAAQ,IAAM,EAAG,CAAC,KAAQ,IAAM,EAAG,CAAC,KAAQ,IAAM,EACnD,CAAC,KAAQ,IAAM,EAAG,CAAC,KAAQ,IAAM,EAAG,CAAC,KAAQ,IAAM,EACnD,CAAC,KAAQ,IAAM,EAAG,CAAC,KAAQ,IAAM,EAAG,CAAC,KAAQ,IAAM,EACnD,CAAC,KAAQ,IAAM,EAAG,CAAC,KAAQ,IAAM,EAAG,CAAC,KAAQ,IAAM,EACnD,CAAC,KAAQ,IAAM,EAAG,CAAC,KAAQ,IAAM,EAAG,CAAC,KAAQ,IAAM,EACnD,CAAC,KAAQ,IAAM,EAAG,CAAC,KAAQ,IAAM,EAAG,CAAC,KAAQ,IAAM,EACnD,CAAC,KAAQ,IAAM,EAAG,CAAC,KAAQ,IAAM,EAAG,CAAC,KAAQ,IAAM,EACnD,CAAC,KAAQ,IAAM,EAAG,CAAC,KAAQ,IAAM,EAAG,CAAC,KAAQ,IAAM,EACnD,CAAC,KAAQ,IAAM,EAAG,CAAC,KAAQ,IAAM,EAAG,CAAC,KAAQ,IAAM,EACnD,CAAC,KAAQ,IAAM,EAAG,CAAC,KAAQ,IAAM,EAAG,CAAC,KAAQ,IAAM,EACnD,CAAC,KAAQ,IAAM,EAAG,CAAC,KAAQ,IAAM,EAAG,CAAC,KAAQ,IAAM,EACnD,CAAC,KAAQ,IAAM,EAAG,CAAC,KAAQ,IAAM,EAAG,CAAC,KAAQ,IAAM,EACnD,CAAC,KAAQ,IAAM,EAAG,CAAC,KAAQ,IAAM,EAAG,CAAC,KAAQ,IAAM,EACnD,CAAC,KAAQ,IAAM,EAAG,CAAC,KAAQ,IAAM,EAAG,CAAC,KAAQ,IAAM,EACnD,CAAC,KAAQ,IAAM,EAAG,CAAC,KAAQ,IAAM,EAAG,CAAC,KAAQ,IAAM,EACnD,CAAC,KAAQ,IAAM,EAAG,CAAC,KAAQ,IAAM,EAAG,CAAC,KAAQ,IAAM,EACnD,CAAC,KAAQ,IAAM,EAAG,CAAC,KAAQ,IAAM,EAAG,CAAC,KAAQ,IAAM,EACnD,CAAC,KAAQ,IAAM,EAAG,CAAC,KAAQ,IAAM,EAAG,CAAC,KAAQ,IAAM,EACnD,CAAC,KAAQ,IAAM,EAAG,CAAC,KAAQ,IAAM,EAAG,CAAC,KAAQ,IAAM,EACnD,CAAC,KAAQ,IAAM,EAAG,CAAC,KAAQ,IAAM,EAAG,CAAC,KAAQ,IAAM,EACnD,CAAC,KAAQ,IAAM,EAAG,CAAC,KAAQ,IAAM,EAAG,CAAC,KAAQ,IAAM,EACnD,CAAC,KAAQ,IAAM,EAAG,CAAC,KAAQ,IAAM,EAAG,CAAC,KAAQ,IAAM,EACnD,CAAC,KAAQ,IAAM,EAAG,CAAC,KAAQ,IAAM,EAAG,CAAC,KAAQ,IAAM,EACnD,CAAC,KAAQ,IAAM,EAAG,CAAC,KAAQ,IAAM,EAAG,CAAC,KAAQ,IAAM,EACnD,CAAC,KAAQ,IAAM,EAAG,CAAC,KAAQ,IAAM,EAAG,CAAC,KAAQ,IAAM,EACnD,CAAC,KAAQ,IAAM,EAAG,CAAC,KAAQ,IAAM,EAAG,CAAC,KAAQ,IAAM,EACnD,CAAC,KAAQ,IAAM,EAAG,CAAC,KAAQ,IAAM,EAAG,CAAC,KAAQ,IAAM,EACnD,CAAC,KAAQ,IAAM,EAAG,CAAC,KAAQ,IAAM,EAAG,CAAC,KAAQ,IAAM,EACnD,CAAC,KAAQ,IAAM,EAAG,CAAC,KAAQ,IAAM,EAAG,CAAC,KAAQ,IAAM,EACnD,CAAC,KAAQ,IAAM,EAAG,CAAC,KAAQ,IAAM,EAAG,CAAC,KAAQ,IAAM,EACnD,CAAC,KAAQ,IAAM,EAAG,CAAC,KAAQ,IAAM,EAAG,CAAC,KAAQ,IAAM,EACnD,CAAC,KAAQ,IAAM,EAAG,CAAC,KAAQ,IAAM,EAAG,CAAC,KAAQ,IAAM,EACnD,CAAC,KAAQ,IAAM,EAAG,CAAC,KAAQ,IAAM,EAAG,CAAC,KAAQ,IAAM,EACnD,CAAC,KAAQ,IAAM,EAAG,CAAC,KAAQ,IAAM,EAAG,CAAC,KAAQ,IAAM,EACnD,CAAC,KAAQ,IAAM,EAAG,CAAC,KAAQ,IAAM,EAAG,CAAC,KAAQ,IAAM,EACnD,CAAC,KAAQ,IAAM,EAAG,CAAC,KAAQ,IAAM,EAAG,CAAC,KAAQ,IAAM,EACnD,CAAC,KAAQ,IAAM,EAAG,CAAC,KAAQ,IAAM,EAAG,CAAC,MAAQ,KAAM,EACnD,CAAC,MAAQ,KAAM,EAAG,CAAC,MAAQ,KAAM,EAAG,CAAC,MAAQ,KAAM,EACnD,CAAC,MAAQ,KAAM,EAAG,CAAC,MAAQ,KAAM,EAAG,CAAC,MAAQ,KAAM,EACnD,CAAC,MAAQ,KAAM,EAAG,CAAC,MAAQ,KAAM,EAAG,CAAC,MAAQ,KAAM,CACrD,EACMC,GAAiB,CACrB,CAAC,MAAS,KAAO,EAAG,CAAC,MAAS,KAAO,EAAG,CAAC,MAAS,KAAO,EACzD,CAAC,MAAS,KAAO,EAAG,CAAC,MAAS,KAAO,EAAG,CAAC,OAAS,MAAO,EACzD,CAAC,OAAS,MAAO,EAAG,CAAC,OAAS,MAAO,EAAG,CAAC,OAAS,MAAO,EACzD,CAAC,OAAS,MAAO,EAAG,CAAC,OAAS,MAAO,EAAG,CAAC,OAAS,MAAO,EACzD,CAAC,OAAS,MAAO,CACnB,EAGIC,EAEJ,SAASC,GAASC,EAAaC,EAA2B,CACxD,IAAIC,EAAM,EACNC,EAAMF,EAAK,OAAS,EACpBG,EACJ,GAAIJ,EAAMC,EAAK,CAAC,EAAE,CAAC,GAAKD,EAAMC,EAAKE,CAAG,EAAE,CAAC,EACvC,MAAO,GAET,KAAOA,GAAOD,GAEZ,GADAE,EAAOF,EAAMC,GAAQ,EACjBH,EAAMC,EAAKG,CAAG,EAAE,CAAC,EACnBF,EAAME,EAAM,UACHJ,EAAMC,EAAKG,CAAG,EAAE,CAAC,EAC1BD,EAAMC,EAAM,MAEZ,OAAO,GAGX,MAAO,EACT,CAEO,IAAMC,EAAN,KAAmD,CAGxD,aAAc,CAFd,KAAgB,QAAU,IAIxB,GAAI,CAACP,EAAO,CACVA,EAAQ,IAAI,WAAW,KAAK,EAC5BA,EAAM,KAAK,CAAC,EACZA,EAAM,CAAC,EAAI,EAEXA,EAAM,KAAK,EAAG,EAAG,EAAE,EACnBA,EAAM,KAAK,EAAG,IAAM,GAAI,EAIxBA,EAAM,KAAK,EAAG,KAAQ,IAAM,EAC5BA,EAAM,IAAM,EAAI,EAChBA,EAAM,IAAM,EAAI,EAChBA,EAAM,KAAK,EAAG,MAAQ,KAAM,EAC5BA,EAAM,KAAM,EAAI,EAEhBA,EAAM,KAAK,EAAG,MAAQ,KAAM,EAC5BA,EAAM,KAAK,EAAG,MAAQ,KAAM,EAC5BA,EAAM,KAAK,EAAG,MAAQ,KAAM,EAC5BA,EAAM,KAAK,EAAG,MAAQ,KAAM,EAC5BA,EAAM,KAAK,EAAG,MAAQ,KAAM,EAC5BA,EAAM,KAAK,EAAG,MAAQ,KAAM,EAO5B,QAASQ,EAAI,EAAGA,EAAIV,GAAc,OAAQ,EAAEU,EAC1CR,EAAM,KAAK,EAAGF,GAAcU,CAAC,EAAE,CAAC,EAAGV,GAAcU,CAAC,EAAE,CAAC,EAAI,CAAC,CAE9D,CACF,CAEO,QAAQC,EAA+B,CAC5C,OAAIA,EAAM,GAAW,EACjBA,EAAM,IAAY,EAClBA,EAAM,MAAcT,EAAMS,CAAG,EAC7BR,GAASQ,EAAKV,EAAc,EAAU,EACrCU,GAAO,QAAWA,GAAO,QAAaA,GAAO,QAAWA,GAAO,OAAiB,EAC9E,CACT,CAEO,eAAeC,EAAmBC,EAAyD,CAChG,IAAIC,EAAQ,KAAK,QAAQF,CAAS,EAC9BG,EAAaD,IAAU,GAAKD,IAAc,EAC9C,GAAIE,EAAY,CACd,IAAMC,EAAWC,EAAe,aAAaJ,CAAS,EAClDG,IAAa,EACfD,EAAa,GACJC,EAAWF,IACpBA,EAAQE,EAEZ,CACA,OAAOC,EAAe,oBAAoB,EAAGH,EAAOC,CAAU,CAChE,CACF,EClIO,IAAMG,GAAN,KAAmB,CAIzB,aAAc,CAEb,KAAK,UAAY,CAAC,EAElB,KAAK,uBAAyB,SAAU,EAAQ,CAC/C,WAAW,IAAM,CAChB,MAAI,EAAE,MACDC,EAAiB,mBAAmB,CAAC,EAClC,IAAIA,EAAiB,EAAE,QAAU;AAAA;AAAA,EAAS,EAAE,KAAK,EAGlD,IAAI,MAAM,EAAE,QAAU;AAAA;AAAA,EAAS,EAAE,KAAK,EAGvC,CACP,EAAG,CAAC,CACL,CACD,CAEA,YAAYC,EAAsD,CACjE,YAAK,UAAU,KAAKA,CAAQ,EAErB,IAAM,CACZ,KAAK,gBAAgBA,CAAQ,CAC9B,CACD,CAEQ,KAAK,EAAc,CAC1B,KAAK,UAAU,QAASA,GAAa,CACpCA,EAAS,CAAC,CACX,CAAC,CACF,CAEQ,gBAAgBA,EAAuC,CAC9D,KAAK,UAAU,OAAO,KAAK,UAAU,QAAQA,CAAQ,EAAG,CAAC,CAC1D,CAEA,0BAA0BC,EAAmD,CAC5E,KAAK,uBAAyBA,CAC/B,CAEA,2BAA8C,CAC7C,OAAO,KAAK,sBACb,CAEA,kBAAkB,EAAc,CAC/B,KAAK,uBAAuB,CAAC,EAC7B,KAAK,KAAK,CAAC,CACZ,CAGA,0BAA0B,EAAc,CACvC,KAAK,uBAAuB,CAAC,CAC9B,CACD,EAEaC,GAAe,IAAIJ,GAsBzB,SAASK,EAAkBC,EAAmB,CAE/CC,GAAoBD,CAAC,GACzBE,GAAa,kBAAkBF,CAAC,CAGlC,CAoEA,IAAMG,GAAe,WAKd,SAASC,GAAoBC,EAAqB,CACxD,OAAIA,aAAiBC,EACb,GAEDD,aAAiB,OAASA,EAAM,OAASF,IAAgBE,EAAM,UAAYF,EACnF,CAIO,IAAMG,EAAN,cAAgC,KAAM,CAC5C,aAAc,CACb,MAAMH,EAAY,EAClB,KAAK,KAAO,KAAK,OAClB,CACD,EA0EO,IAAMI,EAAN,MAAMC,UAAyB,KAAM,CAG3C,YAAYC,EAAc,CACzB,MAAMA,CAAG,EACT,KAAK,KAAO,mBACb,CAEA,OAAc,UAAUC,EAA8B,CACrD,GAAIA,aAAeF,EAClB,OAAOE,EAGR,IAAMC,EAAS,IAAIH,EACnB,OAAAG,EAAO,QAAUD,EAAI,QACrBC,EAAO,MAAQD,EAAI,MACZC,CACR,CAEA,OAAc,mBAAmBD,EAAqC,CACrE,OAAOA,EAAI,OAAS,mBACrB,CACD,ECrRO,SAASE,GAA4DC,EAAOC,EAAkC,CACpH,IAAMC,EAAQ,KACVC,EAAU,GACVC,EAEJ,OAAO,UAAY,CAClB,GAAID,EACH,OAAOC,EAIR,GADAD,EAAU,GACNF,EACH,GAAI,CACHG,EAASJ,EAAG,MAAME,EAAO,SAAS,CACnC,QAAE,CACDD,EAAiB,CAClB,MAEAG,EAASJ,EAAG,MAAME,EAAO,SAAS,EAGnC,OAAOE,CACR,CACD,CCaO,SAASC,GAAyBC,EAAqBC,EAAiCC,EAAW,EAAGC,EAAWH,EAAM,OAAgB,CAC7I,IAAII,EAAIF,EACJG,EAAIF,EACR,KAAOC,EAAIC,GAAG,CACb,IAAMC,EAAI,KAAK,OAAOF,EAAIC,GAAK,CAAC,EAC5BJ,EAAUD,EAAMM,CAAC,CAAC,EACrBF,EAAIE,EAAI,EAERD,EAAIC,CAEN,CACA,OAAOF,EAAI,CACZ,CA4CO,IAAMG,EAAN,MAAMA,CAAmB,CAM/B,YAA6BC,EAAsB,CAAtB,YAAAA,EAH7B,KAAQ,2BAA6B,CAIrC,CAMA,mBAAmBC,EAAgD,CAClE,GAAIF,EAAgB,iBAAkB,CACrC,GAAI,KAAK,wBACR,QAAWG,KAAQ,KAAK,OACvB,GAAI,KAAK,uBAAuBA,CAAI,GAAK,CAACD,EAAUC,CAAI,EACvD,MAAM,IAAI,MAAM,8FAA8F,EAIjH,KAAK,uBAAyBD,CAC/B,CAEA,IAAME,EAAMC,GAAsB,KAAK,OAAQH,EAAW,KAAK,0BAA0B,EACzF,YAAK,2BAA6BE,EAAM,EACjCA,IAAQ,GAAK,OAAY,KAAK,OAAOA,CAAG,CAChD,CACD,EA7BaJ,EACE,iBAAmB,GAD3B,IAAMM,GAANN,EC8hBA,IAAUO,OAAV,CACC,SAASC,EAAWC,EAAgC,CAC1D,OAAOA,EAAS,CACjB,CAFOF,EAAS,WAAAC,EAIT,SAASE,EAAkBD,EAAgC,CACjE,OAAOA,GAAU,CAClB,CAFOF,EAAS,kBAAAG,EAIT,SAASC,EAAcF,EAAgC,CAC7D,OAAOA,EAAS,CACjB,CAFOF,EAAS,cAAAI,EAIT,SAASC,EAA2BH,EAAgC,CAC1E,OAAOA,IAAW,CACnB,CAFOF,EAAS,2BAAAK,EAIHL,EAAA,YAAc,EACdA,EAAA,SAAW,GACXA,EAAA,yBAA2B,IAnBxBA,KAAA,IA6BV,SAASM,GAA6BC,EAAuCC,EAAuD,CAC1I,MAAO,CAACC,EAAGC,IAAMF,EAAWD,EAASE,CAAC,EAAGF,EAASG,CAAC,CAAC,CACrD,CAiBO,IAAMC,GAAuC,CAACC,EAAGC,IAAMD,EAAIC,EA4F3D,IAAMC,EAAN,MAAMA,CAAoB,CAGhC,YAKiBC,EACf,CADe,aAAAA,CAEjB,CAEA,QAAQC,EAA4B,CACnC,KAAK,QAAQC,IAAUD,EAAQC,CAAI,EAAU,GAAO,CACrD,CAEA,SAAe,CACd,IAAMC,EAAc,CAAC,EACrB,YAAK,QAAQD,IAAUC,EAAO,KAAKD,CAAI,EAAU,GAAO,EACjDC,CACR,CAEA,OAAOC,EAAsD,CAC5D,OAAO,IAAIL,EAAiBM,GAAM,KAAK,QAAQH,GAAQE,EAAUF,CAAI,EAAIG,EAAGH,CAAI,EAAI,EAAI,CAAC,CAC1F,CAEA,IAAaI,EAAwD,CACpE,OAAO,IAAIP,EAA0BM,GAAM,KAAK,QAAQH,GAAQG,EAAGC,EAAMJ,CAAI,CAAC,CAAC,CAAC,CACjF,CAEA,KAAKE,EAA0C,CAC9C,IAAID,EAAS,GACb,YAAK,QAAQD,IAAUC,EAASC,EAAUF,CAAI,EAAU,CAACC,EAAS,EAC3DA,CACR,CAEA,UAAUC,EAAgD,CACzD,IAAID,EACJ,YAAK,QAAQD,GACRE,EAAUF,CAAI,GACjBC,EAASD,EACF,IAED,EACP,EACMC,CACR,CAEA,SAASC,EAAgD,CACxD,IAAID,EACJ,YAAK,QAAQD,IACRE,EAAUF,CAAI,IACjBC,EAASD,GAEH,GACP,EACMC,CACR,CAEA,cAAcI,EAA0C,CACvD,IAAIJ,EACAK,EAAQ,GACZ,YAAK,QAAQN,KACRM,GAASC,GAAc,cAAcF,EAAWL,EAAMC,CAAO,CAAC,KACjEK,EAAQ,GACRL,EAASD,GAEH,GACP,EACMC,CACR,CACD,EAvEaJ,EACW,MAAQ,IAAIA,EAAwBW,GAAa,CAAE,CAAC,EADrE,IAAMC,GAANZ,ECzvBA,SAASa,GAA+CC,EAAWC,EAA4C,CACrH,IAAMC,EAAyB,OAAO,OAAO,IAAI,EACjD,QAAWC,KAAWH,EAAM,CAC3B,IAAMI,EAAMH,EAAQE,CAAO,EACvBE,EAASH,EAAOE,CAAG,EAClBC,IACJA,EAASH,EAAOE,CAAG,EAAI,CAAC,GAEzBC,EAAO,KAAKF,CAAO,CACpB,CACA,OAAOD,CACR,CAhCA,IAAAI,GAAAC,GAmFaC,GAAN,KAAsC,CAG5C,YAAYC,EAAqBC,EAAsB,CAAtB,WAAAA,EAFjC,KAAQ,KAAO,IAAI,IAsDnB,KAACJ,IAA8B,aAnD9B,QAAWK,KAASF,EACnB,KAAK,IAAIE,CAAK,CAEhB,CAEA,IAAI,MAAe,CAClB,OAAO,KAAK,KAAK,IAClB,CAEA,IAAIA,EAAgB,CACnB,IAAMC,EAAM,KAAK,MAAMD,CAAK,EAC5B,YAAK,KAAK,IAAIC,EAAKD,CAAK,EACjB,IACR,CAEA,OAAOA,EAAmB,CACzB,OAAO,KAAK,KAAK,OAAO,KAAK,MAAMA,CAAK,CAAC,CAC1C,CAEA,IAAIA,EAAmB,CACtB,OAAO,KAAK,KAAK,IAAI,KAAK,MAAMA,CAAK,CAAC,CACvC,CAEA,CAAC,SAAoC,CACpC,QAAWE,KAAS,KAAK,KAAK,OAAO,EACpC,KAAM,CAACA,EAAOA,CAAK,CAErB,CAEA,MAA4B,CAC3B,OAAO,KAAK,OAAO,CACpB,CAEA,CAAC,QAA8B,CAC9B,QAAWA,KAAS,KAAK,KAAK,OAAO,EACpC,MAAMA,CAER,CAEA,OAAc,CACb,KAAK,KAAK,MAAM,CACjB,CAEA,QAAQC,EAAwDC,EAAqB,CACpF,KAAK,KAAK,QAAQF,GAASC,EAAW,KAAKC,EAASF,EAAOA,EAAO,IAAI,CAAC,CACxE,CAEA,EAACN,GAAA,OAAO,SAIPD,GAAA,OAAO,YAJPC,GAAe,GAAyB,CACxC,OAAO,KAAK,OAAO,CACpB,CAGD,ECRO,IAAMS,EAAN,KAAmB,CAAnB,cAEN,KAAQ,IAAM,IAAI,IAElB,IAAIC,EAAQC,EAAgB,CAC3B,IAAIC,EAAS,KAAK,IAAI,IAAIF,CAAG,EAExBE,IACJA,EAAS,IAAI,IACb,KAAK,IAAI,IAAIF,EAAKE,CAAM,GAGzBA,EAAO,IAAID,CAAK,CACjB,CAEA,OAAOD,EAAQC,EAAgB,CAC9B,IAAMC,EAAS,KAAK,IAAI,IAAIF,CAAG,EAE1BE,IAILA,EAAO,OAAOD,CAAK,EAEfC,EAAO,OAAS,GACnB,KAAK,IAAI,OAAOF,CAAG,EAErB,CAEA,QAAQA,EAAQG,EAA8B,CAC7C,IAAMD,EAAS,KAAK,IAAI,IAAIF,CAAG,EAE1BE,GAILA,EAAO,QAAQC,CAAE,CAClB,CAEA,IAAIH,EAAwB,CAC3B,IAAME,EAAS,KAAK,IAAI,IAAIF,CAAG,EAC/B,OAAKE,GACG,IAAI,GAGb,CACD,EC5KO,IAAUE,QAAV,CAEC,SAASC,EAAYC,EAAkC,CAC7D,OAAOA,GAAS,OAAOA,GAAU,UAAY,OAAOA,EAAM,OAAO,QAAQ,GAAM,UAChF,CAFOF,GAAS,GAAAC,EAIhB,IAAME,EAAwB,OAAO,OAAO,CAAC,CAAC,EACvC,SAASC,GAA8B,CAC7C,OAAOD,CACR,CAFOH,GAAS,MAAAI,EAIT,SAAUC,EAAUC,EAAyB,CACnD,MAAMA,CACP,CAFON,GAAU,OAAAK,EAIV,SAASE,EAAQC,EAAiD,CACxE,OAAIP,EAAGO,CAAiB,EAChBA,EAEAH,EAAOG,CAAiB,CAEjC,CANOR,GAAS,KAAAO,EAQT,SAASE,EAAQC,EAAuD,CAC9E,OAAOA,GAAYP,CACpB,CAFOH,GAAS,KAAAS,EAIT,SAAUE,EAAWC,EAA8B,CACzD,QAASC,EAAID,EAAM,OAAS,EAAGC,GAAK,EAAGA,IACtC,MAAMD,EAAMC,CAAC,CAEf,CAJOb,GAAU,QAAAW,EAMV,SAASG,EAAWJ,EAAmD,CAC7E,MAAO,CAACA,GAAYA,EAAS,OAAO,QAAQ,EAAE,EAAE,KAAK,EAAE,OAAS,EACjE,CAFOV,GAAS,QAAAc,EAIT,SAASC,EAASL,EAAsC,CAC9D,OAAOA,EAAS,OAAO,QAAQ,EAAE,EAAE,KAAK,EAAE,KAC3C,CAFOV,GAAS,MAAAe,EAIT,SAASC,EAAQN,EAAuBO,EAAkD,CAChG,IAAIJ,EAAI,EACR,QAAWP,KAAWI,EACrB,GAAIO,EAAUX,EAASO,GAAG,EACzB,MAAO,GAGT,MAAO,EACR,CAROb,GAAS,KAAAgB,EAYT,SAASE,EAAQR,EAAuBO,EAA6C,CAC3F,QAAWX,KAAWI,EACrB,GAAIO,EAAUX,CAAO,EACpB,OAAOA,CAKV,CARON,GAAS,KAAAkB,EAYT,SAAUC,EAAUT,EAAuBO,EAA2C,CAC5F,QAAWX,KAAWI,EACjBO,EAAUX,CAAO,IACpB,MAAMA,EAGT,CANON,GAAU,OAAAmB,EAQV,SAAUC,EAAUV,EAAuBW,EAA6C,CAC9F,IAAIC,EAAQ,EACZ,QAAWhB,KAAWI,EACrB,MAAMW,EAAGf,EAASgB,GAAO,CAE3B,CALOtB,GAAU,IAAAoB,EAOV,SAAUG,EAAcb,EAAuBW,EAAuD,CAC5G,IAAIC,EAAQ,EACZ,QAAWhB,KAAWI,EACrB,MAAOW,EAAGf,EAASgB,GAAO,CAE5B,CALOtB,GAAU,QAAAuB,EAOV,SAAUC,MAAaC,EAAuC,CACpE,QAAWf,KAAYe,EACtB,MAAOf,CAET,CAJOV,GAAU,OAAAwB,GAMV,SAASE,EAAahB,EAAuBiB,EAAmDC,EAAoB,CAC1H,IAAIC,EAAQD,EACZ,QAAWtB,KAAWI,EACrBmB,EAAQF,EAAQE,EAAOvB,CAAO,EAE/B,OAAOuB,CACR,CANO7B,GAAS,OAAA0B,EAWT,SAAUI,EAASC,EAAuBtB,EAAcuB,EAAKD,EAAI,OAAqB,CAW5F,IAVItB,EAAO,IACVA,GAAQsB,EAAI,QAGTC,EAAK,EACRA,GAAMD,EAAI,OACAC,EAAKD,EAAI,SACnBC,EAAKD,EAAI,QAGHtB,EAAOuB,EAAIvB,IACjB,MAAMsB,EAAItB,CAAI,CAEhB,CAdOT,GAAU,MAAA8B,EAoBV,SAASG,GAAWvB,EAAuBwB,EAAiB,OAAO,kBAAuC,CAChH,IAAMC,EAAgB,CAAC,EAEvB,GAAID,IAAW,EACd,MAAO,CAACC,EAAUzB,CAAQ,EAG3B,IAAM0B,EAAW1B,EAAS,OAAO,QAAQ,EAAE,EAE3C,QAASG,EAAI,EAAGA,EAAIqB,EAAQrB,IAAK,CAChC,IAAMwB,GAAOD,EAAS,KAAK,EAE3B,GAAIC,GAAK,KACR,MAAO,CAACF,EAAUnC,GAAS,MAAM,CAAC,EAGnCmC,EAAS,KAAKE,GAAK,KAAK,CACzB,CAEA,MAAO,CAACF,EAAU,CAAE,CAAC,OAAO,QAAQ,GAAI,CAAE,OAAOC,CAAU,CAAE,CAAC,CAC/D,CApBOpC,GAAS,QAAAiC,GAsBhB,eAAsBK,EAAgB5B,EAA0C,CAC/E,IAAM6B,EAAc,CAAC,EACrB,cAAiBC,KAAQ9B,EACxB6B,EAAO,KAAKC,CAAI,EAEjB,OAAO,QAAQ,QAAQD,CAAM,CAC9B,CANAvC,GAAsB,aAAAsC,IAlJNtC,KAAA,ICejB,IAAMyC,GAAoB,GACtBC,EAA+C,KAiCtCC,GAAN,MAAMA,EAAgD,CAAtD,cAGN,KAAiB,kBAAoB,IAAI,IAEjC,kBAAkBC,EAAgC,CACzD,IAAIC,EAAM,KAAK,kBAAkB,IAAID,CAAC,EACtC,OAAKC,IACJA,EAAM,CAAE,OAAQ,KAAM,OAAQ,KAAM,YAAa,GAAO,MAAOD,EAAG,IAAKD,GAAkB,KAAM,EAC/F,KAAK,kBAAkB,IAAIC,EAAGC,CAAG,GAE3BA,CACR,CAEA,gBAAgBD,EAAsB,CACrC,IAAME,EAAO,KAAK,kBAAkBF,CAAC,EAChCE,EAAK,SACTA,EAAK,OACJ,IAAI,MAAM,EAAE,MAEf,CAEA,UAAUC,EAAoBC,EAAkC,CAC/D,IAAMF,EAAO,KAAK,kBAAkBC,CAAK,EACzCD,EAAK,OAASE,CACf,CAEA,eAAeC,EAAsB,CACpC,KAAK,kBAAkB,OAAOA,CAAC,CAChC,CAEA,gBAAgBC,EAA+B,CAC9C,KAAK,kBAAkBA,CAAU,EAAE,YAAc,EAClD,CAEQ,cAAcJ,EAAsBK,EAA4D,CACvG,IAAMC,EAAaD,EAAM,IAAIL,CAAI,EACjC,GAAIM,EACH,OAAOA,EAGR,IAAMC,EAASP,EAAK,OAAS,KAAK,cAAc,KAAK,kBAAkBA,EAAK,MAAM,EAAGK,CAAK,EAAIL,EAC9F,OAAAK,EAAM,IAAIL,EAAMO,CAAM,EACfA,CACR,CAEA,uBAAuC,CACtC,IAAMC,EAAkB,IAAI,IAM5B,MAJgB,CAAC,GAAG,KAAK,kBAAkB,QAAQ,CAAC,EAClD,OAAO,CAAC,CAAC,CAAEC,CAAC,IAAMA,EAAE,SAAW,MAAQ,CAAC,KAAK,cAAcA,EAAGD,CAAe,EAAE,WAAW,EAC1F,QAAQ,CAAC,CAACE,CAAC,IAAMA,CAAC,CAGrB,CAEA,0BAA0BC,EAAc,GAAIC,EAA+F,CAC1I,IAAIC,EACJ,GAAID,EACHC,EAAuBD,MACjB,CACN,IAAMJ,EAAkB,IAAI,IAEtBM,EAAiB,CAAC,GAAG,KAAK,kBAAkB,OAAO,CAAC,EACxD,OAAQC,GAASA,EAAK,SAAW,MAAQ,CAAC,KAAK,cAAcA,EAAMP,CAAe,EAAE,WAAW,EAEjG,GAAIM,EAAe,SAAW,EAC7B,OAED,IAAME,EAAiB,IAAI,IAAIF,EAAe,IAAIG,GAAKA,EAAE,KAAK,CAAC,EAO/D,GAJAJ,EAAuBC,EAAe,OAAOI,GACrC,EAAEA,EAAE,QAAUF,EAAe,IAAIE,EAAE,MAAM,EAChD,EAEGL,EAAqB,SAAW,EACnC,MAAM,IAAI,MAAM,oCAAoC,CAEtD,CAEA,GAAI,CAACA,EACJ,OAGD,SAASM,EAAkBC,EAAmC,CAC7D,SAASC,EAAaC,EAAiBC,EAAoC,CAC1E,KAAOD,EAAM,OAAS,GAAKC,EAAc,KAAKC,GAAU,OAAOA,GAAW,SAAWA,IAAWF,EAAM,CAAC,EAAIA,EAAM,CAAC,EAAE,MAAME,CAAM,CAAC,GAChIF,EAAM,MAAM,CAEd,CAEA,IAAMG,EAAQL,EAAQ,OAAQ,MAAM;AAAA,CAAI,EAAE,IAAIM,GAAKA,EAAE,KAAK,EAAE,QAAQ,MAAO,EAAE,CAAC,EAAE,OAAOR,GAAKA,IAAM,EAAE,EACpG,OAAAG,EAAaI,EAAO,CAAC,QAAS,2BAA4B,4CAA4C,CAAC,EAChGA,EAAM,QAAQ,CACtB,CAEA,IAAME,EAAmB,IAAIC,EAC7B,QAAWR,KAAWP,EAAsB,CAC3C,IAAMgB,EAAiBV,EAAkBC,CAAO,EAChD,QAASU,EAAI,EAAGA,GAAKD,EAAe,OAAQC,IAC3CH,EAAiB,IAAIE,EAAe,MAAM,EAAGC,CAAC,EAAE,KAAK;AAAA,CAAI,EAAGV,CAAO,CAErE,CAGAP,EAAqB,KAAKkB,GAAUb,GAAKA,EAAE,IAAKc,EAAgB,CAAC,EAEjE,IAAIC,EAAU,GAEVH,EAAI,EACR,QAAWV,KAAWP,EAAqB,MAAM,EAAGF,CAAW,EAAG,CACjEmB,IACA,IAAMD,EAAiBV,EAAkBC,CAAO,EAC1Cc,EAA2B,CAAC,EAElC,QAASJ,EAAI,EAAGA,EAAID,EAAe,OAAQC,IAAK,CAC/C,IAAIK,EAAON,EAAeC,CAAC,EAE3BK,EAAO,gBADQR,EAAiB,IAAIE,EAAe,MAAM,EAAGC,EAAI,CAAC,EAAE,KAAK;AAAA,CAAI,CAAC,EAC/C,IAAI,IAAIjB,EAAqB,MAAM,cAAcsB,CAAI,GAEnF,IAAMC,GAAaT,EAAiB,IAAIE,EAAe,MAAM,EAAGC,CAAC,EAAE,KAAK;AAAA,CAAI,CAAC,EACvEO,EAAgBC,GAAQ,CAAC,GAAGF,EAAU,EAAE,IAAItC,GAAKqB,EAAkBrB,CAAC,EAAEgC,CAAC,CAAC,EAAGrB,GAAKA,CAAC,EACvF,OAAO4B,EAAcR,EAAeC,CAAC,CAAC,EACtC,OAAW,CAACS,EAAMC,EAAG,IAAK,OAAO,QAAQH,CAAa,EACrDH,EAAyB,QAAQ,wBAAwBM,GAAI,MAAM,8BAA8BD,CAAI,EAAE,EAGxGL,EAAyB,QAAQC,CAAI,CACtC,CAEAF,GAAW;AAAA;AAAA;AAAA,0CAAiDH,CAAC,IAAIjB,EAAqB,MAAM,KAAKO,EAAQ,MAAM,YAAY,IAAI;AAAA,EAA0Bc,EAAyB,KAAK;AAAA,CAAI,CAAC;AAAA;AAAA;AAAA,CAC7L,CAEA,OAAIrB,EAAqB,OAASF,IACjCsB,GAAW;AAAA;AAAA;AAAA,UAAiBpB,EAAqB,OAASF,CAAW;AAAA;AAAA,GAG/D,CAAE,MAAOE,EAAsB,QAASoB,CAAQ,CACxD,CACD,EA5IapC,GACG,IAAM,EADf,IAAM4C,GAAN5C,GA8IA,SAAS6C,GAAqBC,EAA0C,CAC9E/C,EAAoB+C,CACrB,CAEA,GAAIhD,GAAmB,CACtB,IAAMiD,EAA4B,4BAClCF,GAAqB,IAAI,KAAoC,CAC5D,gBAAgBvC,EAAsB,CACrC,IAAM0C,EAAQ,IAAI,MAAM,+BAA+B,EAAE,MACzD,WAAW,IAAM,CACV1C,EAAUyC,CAAyB,GACxC,QAAQ,IAAIC,CAAK,CAEnB,EAAG,GAAI,CACR,CAEA,UAAU5C,EAAoBC,EAAkC,CAC/D,GAAID,GAASA,IAAU6C,EAAW,KACjC,GAAI,CACF7C,EAAc2C,CAAyB,EAAI,EAC7C,MAAQ,CAER,CAEF,CAEA,eAAexC,EAA+B,CAC7C,GAAIA,GAAcA,IAAe0C,EAAW,KAC3C,GAAI,CACF1C,EAAmBwC,CAAyB,EAAI,EAClD,MAAQ,CAER,CAEF,CACA,gBAAgBxC,EAA+B,CAAE,CAClD,CAAC,CACF,CAEO,SAAS2C,GAAuC5C,EAAS,CAC/D,OAAAP,GAAmB,gBAAgBO,CAAC,EAC7BA,CACR,CAEO,SAAS6C,GAAe5C,EAA+B,CAC7DR,GAAmB,eAAeQ,CAAU,CAC7C,CAEA,SAAS6C,GAAsBhD,EAAoBC,EAAkC,CACpFN,GAAmB,UAAUK,EAAOC,CAAM,CAC3C,CAEA,SAASgD,GAAuBC,EAAyBjD,EAAkC,CAC1F,GAAKN,EAGL,QAAWK,KAASkD,EACnBvD,EAAkB,UAAUK,EAAOC,CAAM,CAE3C,CAwCO,SAASkD,GAA+BC,EAAuC,CACrF,GAAIC,GAAS,GAAGD,CAAG,EAAG,CACrB,IAAME,EAAgB,CAAC,EAEvB,QAAWC,KAAKH,EACf,GAAIG,EACH,GAAI,CACHA,EAAE,QAAQ,CACX,OAASC,EAAG,CACXF,EAAO,KAAKE,CAAC,CACd,CAIF,GAAIF,EAAO,SAAW,EACrB,MAAMA,EAAO,CAAC,EACR,GAAIA,EAAO,OAAS,EAC1B,MAAM,IAAI,eAAeA,EAAQ,6CAA6C,EAG/E,OAAO,MAAM,QAAQF,CAAG,EAAI,CAAC,EAAIA,CAClC,SAAWA,EACV,OAAAA,EAAI,QAAQ,EACLA,CAET,CAcO,SAASK,MAAsBC,EAAyC,CAC9E,IAAMC,EAASC,GAAa,IAAMC,GAAQH,CAAW,CAAC,EACtD,OAAAI,GAAuBJ,EAAaC,CAAM,EACnCA,CACR,CAOO,SAASC,GAAaG,EAA6B,CACzD,IAAMC,EAAOC,GAAgB,CAC5B,QAASC,GAAyB,IAAM,CACvCC,GAAeH,CAAI,EACnBD,EAAG,CACJ,CAAC,CACF,CAAC,EACD,OAAOC,CACR,CASO,IAAMI,GAAN,MAAMA,EAAuC,CAOnD,aAAc,CAHd,KAAiB,WAAa,IAAI,IAClC,KAAQ,YAAc,GAGrBH,GAAgB,IAAI,CACrB,CAOO,SAAgB,CAClB,KAAK,cAITE,GAAe,IAAI,EACnB,KAAK,YAAc,GACnB,KAAK,MAAM,EACZ,CAKA,IAAW,YAAsB,CAChC,OAAO,KAAK,WACb,CAKO,OAAc,CACpB,GAAI,KAAK,WAAW,OAAS,EAI7B,GAAI,CACHN,GAAQ,KAAK,UAAU,CACxB,QAAE,CACD,KAAK,WAAW,MAAM,CACvB,CACD,CAKO,IAA2BQ,EAAS,CAC1C,GAAI,CAACA,EACJ,OAAOA,EAER,GAAKA,IAAqC,KACzC,MAAM,IAAI,MAAM,yCAAyC,EAG1D,OAAAC,GAAsBD,EAAG,IAAI,EACzB,KAAK,YACHD,GAAgB,0BACpB,QAAQ,KAAK,IAAI,MAAM,qHAAqH,EAAE,KAAK,EAGpJ,KAAK,WAAW,IAAIC,CAAC,EAGfA,CACR,CAMO,OAA8BA,EAAY,CAChD,GAAKA,EAGL,IAAKA,IAAqC,KACzC,MAAM,IAAI,MAAM,wCAAwC,EAEzD,KAAK,WAAW,OAAOA,CAAC,EACxBA,EAAE,QAAQ,EACX,CAKO,cAAqCA,EAAY,CAClDA,GAGD,KAAK,WAAW,IAAIA,CAAC,IACxB,KAAK,WAAW,OAAOA,CAAC,EACxBC,GAAsBD,EAAG,IAAI,EAE/B,CACD,EAlGaD,GAEL,yBAA2B,GAF5B,IAAMG,EAANH,GAyGeI,EAAf,KAAiD,CAWvD,aAAc,CAFd,KAAmB,OAAS,IAAID,EAG/BN,GAAgB,IAAI,EACpBK,GAAsB,KAAK,OAAQ,IAAI,CACxC,CAEO,SAAgB,CACtBH,GAAe,IAAI,EAEnB,KAAK,OAAO,QAAQ,CACrB,CAKU,UAAiCE,EAAS,CACnD,GAAKA,IAAgC,KACpC,MAAM,IAAI,MAAM,yCAAyC,EAE1D,OAAO,KAAK,OAAO,IAAIA,CAAC,CACzB,CACD,EA/BsBG,EAOL,KAAO,OAAO,OAAoB,CAAE,SAAU,CAAE,CAAE,CAAC,ECrdpE,IAAMC,EAAN,MAAMA,CAAQ,CAQb,YAAYC,EAAY,CACvB,KAAK,QAAUA,EACf,KAAK,KAAOD,EAAK,UACjB,KAAK,KAAOA,EAAK,SAClB,CACD,EAbMA,EAEW,UAAY,IAAIA,EAAU,MAAS,EAFpD,IAAME,GAANF,ECGA,IAAMG,GAAqB,WAAW,aAAe,OAAO,WAAW,YAAY,KAAQ,WAE9EC,GAAN,MAAMC,CAAU,CAOtB,OAAc,OAAOC,EAAqC,CACzD,OAAO,IAAID,EAAUC,CAAc,CACpC,CAEA,YAAYA,EAA0B,CACrC,KAAK,KAAOH,IAAqBG,IAAmB,GAAQ,KAAK,IAAM,WAAW,YAAa,IAAI,KAAK,WAAW,WAAW,EAC9H,KAAK,WAAa,KAAK,KAAK,EAC5B,KAAK,UAAY,EAClB,CAEO,MAAa,CACnB,KAAK,UAAY,KAAK,KAAK,CAC5B,CAEO,OAAc,CACpB,KAAK,WAAa,KAAK,KAAK,EAC5B,KAAK,UAAY,EAClB,CAEO,SAAkB,CACxB,OAAI,KAAK,YAAc,GACf,KAAK,UAAY,KAAK,WAEvB,KAAK,KAAK,EAAI,KAAK,UAC3B,CACD,ECxBA,IAAMC,GAA6B,GAO7BC,GAAoC,GASpCC,GAAsC,GAW3BC,OAAV,CACOA,EAAA,KAAmB,IAAMC,EAAW,KAEjD,SAASC,EAAsBC,EAAyB,CACvD,GAAIJ,GAAqC,CACxC,GAAM,CAAE,iBAAkBK,CAAmB,EAAID,EAC3CE,EAAQC,EAAW,OAAO,EAC5BC,EAAQ,EACZJ,EAAQ,iBAAmB,IAAM,CAC5B,EAAEI,IAAU,IACf,QAAQ,KAAK,4GAA4G,EACzHF,EAAM,MAAM,GAEbD,IAAqB,CACtB,CACD,CACD,CAkBO,SAASI,EAAMC,EAAuBC,EAA2C,CACvF,OAAOC,EAAwBF,EAAO,IAAG,GAAW,EAAG,OAAW,GAAM,OAAWC,CAAU,CAC9F,CAFOV,EAAS,MAAAQ,EAST,SAASI,EAAQH,EAA2B,CAClD,MAAO,CAACI,EAAUC,EAAW,KAAMC,IAAiB,CAEnD,IAAIC,EAAU,GACVC,EACJ,OAAAA,EAASR,EAAMS,GAAK,CACnB,GAAI,CAAAF,EAEG,OAAIC,EACVA,EAAO,QAAQ,EAEfD,EAAU,GAGJH,EAAS,KAAKC,EAAUI,CAAC,CACjC,EAAG,KAAMH,CAAW,EAEhBC,GACHC,EAAO,QAAQ,EAGTA,CACR,CACD,CAvBOjB,EAAS,KAAAY,EAqCT,SAASO,EAAUV,EAAiBU,EAAkBT,EAAwC,CACpG,OAAOU,EAAS,CAACP,EAAUC,EAAW,KAAMC,IAAiBN,EAAMY,GAAKR,EAAS,KAAKC,EAAUK,EAAIE,CAAC,CAAC,EAAG,KAAMN,CAAW,EAAGL,CAAU,CACxI,CAFOV,EAAS,IAAAmB,EAeT,SAASG,EAAWb,EAAiBc,EAAsBb,EAAwC,CACzG,OAAOU,EAAS,CAACP,EAAUC,EAAW,KAAMC,IAAiBN,EAAMY,GAAK,CAAEE,EAAKF,CAAC,EAAGR,EAAS,KAAKC,EAAUO,CAAC,CAAG,EAAG,KAAMN,CAAW,EAAGL,CAAU,CACjJ,CAFOV,EAAS,QAAAsB,EAmBT,SAASE,EAAUf,EAAiBe,EAA2Bd,EAAwC,CAC7G,OAAOU,EAAS,CAACP,EAAUC,EAAW,KAAMC,IAAiBN,EAAMS,GAAKM,EAAON,CAAC,GAAKL,EAAS,KAAKC,EAAUI,CAAC,EAAG,KAAMH,CAAW,EAAGL,CAAU,CAChJ,CAFOV,EAAS,OAAAwB,EAOT,SAASC,EAAUhB,EAA8B,CACvD,OAAOA,CACR,CAFOT,EAAS,OAAAyB,EAST,SAASC,KAAUC,EAA8B,CACvD,MAAO,CAACd,EAAUC,EAAW,KAAMC,IAAiB,CACnD,IAAML,EAAakB,GAAmB,GAAGD,EAAO,IAAIlB,GAASA,EAAMS,GAAKL,EAAS,KAAKC,EAAUI,CAAC,CAAC,CAAC,CAAC,EACpG,OAAOW,EAAuBnB,EAAYK,CAAW,CACtD,CACD,CALOf,EAAS,IAAA0B,EAYT,SAASI,EAAarB,EAAiBsB,EAA6CC,EAAatB,EAAwC,CAC/I,IAAIuB,EAAwBD,EAE5B,OAAOb,EAAUV,EAAOS,IACvBe,EAASF,EAAME,EAAQf,CAAC,EACjBe,GACLvB,CAAU,CACd,CAPOV,EAAS,OAAA8B,EAShB,SAASV,EAAYX,EAAiBC,EAAmD,CACxF,IAAIG,EAEEV,EAAsC,CAC3C,wBAAyB,CACxBU,EAAWJ,EAAMyB,EAAQ,KAAMA,CAAO,CACvC,EACA,yBAA0B,CACzBrB,GAAU,QAAQ,CACnB,CACD,EAEKH,GACJR,EAAsBC,CAAO,EAG9B,IAAM+B,EAAU,IAAIC,EAAWhC,CAAO,EAEtC,OAAAO,GAAY,IAAIwB,CAAO,EAEhBA,EAAQ,KAChB,CAMA,SAASL,EAA8CO,EAAMC,EAAuD,CACnH,OAAIA,aAAiB,MACpBA,EAAM,KAAKD,CAAC,EACFC,GACVA,EAAM,IAAID,CAAC,EAELA,CACR,CAsBO,SAASzB,EAAeF,EAAiBsB,EAA6CO,EAAwC,IAAKC,EAAU,GAAOC,EAAwB,GAAOC,EAA+B/B,EAAwC,CAChQ,IAAIgC,EACAT,EACAU,EACAC,EAAoB,EACpBC,EAEE1C,GAAsC,CAC3C,qBAAAsC,EACA,wBAAyB,CACxBC,EAAejC,EAAMqC,IAAO,CAC3BF,IACAX,EAASF,EAAME,EAAQa,EAAG,EAEtBP,GAAW,CAACI,IACfT,EAAQ,KAAKD,CAAM,EACnBA,EAAS,QAGVY,EAAS,IAAM,CACd,IAAME,GAAUd,EAChBA,EAAS,OACTU,EAAS,QACL,CAACJ,GAAWK,EAAoB,IACnCV,EAAQ,KAAKa,EAAQ,EAEtBH,EAAoB,CACrB,EAEI,OAAON,GAAU,UACpB,aAAaK,CAAM,EACnBA,EAAS,WAAWE,EAAQP,CAAK,GAE7BK,IAAW,SACdA,EAAS,EACT,eAAeE,CAAM,EAGxB,CAAC,CACF,EACA,sBAAuB,CAClBL,GAAyBI,EAAoB,GAChDC,IAAS,CAEX,EACA,yBAA0B,CACzBA,EAAS,OACTH,EAAa,QAAQ,CACtB,CACD,EAEKhC,GACJR,EAAsBC,EAAO,EAG9B,IAAM+B,EAAU,IAAIC,EAAWhC,EAAO,EAEtC,OAAAO,GAAY,IAAIwB,CAAO,EAEhBA,EAAQ,KAChB,CA5DOlC,EAAS,SAAAW,EAqET,SAASqC,EAAcvC,EAAiB6B,EAAgB,EAAG5B,EAA0C,CAC3G,OAAOV,EAAM,SAAiBS,EAAO,CAACwC,EAAM/B,IACtC+B,GAGLA,EAAK,KAAK/B,CAAC,EACJ+B,GAHC,CAAC/B,CAAC,EAIRoB,EAAO,OAAW,GAAM,OAAW5B,CAAU,CACjD,CAROV,EAAS,WAAAgD,EA4BT,SAASE,GAASzC,EAAiB0C,EAAkC,CAACC,EAAGC,IAAMD,IAAMC,EAAG3C,EAAwC,CACtI,IAAI4C,EAAY,GACZC,EAEJ,OAAO/B,EAAOf,EAAO+C,GAAS,CAC7B,IAAMC,EAAaH,GAAa,CAACH,EAAOK,EAAOD,CAAK,EACpD,OAAAD,EAAY,GACZC,EAAQC,EACDC,CACR,EAAG/C,CAAU,CACd,CAVOV,EAAS,MAAAkD,GA6BT,SAASQ,EAAYjD,EAAqBkD,EAA2BjD,EAAoD,CAC/H,MAAO,CACNV,EAAM,OAAOS,EAAOkD,EAAKjD,CAAU,EACnCV,EAAM,OAAOS,EAAOS,GAAK,CAACyC,EAAIzC,CAAC,EAAGR,CAAU,CAC7C,CACD,CALOV,EAAS,MAAA0D,EA2BT,SAASE,EAAUnD,EAAiBoD,EAAoB,GAAOC,EAAe,CAAC,EAAGpD,EAAwC,CAChI,IAAIkD,EAAqBE,EAAQ,MAAM,EAEnCjD,EAA+BJ,EAAMS,GAAK,CACzC0C,EACHA,EAAO,KAAK1C,CAAC,EAEbgB,EAAQ,KAAKhB,CAAC,CAEhB,CAAC,EAEGR,GACHA,EAAW,IAAIG,CAAQ,EAGxB,IAAMkD,EAAQ,IAAM,CACnBH,GAAQ,QAAQ1C,GAAKgB,EAAQ,KAAKhB,CAAC,CAAC,EACpC0C,EAAS,IACV,EAEM1B,EAAU,IAAIC,EAAW,CAC9B,wBAAyB,CACnBtB,IACJA,EAAWJ,EAAMS,GAAKgB,EAAQ,KAAKhB,CAAC,CAAC,EACjCR,GACHA,EAAW,IAAIG,CAAQ,EAG1B,EAEA,uBAAwB,CACnB+C,IACCC,EACH,WAAWE,CAAK,EAEhBA,EAAM,EAGT,EAEA,yBAA0B,CACrBlD,GACHA,EAAS,QAAQ,EAElBA,EAAW,IACZ,CACD,CAAC,EAED,OAAIH,GACHA,EAAW,IAAIwB,CAAO,EAGhBA,EAAQ,KAChB,CArDOlC,EAAS,OAAA4D,EAwET,SAASI,GAAYvD,EAAiBwD,EAA6E,CAWzH,MAVqB,CAACpD,EAAUC,EAAUC,IAAgB,CACzD,IAAMmD,EAAKD,EAAW,IAAIE,EAAoB,EAC9C,OAAO1D,EAAM,SAAU+C,EAAO,CAC7B,IAAMvC,EAASiD,EAAG,SAASV,CAAK,EAC5BvC,IAAWmD,GACdvD,EAAS,KAAKC,EAAUG,CAAM,CAEhC,EAAG,OAAWF,CAAW,CAC1B,CAGD,CAZOf,EAAS,MAAAgE,GAchB,IAAMI,EAAgB,OAAO,eAAe,EAE5C,MAAMD,EAAuD,CAA7D,cACC,KAAiB,MAAiC,CAAC,EAEnD,IAAOE,EAAyB,CAC/B,YAAK,MAAM,KAAKA,CAAE,EACX,IACR,CAEA,QAAQA,EAA4B,CACnC,YAAK,MAAM,KAAKC,IACfD,EAAGC,CAAC,EACGA,EACP,EACM,IACR,CAEA,OAAOD,EAA+B,CACrC,YAAK,MAAM,KAAKC,GAAKD,EAAGC,CAAC,EAAIA,EAAIF,CAAa,EACvC,IACR,CAEA,OAAUrC,EAA+CC,EAA+B,CACvF,IAAIiB,EAAOjB,EACX,YAAK,MAAM,KAAKsC,IACfrB,EAAOlB,EAAMkB,EAAMqB,CAAC,EACbrB,EACP,EACM,IACR,CAEA,MAAME,EAAsC,CAAC,EAAGE,IAAM,IAAMA,EAAuB,CAClF,IAAIC,EAAY,GACZC,EACJ,YAAK,MAAM,KAAKC,GAAS,CACxB,IAAMC,EAAaH,GAAa,CAACH,EAAOK,EAAOD,CAAK,EACpD,OAAAD,EAAY,GACZC,EAAQC,EACDC,EAAaD,EAAQY,CAC7B,CAAC,EAEM,IACR,CAEO,SAASZ,EAAY,CAC3B,QAAWe,KAAQ,KAAK,MAEvB,GADAf,EAAQe,EAAKf,CAAK,EACdA,IAAUY,EACb,MAIF,OAAOZ,CACR,CACD,CAoBO,SAASgB,EAAwBtC,EAA2BuC,EAAmBtD,EAA6BuD,GAAMA,EAAc,CACtI,IAAML,EAAK,IAAIM,IAAgB1D,EAAO,KAAKE,EAAI,GAAGwD,CAAI,CAAC,EACjDC,EAAqB,IAAM1C,EAAQ,GAAGuC,EAAWJ,CAAE,EACnDQ,EAAuB,IAAM3C,EAAQ,eAAeuC,EAAWJ,CAAE,EACjEpD,EAAS,IAAIkB,EAAW,CAAE,uBAAwByC,EAAoB,wBAAyBC,CAAqB,CAAC,EAE3H,OAAO5D,EAAO,KACf,CAPOjB,EAAS,qBAAAwE,EAiBT,SAASM,EAAuB5C,EAA0BuC,EAAmBtD,EAA6BuD,GAAMA,EAAc,CACpI,IAAML,EAAK,IAAIM,IAAgB1D,EAAO,KAAKE,EAAI,GAAGwD,CAAI,CAAC,EACjDC,EAAqB,IAAM1C,EAAQ,iBAAiBuC,EAAWJ,CAAE,EACjEQ,EAAuB,IAAM3C,EAAQ,oBAAoBuC,EAAWJ,CAAE,EACtEpD,EAAS,IAAIkB,EAAW,CAAE,uBAAwByC,EAAoB,wBAAyBC,CAAqB,CAAC,EAE3H,OAAO5D,EAAO,KACf,CAPOjB,EAAS,oBAAA8E,EAYT,SAASC,EAAatE,EAA6B,CACzD,OAAO,IAAI,QAAQuE,GAAWpE,EAAKH,CAAK,EAAEuE,CAAO,CAAC,CACnD,CAFOhF,EAAS,UAAA+E,EAQT,SAASE,EAAeC,EAA2C,CACzE,IAAMjE,EAAS,IAAIkB,EAEnB,OAAA+C,EAAQ,KAAKC,GAAO,CACnBlE,EAAO,KAAKkE,CAAG,CAChB,EAAG,IAAM,CACRlE,EAAO,KAAK,MAAS,CACtB,CAAC,EAAE,QAAQ,IAAM,CAChBA,EAAO,QAAQ,CAChB,CAAC,EAEMA,EAAO,KACf,CAZOjB,EAAS,YAAAiF,EA0BT,SAASG,EAAWC,EAAgBC,EAA6B,CACvE,OAAOD,EAAKnE,GAAKoE,EAAG,KAAKpE,CAAC,CAAC,CAC5B,CAFOlB,EAAS,QAAAoF,EAeT,SAASG,GAAmB9E,EAAiB+E,EAAoCxD,EAA0B,CACjH,OAAAwD,EAAQxD,CAAO,EACRvB,EAAMS,GAAKsE,EAAQtE,CAAC,CAAC,CAC7B,CAHOlB,EAAS,gBAAAuF,GAKhB,MAAME,EAAwC,CAO7C,YAAqBC,EAAkCrD,EAAoC,CAAtE,iBAAAqD,EAHrB,KAAQ,SAAW,EACnB,KAAQ,YAAc,GAGrB,IAAMvF,EAA0B,CAC/B,uBAAwB,IAAM,CAC7BuF,EAAY,YAAY,IAAI,CAC7B,EACA,wBAAyB,IAAM,CAC9BA,EAAY,eAAe,IAAI,CAChC,CACD,EACKrD,GACJnC,EAAsBC,CAAO,EAE9B,KAAK,QAAU,IAAIgC,EAAWhC,CAAO,EACjCkC,GACHA,EAAM,IAAI,KAAK,OAAO,CAExB,CAEA,YAAeqD,EAAyC,CAEvD,KAAK,UACN,CAEA,qBAAwBA,EAA4C,CAEpE,CAEA,aAAyBA,EAAsCC,EAAwB,CAEtF,KAAK,YAAc,EACpB,CAEA,UAAaD,EAAyC,CAErD,KAAK,WACD,KAAK,WAAa,IACrB,KAAK,YAAY,cAAc,EAC3B,KAAK,cACR,KAAK,YAAc,GACnB,KAAK,QAAQ,KAAK,KAAK,YAAY,IAAI,CAAC,GAG3C,CACD,CAMO,SAASE,GAAkBC,EAA0BxD,EAAmC,CAE9F,OADiB,IAAIoD,GAAgBI,EAAKxD,CAAK,EAC/B,QAAQ,KACzB,CAHOrC,EAAS,eAAA4F,GAQT,SAASE,GAAoBC,EAA2C,CAC9E,MAAO,CAAClF,EAAUC,EAAUC,IAAgB,CAC3C,IAAIR,EAAQ,EACRyF,EAAY,GACVC,EAAsB,CAC3B,aAAc,CACb1F,GACD,EACA,WAAY,CACXA,IACIA,IAAU,IACbwF,EAAW,cAAc,EACrBC,IACHA,EAAY,GACZnF,EAAS,KAAKC,CAAQ,GAGzB,EACA,sBAAuB,CAEvB,EACA,cAAe,CACdkF,EAAY,EACb,CACD,EACAD,EAAW,YAAYE,CAAQ,EAC/BF,EAAW,cAAc,EACzB,IAAMrF,EAAa,CAClB,SAAU,CACTqF,EAAW,eAAeE,CAAQ,CACnC,CACD,EAEA,OAAIlF,aAAuBmF,EAC1BnF,EAAY,IAAIL,CAAU,EAChB,MAAM,QAAQK,CAAW,GACnCA,EAAY,KAAKL,CAAU,EAGrBA,CACR,CACD,CAzCOV,EAAS,oBAAA8F,KA5pBA9F,KAAA,IAovBV,IAAMmG,EAAN,MAAMA,CAAe,CAc3B,YAAYC,EAAc,CAP1B,KAAO,cAAwB,EAC/B,KAAO,gBAAkB,EACzB,KAAO,eAAiB,EACxB,KAAO,UAAsB,CAAC,EAK7B,KAAK,KAAO,GAAGA,CAAI,IAAID,EAAe,SAAS,GAC/CA,EAAe,IAAI,IAAI,IAAI,CAC5B,CAEA,MAAME,EAA6B,CAClC,KAAK,WAAa,IAAIC,GACtB,KAAK,cAAgBD,CACtB,CAEA,MAAa,CACZ,GAAI,KAAK,WAAY,CACpB,IAAME,EAAU,KAAK,WAAW,QAAQ,EACxC,KAAK,UAAU,KAAKA,CAAO,EAC3B,KAAK,gBAAkBA,EACvB,KAAK,iBAAmB,EACxB,KAAK,WAAa,MACnB,CACD,CACD,EAjCaJ,EAEI,IAAM,IAAI,IAFdA,EAIG,QAAU,EAJnB,IAAMK,GAANL,EAmCHM,GAA8B,GAWlC,IAAMC,GAAN,MAAMA,EAAe,CAOpB,YACkBC,EACRC,EACAC,GAAgBH,GAAe,WAAW,SAAS,EAAE,EAAE,SAAS,EAAG,GAAG,EAC9E,CAHgB,mBAAAC,EACR,eAAAC,EACA,UAAAC,EALV,KAAQ,eAAyB,CAM7B,CAEJ,SAAgB,CACf,KAAK,SAAS,MAAM,CACrB,CAEA,MAAMC,EAAmBC,EAAiD,CAEzE,IAAMH,EAAY,KAAK,UACvB,GAAIA,GAAa,GAAKG,EAAgBH,EACrC,OAGI,KAAK,UACT,KAAK,QAAU,IAAI,KAEpB,IAAMI,EAAS,KAAK,QAAQ,IAAIF,EAAM,KAAK,GAAK,EAIhD,GAHA,KAAK,QAAQ,IAAIA,EAAM,MAAOE,EAAQ,CAAC,EACvC,KAAK,gBAAkB,EAEnB,KAAK,gBAAkB,EAAG,CAG7B,KAAK,eAAiBJ,EAAY,GAElC,GAAM,CAACK,EAAUC,CAAQ,EAAI,KAAK,qBAAqB,EACjDC,EAAU,IAAI,KAAK,IAAI,8CAA8CJ,CAAa,+CAA+CG,CAAQ,KAC/I,QAAQ,KAAKC,CAAO,EACpB,QAAQ,KAAKF,CAAS,EAEtB,IAAMG,EAAQ,IAAIC,GAAkBF,EAASF,CAAQ,EACrD,KAAK,cAAcG,CAAK,CACzB,CAEA,MAAO,IAAM,CACZ,IAAMJ,EAAS,KAAK,QAAS,IAAIF,EAAM,KAAK,GAAK,EACjD,KAAK,QAAS,IAAIA,EAAM,MAAOE,EAAQ,CAAC,CACzC,CACD,CAEA,sBAAqD,CACpD,GAAI,CAAC,KAAK,QACT,OAED,IAAIC,EACAC,EAAmB,EACvB,OAAW,CAACJ,EAAOE,CAAK,IAAK,KAAK,SAC7B,CAACC,GAAYC,EAAWF,KAC3BC,EAAW,CAACH,EAAOE,CAAK,EACxBE,EAAWF,GAGb,OAAOC,CACR,CACD,EAjEMP,GAEU,QAAU,EAF1B,IAAMY,GAANZ,GAmEMa,EAAN,MAAMC,CAAW,CAOR,YAAqBC,EAAe,CAAf,WAAAA,CAAiB,CAL9C,OAAO,QAAS,CACf,IAAMC,EAAM,IAAI,MAChB,OAAO,IAAIF,EAAWE,EAAI,OAAS,EAAE,CACtC,CAIA,OAAQ,CACP,QAAQ,KAAK,KAAK,MAAM,MAAM;AAAA,CAAI,EAAE,MAAM,CAAC,EAAE,KAAK;AAAA,CAAI,CAAC,CACxD,CACD,EAGaL,GAAN,cAAgC,KAAM,CAC5C,YAAYF,EAAiBL,EAAe,CAC3C,MAAMK,CAAO,EACb,KAAK,KAAO,oBACZ,KAAK,MAAQL,CACd,CACD,EAIaa,GAAN,cAAmC,KAAM,CAC/C,YAAYR,EAAiBL,EAAe,CAC3C,MAAMK,CAAO,EACb,KAAK,KAAO,uBACZ,KAAK,MAAQL,CACd,CACD,EAEIc,GAAK,EACHC,EAAN,KAAyB,CAGxB,YAA4BJ,EAAU,CAAV,WAAAA,EAD5B,KAAO,GAAKG,IAC4B,CACzC,EACME,GAAsB,EAKtBC,GAAkB,CAAIC,EAAmCC,IAA0C,CACxG,GAAID,aAAqBH,EACxBI,EAAGD,CAAS,MAEZ,SAASE,EAAI,EAAGA,EAAIF,EAAU,OAAQE,IAAK,CAC1C,IAAMC,EAAIH,EAAUE,CAAC,EACjBC,GACHF,EAAGE,CAAC,CAEN,CAEF,EAGIC,GAEJ,GAAIC,GAA4B,CAC/B,IAAMC,EAAkB,CAAC,EAEzB,YAAY,IAAM,CACbA,EAAM,SAAW,IAGrB,QAAQ,KAAK,uEAAwE,EACrF,QAAQ,KAAKA,EAAM,KAAK;AAAA,CAAI,CAAC,EAC7BA,EAAM,OAAS,EAChB,EAAG,GAAI,EAEPF,GAAsB,IAAI,qBAAqBG,GAAa,CACvD,OAAOA,GAAc,UACxBD,EAAM,KAAKC,CAAS,CAEtB,CAAC,CACF,CAuBO,IAAMC,EAAN,KAAiB,CAmCvB,YAAYC,EAA0B,CAFtC,KAAU,MAAQ,EAGjB,KAAK,SAAWA,EAChB,KAAK,YAAeC,GAA8B,GAAK,KAAK,UAAU,qBACnE,IAAIpB,GAAemB,GAAS,iBAAmBE,EAAmB,KAAK,UAAU,sBAAwBD,EAA2B,EACtI,OACD,KAAK,SAAW,KAAK,UAAU,UAAY,IAAIE,GAAe,KAAK,SAAS,SAAS,EAAI,OACzF,KAAK,eAAiB,KAAK,UAAU,aACtC,CAEA,SAAU,CACT,GAAI,CAAC,KAAK,UAAW,CAgBpB,GAfA,KAAK,UAAY,GAYb,KAAK,gBAAgB,UAAY,MACpC,KAAK,eAAe,MAAM,EAEvB,KAAK,WAAY,CACpB,GAAIC,GAAmC,CACtC,IAAMb,EAAY,KAAK,WACvB,eAAe,IAAM,CACpBD,GAAgBC,EAAWG,GAAKA,EAAE,OAAO,MAAM,CAAC,CACjD,CAAC,CACF,CAEA,KAAK,WAAa,OAClB,KAAK,MAAQ,CACd,CACA,KAAK,UAAU,0BAA0B,EACzC,KAAK,aAAa,QAAQ,CAC3B,CACD,CAMA,IAAI,OAAkB,CACrB,YAAK,SAAW,CAACW,EAAyBC,EAAgBC,IAAkD,CAC3G,GAAI,KAAK,aAAe,KAAK,MAAQ,KAAK,YAAY,WAAa,EAAG,CACrE,IAAM7B,EAAU,IAAI,KAAK,YAAY,IAAI,+EAA+E,KAAK,KAAK,OAAO,KAAK,YAAY,SAAS,IACnK,QAAQ,KAAKA,CAAO,EAEpB,IAAM8B,EAAQ,KAAK,YAAY,qBAAqB,GAAK,CAAC,gBAAiB,EAAE,EACvE7B,EAAQ,IAAIO,GAAqB,GAAGR,CAAO,+CAA+C8B,EAAM,CAAC,CAAC,UAAWA,EAAM,CAAC,CAAC,EAE3H,OADqB,KAAK,UAAU,iBAAmBN,GAC1CvB,CAAK,EAEX8B,EAAW,IACnB,CAEA,GAAI,KAAK,UAER,OAAOA,EAAW,KAGfH,IACHD,EAAWA,EAAS,KAAKC,CAAQ,GAGlC,IAAMI,EAAY,IAAItB,EAAgBiB,CAAQ,EAE1CM,EACAtC,EACA,KAAK,aAAe,KAAK,OAAS,KAAK,KAAK,KAAK,YAAY,UAAY,EAAG,IAE/EqC,EAAU,MAAQ5B,EAAW,OAAO,EACpC6B,EAAgB,KAAK,YAAY,MAAMD,EAAU,MAAO,KAAK,MAAQ,CAAC,GAGnEN,KACHM,EAAU,MAAQrC,GAASS,EAAW,OAAO,GAGzC,KAAK,WAIC,KAAK,sBAAsBM,GACrC,KAAK,iBAAmB,IAAIwB,GAC5B,KAAK,WAAa,CAAC,KAAK,WAAYF,CAAS,GAE7C,KAAK,WAAW,KAAKA,CAAS,GAP9B,KAAK,UAAU,yBAAyB,IAAI,EAC5C,KAAK,WAAaA,EAClB,KAAK,UAAU,wBAAwB,IAAI,GAQ5C,KAAK,QAGL,IAAMG,EAASC,GAAa,IAAM,CACjCnB,IAAqB,WAAWkB,CAAM,EACtCF,IAAgB,EAChB,KAAK,gBAAgBD,CAAS,CAC/B,CAAC,EAOD,GANIH,aAAuBQ,EAC1BR,EAAY,IAAIM,CAAM,EACZ,MAAM,QAAQN,CAAW,GACnCA,EAAY,KAAKM,CAAM,EAGpBlB,GAAqB,CACxB,IAAMtB,EAAQ,IAAI,MAAM,EAAE,MAAO,MAAM;AAAA,CAAI,EAAE,MAAM,EAAG,CAAC,EAAE,KAAK;AAAA,CAAI,EAAE,KAAK,EACnE2C,EAAQ,uDAAuD,KAAK3C,CAAK,EAC/EsB,GAAoB,SAASkB,EAAQG,IAAQ,CAAC,GAAK3C,EAAOwC,CAAM,CACjE,CAEA,OAAOA,CACR,EAEO,KAAK,MACb,CAEQ,gBAAgBI,EAAgC,CAGvD,GAFA,KAAK,UAAU,uBAAuB,IAAI,EAEtC,CAAC,KAAK,WACT,OAGD,GAAI,KAAK,QAAU,EAAG,CACrB,KAAK,WAAa,OAClB,KAAK,UAAU,0BAA0B,IAAI,EAC7C,KAAK,MAAQ,EACb,MACD,CAGA,IAAM1B,EAAY,KAAK,WAEjB2B,EAAQ3B,EAAU,QAAQ0B,CAAQ,EACxC,GAAIC,IAAU,GACb,cAAQ,IAAI,YAAa,KAAK,SAAS,EACvC,QAAQ,IAAI,QAAS,KAAK,KAAK,EAC/B,QAAQ,IAAI,OAAQ,KAAK,UAAU,KAAK,UAAU,CAAC,EAC7C,IAAI,MAAM,uCAAuC,EAGxD,KAAK,QACL3B,EAAU2B,CAAK,EAAI,OAEnB,IAAMC,EAAsB,KAAK,eAAgB,UAAY,KAC7D,GAAI,KAAK,MAAQ9B,IAAuBE,EAAU,OAAQ,CACzD,IAAI6B,EAAI,EACR,QAAS3B,EAAI,EAAGA,EAAIF,EAAU,OAAQE,IACjCF,EAAUE,CAAC,EACdF,EAAU6B,GAAG,EAAI7B,EAAUE,CAAC,EAClB0B,IACV,KAAK,eAAgB,MACjBC,EAAI,KAAK,eAAgB,GAC5B,KAAK,eAAgB,KAIxB7B,EAAU,OAAS6B,CACpB,CACD,CAEQ,SAASH,EAA2DjC,EAAU,CACrF,GAAI,CAACiC,EACJ,OAGD,IAAMI,EAAe,KAAK,UAAU,iBAAmBnB,EACvD,GAAI,CAACmB,EAAc,CAClBJ,EAAS,MAAMjC,CAAK,EACpB,MACD,CAEA,GAAI,CACHiC,EAAS,MAAMjC,CAAK,CACrB,OAASsC,EAAG,CACXD,EAAaC,CAAC,CACf,CACD,CAGQ,cAAcC,EAA+B,CACpD,IAAMhC,EAAYgC,EAAG,QAAS,WAC9B,KAAOA,EAAG,EAAIA,EAAG,KAEhB,KAAK,SAAShC,EAAUgC,EAAG,GAAG,EAAGA,EAAG,KAAU,EAE/CA,EAAG,MAAM,CACV,CAMA,KAAKC,EAAgB,CAQpB,GAPI,KAAK,gBAAgB,UACxB,KAAK,cAAc,KAAK,cAAc,EACtC,KAAK,UAAU,KAAK,GAGrB,KAAK,UAAU,MAAM,KAAK,KAAK,EAE1B,KAAK,WAEH,GAAI,KAAK,sBAAsBpC,EACrC,KAAK,SAAS,KAAK,WAAYoC,CAAK,MAC9B,CACN,IAAMD,EAAK,KAAK,eAChBA,EAAG,QAAQ,KAAMC,EAAO,KAAK,WAAW,MAAM,EAC9C,KAAK,cAAcD,CAAE,CACtB,CAEA,KAAK,UAAU,KAAK,CACrB,CAEA,cAAwB,CACvB,OAAO,KAAK,MAAQ,CACrB,CACD,EAQA,IAAME,GAAN,KAA8D,CAA9D,cAMC,KAAO,EAAI,GAKX,KAAO,IAAM,EAWN,QAAWC,EAAqBC,EAAUC,EAAa,CAC7D,KAAK,EAAI,EACT,KAAK,IAAMA,EACX,KAAK,QAAUF,EACf,KAAK,MAAQC,CACd,CAEO,OAAQ,CACd,KAAK,EAAI,KAAK,IACd,KAAK,QAAU,OACf,KAAK,MAAQ,MACd,CACD,ECvxCO,IAAME,EAAN,MAAMC,CAA0C,CAuBrD,aAAc,CApBd,KAAQ,WAAuD,OAAO,OAAO,IAAI,EACjF,KAAQ,QAAkB,GAG1B,KAAiB,UAAY,IAAIC,EACjC,KAAgB,SAAW,KAAK,UAAU,MAgBxC,IAAMC,EAAkB,IAAIC,EAC5B,KAAK,SAASD,CAAe,EAC7B,KAAK,QAAUA,EAAgB,QAC/B,KAAK,gBAAkBA,CACzB,CAlBA,OAAc,kBAAkBE,EAAuC,CACrE,OAAQA,EAAQ,KAAO,CACzB,CACA,OAAc,aAAaA,EAAgD,CACzE,OAASA,GAAS,EAAK,CACzB,CACA,OAAc,gBAAgBA,EAAsC,CAClE,OAAOA,GAAS,CAClB,CACA,OAAc,oBAAoBC,EAAeC,EAAeC,EAAsB,GAA8B,CAClH,OAASF,EAAQ,WAAa,GAAOC,EAAQ,IAAM,GAAMC,EAAW,EAAE,EACxE,CASO,SAAgB,CACrB,KAAK,UAAU,QAAQ,CACzB,CAEA,IAAW,UAAqB,CAC9B,OAAO,OAAO,KAAK,KAAK,UAAU,CACpC,CAEA,IAAW,eAAwB,CACjC,OAAO,KAAK,OACd,CAEA,IAAW,cAAcC,EAAiB,CACxC,GAAI,CAAC,KAAK,WAAWA,CAAO,EAC1B,MAAM,IAAI,MAAM,4BAA4BA,CAAO,GAAG,EAExD,KAAK,QAAUA,EACf,KAAK,gBAAkB,KAAK,WAAWA,CAAO,EAC9C,KAAK,UAAU,KAAKA,CAAO,CAC7B,CAEO,SAASC,EAAyC,CACvD,KAAK,WAAWA,EAAS,OAAO,EAAIA,CACtC,CAKO,QAAQC,EAA+B,CAC5C,OAAO,KAAK,gBAAgB,QAAQA,CAAG,CACzC,CAEO,mBAAmBC,EAAmB,CAC3C,IAAIC,EAAS,EACTC,EAAgB,EACdC,EAASH,EAAE,OACjB,QAASI,EAAI,EAAGA,EAAID,EAAQ,EAAEC,EAAG,CAC/B,IAAIC,EAAOL,EAAE,WAAWI,CAAC,EAEzB,GAAI,OAAUC,GAAQA,GAAQ,MAAQ,CACpC,GAAI,EAAED,GAAKD,EAMT,OAAOF,EAAS,KAAK,QAAQI,CAAI,EAEnC,IAAMC,EAASN,EAAE,WAAWI,CAAC,EAGzB,OAAUE,GAAUA,GAAU,MAChCD,GAAQA,EAAO,OAAU,KAAQC,EAAS,MAAS,MAEnDL,GAAU,KAAK,QAAQK,CAAM,CAEjC,CACA,IAAMC,EAAc,KAAK,eAAeF,EAAMH,CAAa,EACvDM,EAAUnB,EAAe,aAAakB,CAAW,EACjDlB,EAAe,kBAAkBkB,CAAW,IAC9CC,GAAWnB,EAAe,aAAaa,CAAa,GAEtDD,GAAUO,EACVN,EAAgBK,CAClB,CACA,OAAON,CACT,CAEO,eAAeQ,EAAmBC,EAAyD,CAChG,OAAO,KAAK,gBAAgB,eAAeD,EAAWC,CAAS,CACjE,CACF,ECrGA,IAAMC,GAAgB,CACpB,CAAC,IAAQ,GAAM,EAAG,CAAC,KAAQ,IAAM,EAAG,CAAC,KAAQ,IAAM,EACnD,CAAC,KAAQ,IAAM,EAAG,CAAC,KAAQ,IAAM,EAAG,CAAC,KAAQ,IAAM,EACnD,CAAC,KAAQ,IAAM,EAAG,CAAC,KAAQ,IAAM,EAAG,CAAC,KAAQ,IAAM,EACnD,CAAC,KAAQ,IAAM,EAAG,CAAC,KAAQ,IAAM,EAAG,CAAC,KAAQ,IAAM,EACnD,CAAC,KAAQ,IAAM,EAAG,CAAC,KAAQ,IAAM,EAAG,CAAC,KAAQ,IAAM,EACnD,CAAC,KAAQ,IAAM,EAAG,CAAC,KAAQ,IAAM,EAAG,CAAC,KAAQ,IAAM,EACnD,CAAC,KAAQ,IAAM,EAAG,CAAC,KAAQ,IAAM,EAAG,CAAC,KAAQ,IAAM,EACnD,CAAC,KAAQ,IAAM,EAAG,CAAC,KAAQ,IAAM,EAAG,CAAC,KAAQ,IAAM,EACnD,CAAC,KAAQ,IAAM,EAAG,CAAC,KAAQ,IAAM,EAAG,CAAC,KAAQ,IAAM,EACnD,CAAC,KAAQ,IAAM,EAAG,CAAC,KAAQ,IAAM,EAAG,CAAC,KAAQ,IAAM,EACnD,CAAC,KAAQ,IAAM,EAAG,CAAC,KAAQ,IAAM,EAAG,CAAC,KAAQ,IAAM,EACnD,CAAC,KAAQ,IAAM,EAAG,CAAC,KAAQ,IAAM,EAAG,CAAC,KAAQ,IAAM,EACnD,CAAC,KAAQ,IAAM,EAAG,CAAC,KAAQ,IAAM,EAAG,CAAC,KAAQ,IAAM,EACnD,CAAC,KAAQ,IAAM,EAAG,CAAC,KAAQ,IAAM,EAAG,CAAC,KAAQ,IAAM,EACnD,CAAC,KAAQ,IAAM,EAAG,CAAC,KAAQ,IAAM,EAAG,CAAC,KAAQ,IAAM,EACnD,CAAC,KAAQ,IAAM,EAAG,CAAC,KAAQ,IAAM,EAAG,CAAC,KAAQ,IAAM,EACnD,CAAC,KAAQ,IAAM,EAAG,CAAC,KAAQ,IAAM,EAAG,CAAC,KAAQ,IAAM,EACnD,CAAC,KAAQ,IAAM,EAAG,CAAC,KAAQ,IAAM,EAAG,CAAC,KAAQ,IAAM,EACnD,CAAC,KAAQ,IAAM,EAAG,CAAC,KAAQ,IAAM,EAAG,CAAC,KAAQ,IAAM,EACnD,CAAC,KAAQ,IAAM,EAAG,CAAC,KAAQ,IAAM,EAAG,CAAC,KAAQ,IAAM,EACnD,CAAC,KAAQ,IAAM,EAAG,CAAC,KAAQ,IAAM,EAAG,CAAC,KAAQ,IAAM,EACnD,CAAC,KAAQ,IAAM,EAAG,CAAC,KAAQ,IAAM,EAAG,CAAC,KAAQ,IAAM,EACnD,CAAC,KAAQ,IAAM,EAAG,CAAC,KAAQ,IAAM,EAAG,CAAC,KAAQ,IAAM,EACnD,CAAC,KAAQ,IAAM,EAAG,CAAC,KAAQ,IAAM,EAAG,CAAC,KAAQ,IAAM,EACnD,CAAC,KAAQ,IAAM,EAAG,CAAC,KAAQ,IAAM,EAAG,CAAC,KAAQ,IAAM,EACnD,CAAC,KAAQ,IAAM,EAAG,CAAC,KAAQ,IAAM,EAAG,CAAC,KAAQ,IAAM,EACnD,CAAC,KAAQ,IAAM,EAAG,CAAC,KAAQ,IAAM,EAAG,CAAC,KAAQ,IAAM,EACnD,CAAC,KAAQ,IAAM,EAAG,CAAC,KAAQ,IAAM,EAAG,CAAC,KAAQ,IAAM,EACnD,CAAC,KAAQ,IAAM,EAAG,CAAC,KAAQ,IAAM,EAAG,CAAC,KAAQ,IAAM,EACnD,CAAC,KAAQ,IAAM,EAAG,CAAC,KAAQ,IAAM,EAAG,CAAC,KAAQ,IAAM,EACnD,CAAC,KAAQ,IAAM,EAAG,CAAC,KAAQ,IAAM,EAAG,CAAC,KAAQ,IAAM,EACnD,CAAC,KAAQ,IAAM,EAAG,CAAC,KAAQ,IAAM,EAAG,CAAC,KAAQ,IAAM,EACnD,CAAC,KAAQ,IAAM,EAAG,CAAC,KAAQ,IAAM,EAAG,CAAC,KAAQ,IAAM,EACnD,CAAC,KAAQ,IAAM,EAAG,CAAC,KAAQ,IAAM,EAAG,CAAC,KAAQ,IAAM,EACnD,CAAC,KAAQ,IAAM,EAAG,CAAC,KAAQ,IAAM,EAAG,CAAC,KAAQ,IAAM,EACnD,CAAC,KAAQ,IAAM,EAAG,CAAC,KAAQ,IAAM,EAAG,CAAC,KAAQ,IAAM,EACnD,CAAC,KAAQ,IAAM,EAAG,CAAC,KAAQ,IAAM,EAAG,CAAC,KAAQ,IAAM,EACnD,CAAC,KAAQ,IAAM,EAAG,CAAC,KAAQ,IAAM,EAAG,CAAC,KAAQ,IAAM,EACnD,CAAC,KAAQ,IAAM,EAAG,CAAC,KAAQ,IAAM,EAAG,CAAC,KAAQ,IAAM,EACnD,CAAC,KAAQ,IAAM,EAAG,CAAC,KAAQ,IAAM,EAAG,CAAC,KAAQ,IAAM,EACnD,CAAC,KAAQ,IAAM,EAAG,CAAC,KAAQ,IAAM,EAAG,CAAC,KAAQ,IAAM,EACnD,CAAC,KAAQ,IAAM,EAAG,CAAC,KAAQ,IAAM,EAAG,CAAC,KAAQ,IAAM,EACnD,CAAC,KAAQ,IAAM,EAAG,CAAC,KAAQ,IAAM,EAAG,CAAC,KAAQ,IAAM,EACnD,CAAC,KAAQ,IAAM,EAAG,CAAC,KAAQ,IAAM,EAAG,CAAC,KAAQ,IAAM,EACnD,CAAC,KAAQ,IAAM,EAAG,CAAC,KAAQ,IAAM,EAAG,CAAC,KAAQ,IAAM,EACnD,CAAC,KAAQ,IAAM,EAAG,CAAC,KAAQ,IAAM,EAAG,CAAC,KAAQ,IAAM,EACnD,CAAC,KAAQ,IAAM,EAAG,CAAC,KAAQ,IAAM,EAAG,CAAC,KAAQ,IAAM,EACnD,CAAC,KAAQ,IAAM,EAAG,CAAC,KAAQ,IAAM,EAAG,CAAC,KAAQ,IAAM,EACnD,CAAC,KAAQ,IAAM,EAAG,CAAC,KAAQ,IAAM,EAAG,CAAC,KAAQ,IAAM,EACnD,CAAC,KAAQ,IAAM,EAAG,CAAC,KAAQ,IAAM,EAAG,CAAC,KAAQ,IAAM,EACnD,CAAC,KAAQ,IAAM,EAAG,CAAC,KAAQ,IAAM,EAAG,CAAC,KAAQ,IAAM,EACnD,CAAC,KAAQ,IAAM,EAAG,CAAC,KAAQ,IAAM,EAAG,CAAC,KAAQ,IAAM,EACnD,CAAC,KAAQ,IAAM,EAAG,CAAC,KAAQ,IAAM,EAAG,CAAC,KAAQ,IAAM,EACnD,CAAC,KAAQ,IAAM,EAAG,CAAC,KAAQ,IAAM,EAAG,CAAC,KAAQ,IAAM,EACnD,CAAC,KAAQ,IAAM,EAAG,CAAC,KAAQ,IAAM,EAAG,CAAC,KAAQ,IAAM,EACnD,CAAC,KAAQ,IAAM,EAAG,CAAC,KAAQ,IAAM,EAAG,CAAC,KAAQ,IAAM,EACnD,CAAC,KAAQ,IAAM,EAAG,CAAC,KAAQ,IAAM,EAAG,CAAC,MAAQ,KAAM,EACnD,CAAC,MAAQ,KAAM,EAAG,CAAC,MAAQ,KAAM,EAAG,CAAC,MAAQ,KAAM,EACnD,CAAC,MAAQ,KAAM,EAAG,CAAC,MAAQ,KAAM,EAAG,CAAC,MAAQ,KAAM,EACnD,CAAC,MAAQ,KAAM,EAAG,CAAC,MAAQ,KAAM,EAAG,CAAC,MAAQ,KAAM,EACnD,CAAC,MAAQ,KAAM,EAAG,CAAC,MAAQ,KAAM,EAAG,CAAC,MAAQ,KAAM,EACnD,CAAC,MAAQ,KAAM,EAAG,CAAC,MAAQ,KAAM,EAAG,CAAC,MAAQ,KAAM,EACnD,CAAC,MAAQ,KAAM,EAAG,CAAC,MAAQ,KAAM,EAAG,CAAC,MAAQ,KAAM,EACnD,CAAC,MAAQ,KAAM,EAAG,CAAC,MAAQ,KAAM,EAAG,CAAC,MAAQ,KAAM,EACnD,CAAC,MAAQ,KAAM,EAAG,CAAC,MAAQ,KAAM,EAAG,CAAC,MAAQ,KAAM,EACnD,CAAC,MAAQ,KAAM,EAAG,CAAC,MAAQ,KAAM,EAAG,CAAC,MAAQ,KAAM,EACnD,CAAC,MAAQ,KAAM,EAAG,CAAC,MAAQ,KAAM,EAAG,CAAC,MAAQ,KAAM,EACnD,CAAC,MAAQ,KAAM,EAAG,CAAC,MAAQ,KAAM,EAAG,CAAC,MAAQ,KAAM,EACnD,CAAC,MAAQ,KAAM,EAAG,CAAC,MAAQ,KAAM,EAAG,CAAC,MAAQ,KAAM,EACnD,CAAC,MAAQ,KAAM,EAAG,CAAC,MAAQ,KAAM,EAAG,CAAC,MAAQ,KAAM,EACnD,CAAC,MAAQ,KAAM,EAAG,CAAC,MAAQ,KAAM,EAAG,CAAC,MAAQ,KAAM,EACnD,CAAC,MAAQ,KAAM,CACjB,EACMC,GAAiB,CACrB,CAAC,MAAS,KAAO,EAAG,CAAC,MAAS,KAAO,EACrC,CAAC,MAAS,KAAO,EAAG,CAAC,MAAS,KAAO,EAAG,CAAC,MAAS,KAAO,EACzD,CAAC,MAAS,KAAO,EAAG,CAAC,MAAS,KAAO,EAAG,CAAC,MAAS,KAAO,EACzD,CAAC,MAAS,KAAO,EAAG,CAAC,MAAS,KAAO,EAAG,CAAC,MAAS,KAAO,EACzD,CAAC,MAAS,KAAO,EAAG,CAAC,MAAS,KAAO,EAAG,CAAC,MAAS,KAAO,EACzD,CAAC,MAAS,KAAO,EAAG,CAAC,MAAS,KAAO,EAAG,CAAC,MAAS,KAAO,EACzD,CAAC,MAAS,KAAO,EAAG,CAAC,MAAS,KAAO,EAAG,CAAC,MAAS,KAAO,EACzD,CAAC,MAAS,KAAO,EAAG,CAAC,MAAS,KAAO,EAAG,CAAC,MAAS,KAAO,EACzD,CAAC,MAAS,KAAO,EAAG,CAAC,MAAS,KAAO,EAAG,CAAC,MAAS,KAAO,EACzD,CAAC,MAAS,KAAO,EAAG,CAAC,MAAS,KAAO,EAAG,CAAC,MAAS,KAAO,EACzD,CAAC,MAAS,KAAO,EAAG,CAAC,MAAS,KAAO,EAAG,CAAC,MAAS,KAAO,EACzD,CAAC,MAAS,KAAO,EAAG,CAAC,MAAS,KAAO,EAAG,CAAC,MAAS,KAAO,EACzD,CAAC,MAAS,KAAO,EAAG,CAAC,MAAS,KAAO,EAAG,CAAC,MAAS,KAAO,EACzD,CAAC,MAAS,KAAO,EAAG,CAAC,MAAS,KAAO,EAAG,CAAC,MAAS,KAAO,EACzD,CAAC,MAAS,KAAO,EAAG,CAAC,MAAS,KAAO,EAAG,CAAC,MAAS,KAAO,EACzD,CAAC,MAAS,KAAO,EAAG,CAAC,MAAS,KAAO,EAAG,CAAC,MAAS,KAAO,EACzD,CAAC,MAAS,KAAO,EAAG,CAAC,MAAS,KAAO,EAAG,CAAC,MAAS,KAAO,EACzD,CAAC,MAAS,KAAO,EAAG,CAAC,MAAS,KAAO,EAAG,CAAC,MAAS,KAAO,EACzD,CAAC,MAAS,KAAO,EAAG,CAAC,MAAS,KAAO,EAAG,CAAC,MAAS,KAAO,EACzD,CAAC,MAAS,KAAO,EAAG,CAAC,MAAS,KAAO,EAAG,CAAC,MAAS,KAAO,EACzD,CAAC,MAAS,KAAO,EAAG,CAAC,MAAS,KAAO,EAAG,CAAC,MAAS,KAAO,EACzD,CAAC,MAAS,KAAO,EAAG,CAAC,MAAS,KAAO,EAAG,CAAC,MAAS,KAAO,EACzD,CAAC,MAAS,KAAO,EAAG,CAAC,MAAS,KAAO,EAAG,CAAC,MAAS,KAAO,EACzD,CAAC,MAAS,KAAO,EAAG,CAAC,MAAS,KAAO,EAAG,CAAC,MAAS,KAAO,EACzD,CAAC,MAAS,KAAO,EAAG,CAAC,MAAS,KAAO,EAAG,CAAC,MAAS,KAAO,EACzD,CAAC,MAAS,KAAO,EAAG,CAAC,MAAS,KAAO,EAAG,CAAC,MAAS,KAAO,EACzD,CAAC,MAAS,KAAO,EAAG,CAAC,MAAS,KAAO,EAAG,CAAC,MAAS,KAAO,EACzD,CAAC,MAAS,KAAO,EAAG,CAAC,MAAS,KAAO,EAAG,CAAC,MAAS,KAAO,EACzD,CAAC,MAAS,KAAO,EAAG,CAAC,MAAS,KAAO,EAAG,CAAC,MAAS,KAAO,EACzD,CAAC,MAAS,KAAO,EAAG,CAAC,MAAS,KAAO,EAAG,CAAC,MAAS,KAAO,EACzD,CAAC,MAAS,KAAO,EAAG,CAAC,MAAS,KAAO,EAAG,CAAC,MAAS,KAAO,EACzD,CAAC,OAAS,MAAO,EAAG,CAAC,OAAS,MAAO,EAAG,CAAC,OAAS,MAAO,EACzD,CAAC,OAAS,MAAO,EAAG,CAAC,OAAS,MAAO,EAAG,CAAC,OAAS,MAAO,EACzD,CAAC,OAAS,MAAO,EAAG,CAAC,OAAS,MAAO,EAAG,CAAC,OAAS,MAAO,EACzD,CAAC,OAAS,MAAO,EAAG,CAAC,OAAS,MAAO,EAAG,CAAC,OAAS,MAAO,EACzD,CAAC,OAAS,MAAO,EAAG,CAAC,OAAS,MAAO,EAAG,CAAC,OAAS,MAAO,EACzD,CAAC,OAAS,MAAO,EAAG,CAAC,OAAS,MAAO,EAAG,CAAC,OAAS,MAAO,EACzD,CAAC,OAAS,MAAO,EAAG,CAAC,OAAS,MAAO,EAAG,CAAC,OAAS,MAAO,EACzD,CAAC,OAAS,MAAO,EAAG,CAAC,OAAS,MAAO,EAAG,CAAC,OAAS,MAAO,EACzD,CAAC,OAAS,MAAO,CACnB,EACMC,GAAW,CACf,CAAC,KAAQ,IAAM,EAAG,CAAC,KAAQ,IAAM,EAAG,CAAC,KAAQ,IAAM,EACnD,CAAC,KAAQ,IAAM,EAAG,CAAC,KAAQ,IAAM,EAAG,CAAC,KAAQ,IAAM,EACnD,CAAC,KAAQ,IAAM,EAAG,CAAC,KAAQ,IAAM,EAAG,CAAC,KAAQ,IAAM,EACnD,CAAC,KAAQ,IAAM,EAAG,CAAC,KAAQ,IAAM,EAAG,CAAC,KAAQ,IAAM,EACnD,CAAC,KAAQ,IAAM,EAAG,CAAC,KAAQ,IAAM,EAAG,CAAC,KAAQ,IAAM,EACnD,CAAC,KAAQ,IAAM,EAAG,CAAC,KAAQ,IAAM,EAAG,CAAC,KAAQ,IAAM,EACnD,CAAC,KAAQ,IAAM,EAAG,CAAC,KAAQ,IAAM,EAAG,CAAC,KAAQ,IAAM,EACnD,CAAC,KAAQ,IAAM,EAAG,CAAC,KAAQ,IAAM,EAAG,CAAC,KAAQ,IAAM,EACnD,CAAC,MAAQ,KAAM,EAAG,CAAC,MAAQ,KAAM,EAAG,CAAC,MAAQ,KAAM,EACnD,CAAC,MAAQ,KAAM,EAAG,CAAC,MAAQ,KAAM,EAAG,CAAC,MAAQ,KAAM,EACnD,CAAC,MAAQ,KAAM,EAAG,CAAC,MAAQ,KAAM,EAAG,CAAC,MAAQ,KAAM,EACnD,CAAC,MAAQ,KAAM,EAAG,CAAC,MAAQ,KAAM,EAAG,CAAC,MAAQ,KAAM,EACnD,CAAC,MAAQ,KAAM,EAAG,CAAC,MAAQ,KAAM,EAAG,CAAC,MAAQ,KAAM,EACnD,CAAC,MAAQ,KAAM,EAAG,CAAC,MAAQ,KAAM,EAAG,CAAC,MAAQ,KAAM,EACnD,CAAC,MAAQ,KAAM,EAAG,CAAC,MAAQ,KAAM,EAAG,CAAC,MAAQ,KAAM,EACnD,CAAC,MAAQ,KAAM,EAAG,CAAC,MAAQ,KAAM,EAAG,CAAC,MAAQ,KAAM,EACnD,CAAC,MAAQ,KAAM,EAAG,CAAC,MAAQ,KAAM,EAAG,CAAC,MAAQ,KAAM,EACnD,CAAC,MAAQ,KAAM,EAAG,CAAC,MAAQ,KAAM,EAAG,CAAC,MAAQ,KAAM,EACnD,CAAC,MAAQ,KAAM,EAAG,CAAC,MAAQ,KAAM,EAAG,CAAC,MAAQ,KAAM,EACnD,CAAC,MAAQ,KAAM,EAAG,CAAC,MAAQ,KAAM,EAAG,CAAC,MAAQ,KAAM,EACnD,CAAC,MAAQ,KAAM,CACjB,EACMC,GAAY,CAChB,CAAC,MAAS,KAAO,EAAG,CAAC,MAAS,MAAO,EACrC,CAAC,OAAS,MAAO,EAAG,CAAC,OAAS,MAAO,EAAG,CAAC,OAAS,MAAO,EACzD,CAAC,OAAS,MAAO,EAAG,CAAC,OAAS,MAAO,EAAG,CAAC,OAAS,MAAO,EACzD,CAAC,OAAS,MAAO,EAAG,CAAC,OAAS,MAAO,EAAG,CAAC,OAAS,MAAO,EACzD,CAAC,OAAS,MAAO,EAAG,CAAC,OAAS,MAAO,EAAG,CAAC,OAAS,MAAO,EACzD,CAAC,OAAS,MAAO,EAAG,CAAC,OAAS,MAAO,EAAG,CAAC,OAAS,MAAO,EACzD,CAAC,OAAS,MAAO,EAAG,CAAC,OAAS,MAAO,EAAG,CAAC,OAAS,MAAO,EACzD,CAAC,OAAS,MAAO,EAAG,CAAC,OAAS,MAAO,EAAG,CAAC,OAAS,MAAO,EACzD,CAAC,OAAS,MAAO,EAAG,CAAC,OAAS,MAAO,EAAG,CAAC,OAAS,MAAO,EACzD,CAAC,OAAS,MAAO,EAAG,CAAC,OAAS,MAAO,EAAG,CAAC,OAAS,MAAO,EACzD,CAAC,OAAS,MAAO,EAAG,CAAC,OAAS,MAAO,EAAG,CAAC,OAAS,MAAO,EACzD,CAAC,OAAS,MAAO,EAAG,CAAC,OAAS,MAAO,EAAG,CAAC,OAAS,MAAO,EACzD,CAAC,OAAS,MAAO,EAAG,CAAC,OAAS,MAAO,EAAG,CAAC,OAAS,MAAO,EACzD,CAAC,OAAS,MAAO,EAAG,CAAC,OAAS,MAAO,EAAG,CAAC,OAAS,MAAO,EACzD,CAAC,OAAS,MAAO,EAAG,CAAC,OAAS,MAAO,EAAG,CAAC,OAAS,MAAO,EACzD,CAAC,OAAS,MAAO,EAAG,CAAC,OAAS,MAAO,EAAG,CAAC,OAAS,MAAO,EACzD,CAAC,OAAS,MAAO,EAAG,CAAC,OAAS,MAAO,EAAG,CAAC,OAAS,MAAO,EACzD,CAAC,OAAS,MAAO,EAAG,CAAC,OAAS,MAAO,EAAG,CAAC,OAAS,MAAO,CAC3D,EAGIC,EAEJ,SAASC,GAASC,EAAaC,EAA2B,CACxD,IAAIC,EAAM,EACNC,EAAMF,EAAK,OAAS,EACpBG,EACJ,GAAIJ,EAAMC,EAAK,CAAC,EAAE,CAAC,GAAKD,EAAMC,EAAKE,CAAG,EAAE,CAAC,EACvC,MAAO,GAET,KAAOA,GAAOD,GAEZ,GADAE,EAAOF,EAAMC,GAAQ,EACjBH,EAAMC,EAAKG,CAAG,EAAE,CAAC,EACnBF,EAAME,EAAM,UACHJ,EAAMC,EAAKG,CAAG,EAAE,CAAC,EAC1BD,EAAMC,EAAM,MAEZ,OAAO,GAGX,MAAO,EACT,CAGO,IAAMC,GAAN,KAAoD,CAGzD,aAAc,CAFd,KAAgB,QAAU,KAGxB,GAAI,CAACP,EAAO,CACVA,EAAQ,IAAI,WAAW,KAAK,EAC5BA,EAAM,KAAK,CAAC,EACZA,EAAM,CAAC,EAAI,EACXA,EAAM,KAAK,EAAG,EAAG,EAAE,EACnBA,EAAM,KAAK,EAAG,IAAM,GAAI,EACxB,QAASQ,EAAI,EAAGA,EAAIZ,GAAc,OAAQ,EAAEY,EAC1CR,EAAM,KAAK,EAAGJ,GAAcY,CAAC,EAAE,CAAC,EAAGZ,GAAcY,CAAC,EAAE,CAAC,EAAI,CAAC,EAE5D,QAASA,EAAI,EAAGA,EAAIV,GAAS,OAAQ,EAAEU,EACrCR,EAAM,KAAK,EAAGF,GAASU,CAAC,EAAE,CAAC,EAAGV,GAASU,CAAC,EAAE,CAAC,EAAI,CAAC,CAEpD,CACF,CAEO,QAAQC,EAA+B,CAC5C,OAAIA,EAAM,GAAW,EACjBA,EAAM,IAAY,EAClBA,EAAM,MAAcT,EAAMS,CAAG,EAC7BR,GAASQ,EAAKZ,EAAc,EAAU,EACtCI,GAASQ,EAAKV,EAAS,EAAU,EAC9B,CACT,CAEO,eAAeW,EAAmBC,EAAyD,CAChG,IAAIC,EAAQ,KAAK,QAAQF,CAAS,EAC9BG,EAAaD,IAAU,GAAKD,IAAc,EAC9C,GAAIE,EAAY,CACd,IAAMC,EAAWC,EAAe,aAAaJ,CAAS,EAClDG,IAAa,EACfD,EAAa,GACJC,EAAWF,IACpBA,EAAQE,EAEZ,CACA,OAAOC,EAAe,oBAAoB,EAAGH,EAAOC,CAAU,CAChE,CACF,EC/NO,IAAMG,GAAN,KAA+D,CAC7D,SAASC,EAA0B,CACxCA,EAAS,QAAQ,SAAS,IAAIC,EAAY,CAC5C,CACO,SAAgB,CAAE,CAC3B", "names": ["BMP_COMBINING", "HIGH_COMBINING", "table", "bisearch", "ucs", "data", "min", "max", "mid", "UnicodeV6", "r", "num", "codepoint", "preceding", "width", "<PERSON><PERSON><PERSON><PERSON>", "oldWidth", "UnicodeService", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "ErrorNoTelemetry", "listener", "newUnexpectedErrorHandler", "<PERSON><PERSON><PERSON><PERSON>", "onUnexpectedError", "e", "isCancellationError", "<PERSON><PERSON><PERSON><PERSON>", "canceledName", "isCancellationError", "error", "CancellationError", "ErrorNoTelemetry", "_ErrorNoTelemetry", "msg", "err", "result", "createSingleCallFunction", "fn", "fnDidRunCallback", "_this", "didCall", "result", "findLastIdxMonotonous", "array", "predicate", "startIdx", "endIdxEx", "i", "j", "k", "_MonotonousArray", "_array", "predicate", "item", "idx", "findLastIdxMonotonous", "MonotonousArray", "CompareResult", "is<PERSON><PERSON><PERSON><PERSON>", "result", "isLessThanOrEqual", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "isNeitherLessOrGreaterThan", "compareBy", "selector", "comparator", "a", "b", "numberComparator", "a", "b", "_CallbackIterable", "iterate", "handler", "item", "result", "predicate", "cb", "mapFn", "comparator", "first", "CompareResult", "_callback", "CallbackIterable", "groupBy", "data", "groupFn", "result", "element", "key", "target", "_a", "_b", "SetWith<PERSON>ey", "values", "to<PERSON><PERSON>", "value", "key", "entry", "callbackfn", "thisArg", "SetMap", "key", "value", "values", "fn", "Iterable", "is", "thing", "_empty", "empty", "single", "element", "wrap", "iterableOrElement", "from", "iterable", "reverse", "array", "i", "isEmpty", "first", "some", "predicate", "find", "filter", "map", "fn", "index", "flatMap", "concat", "iterables", "reduce", "reducer", "initialValue", "value", "slice", "arr", "to", "consume", "atMost", "consumed", "iterator", "next", "asyncToArray", "result", "item", "TRACK_DISPOSABLES", "disposableTracker", "_DisposableTracker", "d", "val", "data", "child", "parent", "x", "disposable", "cache", "cacheValue", "result", "rootParentCache", "v", "k", "maxReported", "preComputedLeaks", "uncoveredLeakingObjs", "leakingObjects", "info", "leakingObjsSet", "o", "l", "getStackTracePath", "leaking", "removePrefix", "array", "linesToRemove", "regexp", "lines", "p", "stackTraceStarts", "SetMap", "stackTracePath", "i", "compareBy", "numberComparator", "message", "stackTraceFormattedLines", "line", "prevStarts", "continuations", "groupBy", "cont", "set", "DisposableTracker", "setDisposableTracker", "tracker", "__is_disposable_tracked__", "stack", "Disposable", "trackDisposable", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "setParentOfDisposable", "setParentOfDisposables", "children", "dispose", "arg", "Iterable", "errors", "d", "e", "combinedDisposable", "disposables", "parent", "toDisposable", "dispose", "setParentOfDisposables", "fn", "self", "trackDisposable", "createSingleCallFunction", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "_DisposableStore", "o", "setParentOfDisposable", "DisposableStore", "Disposable", "_Node", "element", "Node", "hasPerformanceNow", "StopWatch", "_StopWatch", "highResolution", "_enableListenerGCedWarning", "_enableDisposeWithListenerWarning", "_enableSnapshotPotentialLeakWarning", "Event", "Disposable", "_addLeakageTraceLogic", "options", "origListenerDidAdd", "stack", "Stacktrace", "count", "defer", "event", "disposable", "debounce", "once", "listener", "thisArgs", "disposables", "<PERSON><PERSON><PERSON>", "result", "e", "map", "snapshot", "i", "for<PERSON>ach", "each", "filter", "signal", "any", "events", "combinedDisposable", "addAndReturnDisposable", "reduce", "merge", "initial", "output", "emitter", "Emitter", "d", "store", "delay", "leading", "flushOnListenerRemove", "leakWarningThreshold", "subscription", "handle", "numDebouncedCalls", "doFire", "cur", "_output", "accumulate", "last", "latch", "equals", "a", "b", "firstCall", "cache", "value", "shouldEmit", "split", "isT", "buffer", "flushAfterTimeout", "_buffer", "flush", "chain", "sythen<PERSON><PERSON>", "cs", "ChainableSynthesis", "HaltChainable", "fn", "v", "step", "fromNodeEventEmitter", "eventName", "id", "args", "onFirstListenerAdd", "onLastListenerRemove", "fromDOMEventEmitter", "to<PERSON>romise", "resolve", "fromPromise", "promise", "res", "forward", "from", "to", "runAndSubscribe", "handler", "EmitterObserver", "_observable", "_change", "fromObservable", "obs", "fromObservableLight", "observable", "<PERSON><PERSON><PERSON><PERSON>", "observer", "DisposableStore", "_EventProfiling", "name", "listenerCount", "StopWatch", "elapsed", "EventProfiling", "_globalLeakWarningThreshold", "_LeakageMonitor", "_error<PERSON><PERSON><PERSON>", "threshold", "name", "stack", "listenerCount", "count", "topStack", "topCount", "message", "error", "ListenerLeakError", "LeakageMonitor", "Stacktrace", "_Stacktrace", "value", "err", "ListenerRefusalError", "id", "UniqueContainer", "compactionThreshold", "forEachListener", "listeners", "fn", "i", "l", "_listenerFinalizers", "_enableListenerGCedWarning", "leaks", "heldValue", "Emitter", "options", "_globalLeakWarningThreshold", "onUnexpectedError", "EventProfiling", "_enableDisposeWithListenerWarning", "callback", "thisArgs", "disposables", "tuple", "Disposable", "contained", "removeMonitor", "EventDeliveryQueuePrivate", "result", "toDisposable", "DisposableStore", "match", "listener", "index", "adjustDeliveryQueue", "n", "<PERSON><PERSON><PERSON><PERSON>", "e", "dq", "event", "EventDeliveryQueuePrivate", "emitter", "value", "end", "UnicodeService", "_UnicodeService", "Emitter", "defaultProvider", "UnicodeV6", "value", "state", "width", "<PERSON><PERSON><PERSON><PERSON>", "version", "provider", "num", "s", "result", "precedingInfo", "length", "i", "code", "second", "currentInfo", "ch<PERSON><PERSON><PERSON>", "codepoint", "preceding", "BMP_COMBINING", "HIGH_COMBINING", "BMP_WIDE", "HIGH_WIDE", "table", "bisearch", "ucs", "data", "min", "max", "mid", "UnicodeV11", "r", "num", "codepoint", "preceding", "width", "<PERSON><PERSON><PERSON><PERSON>", "oldWidth", "UnicodeService", "Unicode11Addon", "terminal", "UnicodeV11"]}