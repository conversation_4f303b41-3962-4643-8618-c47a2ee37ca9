// PATCHED: Mock functions to disable tracking
function trackDisabled() { console.log('[AUGMENT] Tracking disabled'); return Promise.resolve(); }
function sendTelemetryDisabled() { console.log('[AUGMENT] Telemetry disabled'); return Promise.resolve(); }
function analyticsDisabled() { console.log('[AUGMENT] Analytics disabled'); return Promise.resolve(); }
function reportUsageDisabled() { console.log('[AUGMENT] Usage reporting disabled'); return Promise.resolve(); }

// PATCHED: Local credit system override
const LOCAL_CREDIT_OVERRIDE = {
  enabled: true,
  credits: { total: 10000, used: 0, remaining: 10000 },
  checkCredits: function() { return Promise.resolve(true); },
  deductCredits: function(amount) { 
    console.log('[AUGMENT] Would deduct', amount, 'credits (disabled)'); 
    return Promise.resolve(); 
  },
  getCreditBalance: function() { return Promise.resolve(this.credits); }
};

if (typeof window !== 'undefined') { window.LOCAL_CREDIT_OVERRIDE = LOCAL_CREDIT_OVERRIDE; }
