# Augment Extension Patcher - Applies all the manual steps automatically
# This script implements all the manual patching steps you outlined

param(
    [switch]$Force = $false
)

Write-Host ""
Write-Host "========================================" -ForegroundColor Cyan
Write-Host "   Augment Extension Patcher" -ForegroundColor Cyan
Write-Host "========================================" -ForegroundColor Cyan
Write-Host ""

# Set paths
$augmentPath = Join-Path $env:USERPROFILE ".vscode\extensions\augment.vscode-augment-0.464.0"
$extensionFile = Join-Path $augmentPath "out\extension.js"
$backupFile = Join-Path $augmentPath "out\extension.js.backup"

# Check if VS Code is running
$codeProcesses = Get-Process -Name "Code" -ErrorAction SilentlyContinue
if ($codeProcesses -and !$Force) {
    Write-Host "WARNING: VS Code is currently running!" -ForegroundColor Red
    Write-Host "Please close VS Code completely before running this script." -ForegroundColor Yellow
    Write-Host ""
    Write-Host "To force patching anyway, run with -Force parameter:" -ForegroundColor Yellow
    Write-Host "  .\patch-augment-extension.ps1 -Force" -ForegroundColor Yellow
    Write-Host ""
    Read-Host "Press Enter to exit"
    exit 1
}

# Check if Augment extension exists
if (-not (Test-Path $extensionFile)) {
    Write-Host "ERROR: Augment extension file not found!" -ForegroundColor Red
    Write-Host "Expected: $extensionFile" -ForegroundColor Red
    Read-Host "Press Enter to exit"
    exit 1
}

Write-Host "Found Augment extension file: $extensionFile" -ForegroundColor Green
Write-Host ""

# Step 3: Create Backup
Write-Host "Step 1: Creating backup..." -ForegroundColor Yellow
if (-not (Test-Path $backupFile)) {
    try {
        Copy-Item $extensionFile $backupFile -Force
        Write-Host "Backup created: extension.js.backup" -ForegroundColor Green
    } catch {
        Write-Host "ERROR: Failed to create backup!" -ForegroundColor Red
        Write-Host $_.Exception.Message -ForegroundColor Red
        Read-Host "Press Enter to exit"
        exit 1
    }
} else {
    Write-Host "Backup already exists" -ForegroundColor Green
}

# Step 4: Read the extension file
Write-Host ""
Write-Host "Step 2: Reading extension file..." -ForegroundColor Yellow
try {
    $extensionCode = Get-Content $extensionFile -Raw -Encoding UTF8
    $sizeKB = [math]::Round($extensionCode.Length / 1024, 1)
    Write-Host "Extension file loaded ($sizeKB KB)" -ForegroundColor Green
} catch {
    Write-Host "ERROR: Failed to read extension file!" -ForegroundColor Red
    Write-Host $_.Exception.Message -ForegroundColor Red
    Read-Host "Press Enter to exit"
    exit 1
}

Write-Host ""
Write-Host "Step 3: Applying patches..." -ForegroundColor Yellow

# A. Add Mock Functions at the Beginning
$mockFunctions = @"
// PATCHED: Mock functions to disable tracking
function trackDisabled() { console.log('[AUGMENT] Tracking disabled'); return Promise.resolve(); }
function sendTelemetryDisabled() { console.log('[AUGMENT] Telemetry disabled'); return Promise.resolve(); }
function analyticsDisabled() { console.log('[AUGMENT] Analytics disabled'); return Promise.resolve(); }
function reportUsageDisabled() { console.log('[AUGMENT] Usage reporting disabled'); return Promise.resolve(); }

// PATCHED: Local credit system override
const LOCAL_CREDIT_OVERRIDE = {
  enabled: true,
  credits: { total: 10000, used: 0, remaining: 10000 },
  checkCredits: function() { return Promise.resolve(true); },
  deductCredits: function(amount) {
    console.log('[AUGMENT] Would deduct', amount, 'credits (disabled)');
    return Promise.resolve();
  },
  getCreditBalance: function() { return Promise.resolve(this.credits); }
};

if (typeof window !== 'undefined') { window.LOCAL_CREDIT_OVERRIDE = LOCAL_CREDIT_OVERRIDE; }

"@

$extensionCode = $mockFunctions + "`n" + $extensionCode
Write-Host "✓ Added mock functions and credit override" -ForegroundColor Green

# B. Find and Replace Tracking Functions
$trackingReplacements = @(
    @{ Find = '\.track\('; Replace = '.trackDisabled(' },
    @{ Find = '\.sendTelemetry\('; Replace = '.sendTelemetryDisabled(' },
    @{ Find = '\.analytics\('; Replace = '.analyticsDisabled(' },
    @{ Find = '\.reportUsage\('; Replace = '.reportUsageDisabled(' }
)

$totalReplacements = 0
foreach ($replacement in $trackingReplacements) {
    $matches = [regex]::Matches($extensionCode, $replacement.Find)
    if ($matches.Count -gt 0) {
        $extensionCode = $extensionCode -replace $replacement.Find, $replacement.Replace
        $totalReplacements += $matches.Count
        Write-Host "✓ Replaced $($matches.Count) instances of $($replacement.Find)" -ForegroundColor Green
    }
}

# C. Block Network Requests
$urlReplacements = @(
    @{ Find = 'https://api\.augmentcode\.com/usage'; Replace = 'http://localhost:0' },
    @{ Find = 'https://api\.augmentcode\.com/analytics'; Replace = 'http://localhost:0' },
    @{ Find = 'https://api\.augmentcode\.com/billing'; Replace = 'http://localhost:0' },
    @{ Find = 'https://api\.augmentcode\.com/telemetry'; Replace = 'http://localhost:0' }
)

foreach ($replacement in $urlReplacements) {
    $matches = [regex]::Matches($extensionCode, $replacement.Find)
    if ($matches.Count -gt 0) {
        $extensionCode = $extensionCode -replace $replacement.Find, $replacement.Replace
        $totalReplacements += $matches.Count
        Write-Host "✓ Blocked $($matches.Count) network endpoints" -ForegroundColor Green
    }
}

# D. Patch Credit Functions (Optional)
$creditReplacements = @(
    @{ Find = '\.getCreditBalance\(\)'; Replace = '.getCreditBalance() || LOCAL_CREDIT_OVERRIDE.getCreditBalance()' },
    @{ Find = '\.checkCredits\('; Replace = '.checkCredits() || LOCAL_CREDIT_OVERRIDE.checkCredits(' }
)

foreach ($replacement in $creditReplacements) {
    $matches = [regex]::Matches($extensionCode, $replacement.Find)
    if ($matches.Count -gt 0) {
        $extensionCode = $extensionCode -replace $replacement.Find, $replacement.Replace
        $totalReplacements += $matches.Count
        Write-Host "✓ Patched $($matches.Count) credit functions" -ForegroundColor Green
    }
}

Write-Host ""
Write-Host "Step 4: Saving patched extension..." -ForegroundColor Yellow

# Step 5: Save the patched file
try {
    Set-Content $extensionFile $extensionCode -Encoding UTF8 -Force
    Write-Host "✓ Patched extension saved successfully" -ForegroundColor Green
} catch {
    Write-Host "ERROR: Failed to save patched extension!" -ForegroundColor Red
    Write-Host $_.Exception.Message -ForegroundColor Red
    Write-Host ""
    Write-Host "Restoring from backup..." -ForegroundColor Yellow
    Copy-Item $backupFile $extensionFile -Force
    Read-Host "Press Enter to exit"
    exit 1
}

Write-Host ""
Write-Host "========================================" -ForegroundColor Cyan
Write-Host "   Patching Complete!" -ForegroundColor Cyan
Write-Host "========================================" -ForegroundColor Cyan
Write-Host ""
Write-Host "📊 Applied $totalReplacements patches total" -ForegroundColor Green
Write-Host ""
Write-Host "📋 What was patched:" -ForegroundColor Cyan
Write-Host "  ✓ Added mock tracking functions" -ForegroundColor Green
Write-Host "  ✓ Added local credit system override" -ForegroundColor Green
Write-Host "  ✓ Disabled telemetry and analytics calls" -ForegroundColor Green
Write-Host "  ✓ Blocked network requests to tracking endpoints" -ForegroundColor Green
Write-Host "  ✓ Patched credit system functions" -ForegroundColor Green
Write-Host ""
Write-Host "📋 Next steps:" -ForegroundColor Cyan
Write-Host "  1. Start VS Code" -ForegroundColor Yellow
Write-Host "  2. Open Developer Console (Help > Toggle Developer Tools > Console)" -ForegroundColor Yellow
Write-Host "  3. Look for '[AUGMENT] Tracking disabled' messages" -ForegroundColor Yellow
Write-Host "  4. Verify Augment features still work" -ForegroundColor Yellow
Write-Host ""
Write-Host "🔄 To restore original extension:" -ForegroundColor Cyan
Write-Host "  Copy-Item '$backupFile' '$extensionFile' -Force" -ForegroundColor Cyan
Write-Host ""

Read-Host "Press Enter to exit"
