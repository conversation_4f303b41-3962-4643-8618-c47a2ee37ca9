# Augment Usage Tracking Disabler

This tool disables usage tracking in the Augment VS Code extension while keeping the local credit display functionality.

## ⚠️ Important Warnings

- **This may violate Augment's terms of service**
- **Use at your own risk**
- **Always backup your files before proceeding**
- **Extension updates will overwrite these changes**

## What This Does

✅ **Keeps Working:**
- Local credit display in status bar
- All Augment AI features (chat, completions, etc.)
- Local functionality and UI

❌ **Disables:**
- Usage tracking to Augment servers
- Analytics and telemetry reporting
- Network requests to billing/usage endpoints
- Credit consumption reporting

## Files Included

- `disable-augment-tracking.js` - Main patching script
- `restore-augment.js` - Script to restore original extension
- `augment-config.json` - Configuration file
- `README-AUGMENT-PATCH.md` - This documentation

## How to Use

### Step 1: Backup (IMPORTANT!)
```bash
# Navigate to your Augment extension directory
cd ~/.vscode/extensions/augment.vscode-augment-0.464.0/out/

# Create manual backup
cp extension.js extension.js.manual-backup
```

### Step 2: Run the Patcher
```bash
# Run the disable script
node disable-augment-tracking.js
```

### Step 3: Restart VS Code
Close and reopen VS Code for changes to take effect.

### Step 4: Verify
- Check that Augment still works
- Look for "[AUGMENT] Tracking disabled" messages in console
- Credit display should still show in status bar

## What Gets Patched

### 1. Telemetry Functions
```javascript
// Before
.track(usageData)
.sendTelemetry(event)

// After  
.trackDisabled(usageData)  // Does nothing
.sendTelemetryDisabled(event)  // Does nothing
```

### 2. Network Requests
```javascript
// Before
fetch("https://api.augmentcode.com/usage")

// After
fetch("http://localhost:0" // DISABLED)
```

### 3. Credit System
```javascript
// Adds local override
const LOCAL_CREDIT_OVERRIDE = {
  credits: { total: 10000, used: 0, remaining: 10000 },
  checkCredits: () => Promise.resolve(true),
  deductCredits: (amount) => Promise.resolve()
};
```

## Restoring Original Extension

If you want to restore the original Augment extension:

```bash
# Option 1: Use restore script
node restore-augment.js

# Option 2: Manual restore
cd ~/.vscode/extensions/augment.vscode-augment-0.464.0/out/
cp extension.js.backup extension.js
```

## Troubleshooting

### Extension Not Found
```
❌ Augment extension not found
```
**Solution:** Make sure Augment extension is installed and check the path in the script.

### Permission Errors
```
❌ Permission denied
```
**Solution:** Run with appropriate permissions or check file ownership.

### Extension Still Tracking
**Solution:** 
1. Restart VS Code completely
2. Check console for "[AUGMENT] Tracking disabled" messages
3. Verify the patch was applied correctly

### Features Not Working
**Solution:**
1. Restore original extension: `node restore-augment.js`
2. Restart VS Code
3. Check if issue persists with original extension

## Technical Details

### Patch Locations
- **Extension file:** `~/.vscode/extensions/augment.vscode-augment-0.464.0/out/extension.js`
- **Backup location:** `~/.vscode/extensions/augment.vscode-augment-0.464.0/out/extension.js.backup`

### What Happens to Credits
- Local display shows fake unlimited credits
- No actual credit consumption is reported to Augment servers
- All AI features continue to work normally

### Network Blocking
The script blocks requests to these endpoints:
- `https://api.augmentcode.com/usage`
- `https://api.augmentcode.com/analytics`
- `https://api.augmentcode.com/billing`
- `https://api.augmentcode.com/telemetry`

## Limitations

1. **Extension Updates:** Any update to Augment will overwrite these changes
2. **Terms of Service:** This may violate Augment's ToS
3. **Support:** Augment support won't help with modified extensions
4. **Functionality:** Some features might break if they depend on usage tracking

## Legal Disclaimer

This tool is provided for educational purposes only. Users are responsible for:
- Complying with Augment's terms of service
- Understanding the risks of modifying software
- Any consequences of using this tool

The authors are not responsible for any issues, violations, or damages resulting from the use of this tool.

## Alternative Approaches

Instead of patching, consider:
1. **Network-level blocking** using hosts file or firewall
2. **Proxy configuration** to intercept and block requests
3. **Using Augment's official settings** if they provide opt-out options

---

**Remember:** Always backup your files and use at your own risk!
