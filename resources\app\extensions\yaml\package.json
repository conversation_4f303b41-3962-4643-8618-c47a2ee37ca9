{"name": "yaml", "displayName": "%displayName%", "description": "%description%", "version": "1.0.0", "publisher": "vscode", "license": "MIT", "engines": {"vscode": "*"}, "scripts": {"update-grammar": "node ./build/update-grammar.js"}, "categories": ["Programming Languages"], "contributes": {"languages": [{"id": "dockercompose", "aliases": ["Compose", "compose"], "filenamePatterns": ["compose.yml", "compose.yaml", "compose.*.yml", "compose.*.yaml", "*docker*compose*.yml", "*docker*compose*.yaml"], "configuration": "./language-configuration.json"}, {"id": "yaml", "aliases": ["YAML", "yaml"], "extensions": [".yaml", ".yml", ".e<PERSON>l", ".eyml", ".cff", ".yaml-tmlanguage", ".yaml-tmpreferences", ".yaml-tmtheme", ".winget"], "firstLine": "^#cloud-config", "configuration": "./language-configuration.json"}], "grammars": [{"language": "dockercompose", "scopeName": "source.yaml", "path": "./syntaxes/yaml.tmLanguage.json"}, {"scopeName": "source.yaml.1.3", "path": "./syntaxes/yaml-1.3.tmLanguage.json"}, {"scopeName": "source.yaml.1.2", "path": "./syntaxes/yaml-1.2.tmLanguage.json"}, {"scopeName": "source.yaml.1.1", "path": "./syntaxes/yaml-1.1.tmLanguage.json"}, {"scopeName": "source.yaml.1.0", "path": "./syntaxes/yaml-1.0.tmLanguage.json"}, {"scopeName": "source.yaml.embedded", "path": "./syntaxes/yaml-embedded.tmLanguage.json"}, {"language": "yaml", "scopeName": "source.yaml", "path": "./syntaxes/yaml.tmLanguage.json", "unbalancedBracketScopes": ["invalid.illegal", "meta.scalar.yaml", "storage.type.tag.shorthand.yaml", "keyword.control.flow"]}], "configurationDefaults": {"[yaml]": {"editor.insertSpaces": true, "editor.tabSize": 2, "editor.autoIndent": "advanced", "diffEditor.ignoreTrimWhitespace": false, "editor.defaultColorDecorators": "never"}, "[dockercompose]": {"editor.insertSpaces": true, "editor.tabSize": 2, "editor.autoIndent": "advanced"}}}, "repository": {"type": "git", "url": "https://github.com/microsoft/vscode.git"}}