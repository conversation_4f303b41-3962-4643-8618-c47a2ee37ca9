# Augment Usage Tracking Disabler - PowerShell Version
# This script disables usage tracking in Augment extension while keeping local credit display

Write-Host ""
Write-Host "========================================" -ForegroundColor Cyan
Write-Host "   Augment Usage Tracking Disabler" -ForegroundColor Cyan
Write-Host "========================================" -ForegroundColor Cyan
Write-Host ""
Write-Host "WARNING: This modifies Augment extension files" -ForegroundColor Yellow
Write-Host "Make sure to backup originals first!" -ForegroundColor Yellow
Write-Host ""

# Set paths
$augmentPath = Join-Path $env:USERPROFILE ".vscode\extensions\augment.vscode-augment-0.464.0"
$extensionFile = Join-Path $augmentPath "out\extension.js"
$backupFile = Join-Path $augmentPath "out\extension.js.backup"

# Check if Augment extension exists
if (-not (Test-Path $augmentPath)) {
    Write-Host "ERROR: Augment extension not found!" -ForegroundColor Red
    Write-Host "Expected location: $augmentPath" -ForegroundColor Red
    Write-Host "Please make sure Augment extension is installed." -ForegroundColor Red
    Read-Host "Press Enter to exit"
    exit 1
}

if (-not (Test-Path $extensionFile)) {
    Write-Host "ERROR: Extension file not found!" -ForegroundColor Red
    Write-Host "Expected file: $extensionFile" -ForegroundColor Red
    Read-Host "Press Enter to exit"
    exit 1
}

Write-Host "Found Augment extension at: $augmentPath" -ForegroundColor Green
Write-Host ""

# Create backup
Write-Host "Creating backup..." -ForegroundColor Yellow
if (-not (Test-Path $backupFile)) {
    Copy-Item $extensionFile $backupFile
    Write-Host "✓ Backup created: extension.js.backup" -ForegroundColor Green
} else {
    Write-Host "✓ Backup already exists" -ForegroundColor Green
}

# Read the extension file
Write-Host "Reading extension file..." -ForegroundColor Yellow
try {
    $extensionCode = Get-Content $extensionFile -Raw -Encoding UTF8
} catch {
    Write-Host "ERROR: Failed to read extension file!" -ForegroundColor Red
    Write-Host $_.Exception.Message -ForegroundColor Red
    Read-Host "Press Enter to exit"
    exit 1
}

Write-Host "Applying patches..." -ForegroundColor Yellow

# Apply patches to disable tracking
$patchCount = 0

# 1. Disable telemetry/analytics calls
$telemetryPatches = @(
    @{ Pattern = '\.track\s*\('; Replacement = '.trackDisabled(' },
    @{ Pattern = '\.sendTelemetry\s*\('; Replacement = '.sendTelemetryDisabled(' },
    @{ Pattern = '\.analytics\s*\('; Replacement = '.analyticsDisabled(' },
    @{ Pattern = '\.reportUsage\s*\('; Replacement = '.reportUsageDisabled(' }
)

foreach ($patch in $telemetryPatches) {
    $matches = [regex]::Matches($extensionCode, $patch.Pattern)
    if ($matches.Count -gt 0) {
        $extensionCode = $extensionCode -replace $patch.Pattern, $patch.Replacement
        $patchCount += $matches.Count
        Write-Host "Applied patch: $($patch.Pattern) ($($matches.Count) replacements)" -ForegroundColor Green
    }
}

# 2. Block network requests to usage/billing endpoints
$networkPatches = @(
    @{ Pattern = 'https://.*augmentcode\.com.*usage.*'; Replacement = '"http://localhost:0" // BLOCKED USAGE ENDPOINT' },
    @{ Pattern = 'https://.*augmentcode\.com.*billing.*'; Replacement = '"http://localhost:0" // BLOCKED BILLING ENDPOINT' },
    @{ Pattern = 'https://.*augmentcode\.com.*analytics.*'; Replacement = '"http://localhost:0" // BLOCKED ANALYTICS ENDPOINT' }
)

foreach ($patch in $networkPatches) {
    $matches = [regex]::Matches($extensionCode, $patch.Pattern)
    if ($matches.Count -gt 0) {
        $extensionCode = $extensionCode -replace $patch.Pattern, $patch.Replacement
        $patchCount += $matches.Count
        Write-Host "Blocked network endpoint: $($matches.Count) URLs" -ForegroundColor Green
    }
}

# 3. Add mock functions and local credit system
$mockFunctions = @"
// PATCHED: Mock functions to disable tracking
function trackDisabled() { console.log('[AUGMENT] Tracking disabled'); return Promise.resolve(); }
function sendTelemetryDisabled() { console.log('[AUGMENT] Telemetry disabled'); return Promise.resolve(); }
function analyticsDisabled() { console.log('[AUGMENT] Analytics disabled'); return Promise.resolve(); }
function reportUsageDisabled() { console.log('[AUGMENT] Usage reporting disabled'); return Promise.resolve(); }

// PATCHED: Local credit system override
const LOCAL_CREDIT_OVERRIDE = {
  enabled: true,
  credits: { total: 10000, used: 0, remaining: 10000 },
  checkCredits: function() { return Promise.resolve(true); },
  deductCredits: function(amount) {
    console.log('[AUGMENT] Would deduct', amount, 'credits (disabled)');
    return Promise.resolve();
  },
  getCreditBalance: function() { return Promise.resolve(this.credits); }
};

if (typeof window !== 'undefined') { window.LOCAL_CREDIT_OVERRIDE = LOCAL_CREDIT_OVERRIDE; }

"@

$extensionCode = $mockFunctions + "`n" + $extensionCode

# 4. Patch credit balance display
$creditPatches = @(
    @{ Pattern = '\.getCreditBalance\s*\(\s*\)'; Replacement = '.getCreditBalance() || LOCAL_CREDIT_OVERRIDE.getCreditBalance()' },
    @{ Pattern = '\.checkCredits\s*\('; Replacement = '.checkCredits() || LOCAL_CREDIT_OVERRIDE.checkCredits(' }
)

foreach ($patch in $creditPatches) {
    $matches = [regex]::Matches($extensionCode, $patch.Pattern)
    if ($matches.Count -gt 0) {
        $extensionCode = $extensionCode -replace $patch.Pattern, $patch.Replacement
        $patchCount += $matches.Count
        Write-Host "Patched credit system: $($matches.Count) replacements" -ForegroundColor Green
    }
}

# Write the patched file
Write-Host "Writing patched extension..." -ForegroundColor Yellow
try {
    Set-Content $extensionFile $extensionCode -Encoding UTF8
} catch {
    Write-Host "ERROR: Failed to write patched extension!" -ForegroundColor Red
    Write-Host $_.Exception.Message -ForegroundColor Red
    Read-Host "Press Enter to exit"
    exit 1
}

Write-Host ""
Write-Host "========================================" -ForegroundColor Cyan
Write-Host "   Patching Complete!" -ForegroundColor Cyan
Write-Host "========================================" -ForegroundColor Cyan
Write-Host ""
Write-Host "📊 Applied $patchCount patches total" -ForegroundColor Green
Write-Host ""
Write-Host "📋 What was changed:" -ForegroundColor Cyan
Write-Host "  ✅ Disabled telemetry and analytics calls" -ForegroundColor Green
Write-Host "  ✅ Blocked network requests to usage tracking endpoints" -ForegroundColor Green
Write-Host "  ✅ Added local credit system override" -ForegroundColor Green
Write-Host "  ✅ Kept credit display functionality" -ForegroundColor Green
Write-Host ""
Write-Host "⚠️  Important notes:" -ForegroundColor Yellow
Write-Host "  • Restart VS Code for changes to take effect" -ForegroundColor Yellow
Write-Host "  • Extension updates will overwrite these changes" -ForegroundColor Yellow
Write-Host "  • Backup saved at: $backupFile" -ForegroundColor Yellow
Write-Host "  • This may violate Augment terms of service" -ForegroundColor Yellow
Write-Host ""
Write-Host "🔄 To restore original extension:" -ForegroundColor Cyan
Write-Host "  .\restore-augment.ps1" -ForegroundColor Cyan
Write-Host ""

Read-Host "Press Enter to exit"
