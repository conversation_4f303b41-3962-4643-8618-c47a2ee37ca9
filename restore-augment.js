/**
 * <PERSON><PERSON><PERSON> to restore original Augment extension
 */

const fs = require('fs');
const path = require('path');
const os = require('os');

// Path to Augment extension
const AUGMENT_PATH = path.join(
  os.homedir(), 
  '.vscode', 
  'extensions', 
  'augment.vscode-augment-0.464.0'
);

const EXTENSION_JS_PATH = path.join(AUGMENT_PATH, 'out', 'extension.js');
const BACKUP_PATH = path.join(AUGMENT_PATH, 'out', 'extension.js.backup');

console.log('🔄 Augment Extension Restorer');
console.log('============================');

// Check if backup exists
if (!fs.existsSync(BACKUP_PATH)) {
  console.error('❌ Backup file not found at:', BACKUP_PATH);
  console.log('Cannot restore original extension.');
  process.exit(1);
}

// Restore from backup
console.log('📦 Restoring from backup...');
fs.copyFileSync(BACKUP_PATH, EXTENSION_JS_PATH);

console.log('✅ Original Augment extension restored!');
console.log('🔄 Please restart VS Code for changes to take effect.');
