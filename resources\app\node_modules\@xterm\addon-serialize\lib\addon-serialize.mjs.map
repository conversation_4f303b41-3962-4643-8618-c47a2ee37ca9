{"version": 3, "sources": ["../../../src/common/Color.ts", "../../../src/browser/Types.ts", "../src/SerializeAddon.ts"], "sourcesContent": ["/**\n * Copyright (c) 2019 The xterm.js authors. All rights reserved.\n * @license MIT\n */\n\nimport { IColor, IColorRGB } from 'common/Types';\n\nlet $r = 0;\nlet $g = 0;\nlet $b = 0;\nlet $a = 0;\n\nexport const NULL_COLOR: IColor = {\n  css: '#00000000',\n  rgba: 0\n};\n\n/**\n * Helper functions where the source type is \"channels\" (individual color channels as numbers).\n */\nexport namespace channels {\n  export function toCss(r: number, g: number, b: number, a?: number): string {\n    if (a !== undefined) {\n      return `#${toPaddedHex(r)}${toPaddedHex(g)}${toPaddedHex(b)}${toPaddedHex(a)}`;\n    }\n    return `#${toPaddedHex(r)}${toPaddedHex(g)}${toPaddedHex(b)}`;\n  }\n\n  export function toRgba(r: number, g: number, b: number, a: number = 0xFF): number {\n    // Note: The aggregated number is RGBA32 (BE), thus needs to be converted to ABGR32\n    // on LE systems, before it can be used for direct 32-bit buffer writes.\n    // >>> 0 forces an unsigned int\n    return (r << 24 | g << 16 | b << 8 | a) >>> 0;\n  }\n\n  export function toColor(r: number, g: number, b: number, a?: number): IColor {\n    return {\n      css: channels.toCss(r, g, b, a),\n      rgba: channels.toRgba(r, g, b, a)\n    };\n  }\n}\n\n/**\n * Helper functions where the source type is `IColor`.\n */\nexport namespace color {\n  export function blend(bg: IColor, fg: IColor): IColor {\n    $a = (fg.rgba & 0xFF) / 255;\n    if ($a === 1) {\n      return {\n        css: fg.css,\n        rgba: fg.rgba\n      };\n    }\n    const fgR = (fg.rgba >> 24) & 0xFF;\n    const fgG = (fg.rgba >> 16) & 0xFF;\n    const fgB = (fg.rgba >> 8) & 0xFF;\n    const bgR = (bg.rgba >> 24) & 0xFF;\n    const bgG = (bg.rgba >> 16) & 0xFF;\n    const bgB = (bg.rgba >> 8) & 0xFF;\n    $r = bgR + Math.round((fgR - bgR) * $a);\n    $g = bgG + Math.round((fgG - bgG) * $a);\n    $b = bgB + Math.round((fgB - bgB) * $a);\n    const css = channels.toCss($r, $g, $b);\n    const rgba = channels.toRgba($r, $g, $b);\n    return { css, rgba };\n  }\n\n  export function isOpaque(color: IColor): boolean {\n    return (color.rgba & 0xFF) === 0xFF;\n  }\n\n  export function ensureContrastRatio(bg: IColor, fg: IColor, ratio: number): IColor | undefined {\n    const result = rgba.ensureContrastRatio(bg.rgba, fg.rgba, ratio);\n    if (!result) {\n      return undefined;\n    }\n    return channels.toColor(\n      (result >> 24 & 0xFF),\n      (result >> 16 & 0xFF),\n      (result >> 8  & 0xFF)\n    );\n  }\n\n  export function opaque(color: IColor): IColor {\n    const rgbaColor = (color.rgba | 0xFF) >>> 0;\n    [$r, $g, $b] = rgba.toChannels(rgbaColor);\n    return {\n      css: channels.toCss($r, $g, $b),\n      rgba: rgbaColor\n    };\n  }\n\n  export function opacity(color: IColor, opacity: number): IColor {\n    $a = Math.round(opacity * 0xFF);\n    [$r, $g, $b] = rgba.toChannels(color.rgba);\n    return {\n      css: channels.toCss($r, $g, $b, $a),\n      rgba: channels.toRgba($r, $g, $b, $a)\n    };\n  }\n\n  export function multiplyOpacity(color: IColor, factor: number): IColor {\n    $a = color.rgba & 0xFF;\n    return opacity(color, ($a * factor) / 0xFF);\n  }\n\n  export function toColorRGB(color: IColor): IColorRGB {\n    return [(color.rgba >> 24) & 0xFF, (color.rgba >> 16) & 0xFF, (color.rgba >> 8) & 0xFF];\n  }\n}\n\n/**\n * Helper functions where the source type is \"css\" (string: '#rgb', '#rgba', '#rrggbb',\n * '#rrggbbaa').\n */\nexport namespace css {\n  // Attempt to set get the shared canvas context\n  let $ctx: CanvasRenderingContext2D | undefined;\n  let $litmusColor: CanvasGradient | undefined;\n  try {\n    // This is guaranteed to run in the first window, so document should be correct\n    const canvas = document.createElement('canvas');\n    canvas.width = 1;\n    canvas.height = 1;\n    const ctx = canvas.getContext('2d', {\n      willReadFrequently: true\n    });\n    if (ctx) {\n      $ctx = ctx;\n      $ctx.globalCompositeOperation = 'copy';\n      $litmusColor = $ctx.createLinearGradient(0, 0, 1, 1);\n    }\n  }\n  catch {\n    // noop\n  }\n\n  /**\n   * Converts a css string to an IColor, this should handle all valid CSS color strings and will\n   * throw if it's invalid. The ideal format to use is `#rrggbb[aa]` as it's the fastest to parse.\n   *\n   * Only `#rgb[a]`, `#rrggbb[aa]`, `rgb()` and `rgba()` formats are supported when run in a Node\n   * environment.\n   */\n  export function toColor(css: string): IColor {\n    // Formats: #rgb[a] and #rrggbb[aa]\n    if (css.match(/#[\\da-f]{3,8}/i)) {\n      switch (css.length) {\n        case 4: { // #rgb\n          $r = parseInt(css.slice(1, 2).repeat(2), 16);\n          $g = parseInt(css.slice(2, 3).repeat(2), 16);\n          $b = parseInt(css.slice(3, 4).repeat(2), 16);\n          return channels.toColor($r, $g, $b);\n        }\n        case 5: { // #rgba\n          $r = parseInt(css.slice(1, 2).repeat(2), 16);\n          $g = parseInt(css.slice(2, 3).repeat(2), 16);\n          $b = parseInt(css.slice(3, 4).repeat(2), 16);\n          $a = parseInt(css.slice(4, 5).repeat(2), 16);\n          return channels.toColor($r, $g, $b, $a);\n        }\n        case 7: // #rrggbb\n          return {\n            css,\n            rgba: (parseInt(css.slice(1), 16) << 8 | 0xFF) >>> 0\n          };\n        case 9: // #rrggbbaa\n          return {\n            css,\n            rgba: parseInt(css.slice(1), 16) >>> 0\n          };\n      }\n    }\n\n    // Formats: rgb() or rgba()\n    const rgbaMatch = css.match(/rgba?\\(\\s*(\\d{1,3})\\s*,\\s*(\\d{1,3})\\s*,\\s*(\\d{1,3})\\s*(,\\s*(0|1|\\d?\\.(\\d+))\\s*)?\\)/);\n    if (rgbaMatch) {\n      $r = parseInt(rgbaMatch[1]);\n      $g = parseInt(rgbaMatch[2]);\n      $b = parseInt(rgbaMatch[3]);\n      $a = Math.round((rgbaMatch[5] === undefined ? 1 : parseFloat(rgbaMatch[5])) * 0xFF);\n      return channels.toColor($r, $g, $b, $a);\n    }\n\n    // Validate the context is available for canvas-based color parsing\n    if (!$ctx || !$litmusColor) {\n      throw new Error('css.toColor: Unsupported css format');\n    }\n\n    // Validate the color using canvas fillStyle\n    // See https://html.spec.whatwg.org/multipage/canvas.html#fill-and-stroke-styles\n    $ctx.fillStyle = $litmusColor;\n    $ctx.fillStyle = css;\n    if (typeof $ctx.fillStyle !== 'string') {\n      throw new Error('css.toColor: Unsupported css format');\n    }\n\n    $ctx.fillRect(0, 0, 1, 1);\n    [$r, $g, $b, $a] = $ctx.getImageData(0, 0, 1, 1).data;\n\n    // Validate the color is non-transparent as color hue gets lost when drawn to the canvas\n    if ($a !== 0xFF) {\n      throw new Error('css.toColor: Unsupported css format');\n    }\n\n    // Extract the color from the canvas' fillStyle property which exposes the color value in rgba()\n    // format\n    // See https://html.spec.whatwg.org/multipage/canvas.html#serialisation-of-a-color\n    return {\n      rgba: channels.toRgba($r, $g, $b, $a),\n      css\n    };\n  }\n}\n\n/**\n * Helper functions where the source type is \"rgb\" (number: 0xrrggbb).\n */\nexport namespace rgb {\n  /**\n   * Gets the relative luminance of an RGB color, this is useful in determining the contrast ratio\n   * between two colors.\n   * @param rgb The color to use.\n   * @see https://www.w3.org/TR/WCAG20/#relativeluminancedef\n   */\n  export function relativeLuminance(rgb: number): number {\n    return relativeLuminance2(\n      (rgb >> 16) & 0xFF,\n      (rgb >> 8 ) & 0xFF,\n      (rgb      ) & 0xFF);\n  }\n\n  /**\n   * Gets the relative luminance of an RGB color, this is useful in determining the contrast ratio\n   * between two colors.\n   * @param r The red channel (0x00 to 0xFF).\n   * @param g The green channel (0x00 to 0xFF).\n   * @param b The blue channel (0x00 to 0xFF).\n   * @see https://www.w3.org/TR/WCAG20/#relativeluminancedef\n   */\n  export function relativeLuminance2(r: number, g: number, b: number): number {\n    const rs = r / 255;\n    const gs = g / 255;\n    const bs = b / 255;\n    const rr = rs <= 0.03928 ? rs / 12.92 : Math.pow((rs + 0.055) / 1.055, 2.4);\n    const rg = gs <= 0.03928 ? gs / 12.92 : Math.pow((gs + 0.055) / 1.055, 2.4);\n    const rb = bs <= 0.03928 ? bs / 12.92 : Math.pow((bs + 0.055) / 1.055, 2.4);\n    return rr * 0.2126 + rg * 0.7152 + rb * 0.0722;\n  }\n}\n\n/**\n * Helper functions where the source type is \"rgba\" (number: 0xrrggbbaa).\n */\nexport namespace rgba {\n  export function blend(bg: number, fg: number): number {\n    $a = (fg & 0xFF) / 0xFF;\n    if ($a === 1) {\n      return fg;\n    }\n    const fgR = (fg >> 24) & 0xFF;\n    const fgG = (fg >> 16) & 0xFF;\n    const fgB = (fg >> 8) & 0xFF;\n    const bgR = (bg >> 24) & 0xFF;\n    const bgG = (bg >> 16) & 0xFF;\n    const bgB = (bg >> 8) & 0xFF;\n    $r = bgR + Math.round((fgR - bgR) * $a);\n    $g = bgG + Math.round((fgG - bgG) * $a);\n    $b = bgB + Math.round((fgB - bgB) * $a);\n    return channels.toRgba($r, $g, $b);\n  }\n\n  /**\n   * Given a foreground color and a background color, either increase or reduce the luminance of the\n   * foreground color until the specified contrast ratio is met. If pure white or black is hit\n   * without the contrast ratio being met, go the other direction using the background color as the\n   * foreground color and take either the first or second result depending on which has the higher\n   * contrast ratio.\n   *\n   * `undefined` will be returned if the contrast ratio is already met.\n   *\n   * @param bgRgba The background color in rgba format.\n   * @param fgRgba The foreground color in rgba format.\n   * @param ratio The contrast ratio to achieve.\n   */\n  export function ensureContrastRatio(bgRgba: number, fgRgba: number, ratio: number): number | undefined {\n    const bgL = rgb.relativeLuminance(bgRgba >> 8);\n    const fgL = rgb.relativeLuminance(fgRgba >> 8);\n    const cr = contrastRatio(bgL, fgL);\n    if (cr < ratio) {\n      if (fgL < bgL) {\n        const resultA = reduceLuminance(bgRgba, fgRgba, ratio);\n        const resultARatio = contrastRatio(bgL, rgb.relativeLuminance(resultA >> 8));\n        if (resultARatio < ratio) {\n          const resultB = increaseLuminance(bgRgba, fgRgba, ratio);\n          const resultBRatio = contrastRatio(bgL, rgb.relativeLuminance(resultB >> 8));\n          return resultARatio > resultBRatio ? resultA : resultB;\n        }\n        return resultA;\n      }\n      const resultA = increaseLuminance(bgRgba, fgRgba, ratio);\n      const resultARatio = contrastRatio(bgL, rgb.relativeLuminance(resultA >> 8));\n      if (resultARatio < ratio) {\n        const resultB = reduceLuminance(bgRgba, fgRgba, ratio);\n        const resultBRatio = contrastRatio(bgL, rgb.relativeLuminance(resultB >> 8));\n        return resultARatio > resultBRatio ? resultA : resultB;\n      }\n      return resultA;\n    }\n    return undefined;\n  }\n\n  export function reduceLuminance(bgRgba: number, fgRgba: number, ratio: number): number {\n    // This is a naive but fast approach to reducing luminance as converting to\n    // HSL and back is expensive\n    const bgR = (bgRgba >> 24) & 0xFF;\n    const bgG = (bgRgba >> 16) & 0xFF;\n    const bgB = (bgRgba >>  8) & 0xFF;\n    let fgR = (fgRgba >> 24) & 0xFF;\n    let fgG = (fgRgba >> 16) & 0xFF;\n    let fgB = (fgRgba >>  8) & 0xFF;\n    let cr = contrastRatio(rgb.relativeLuminance2(fgR, fgG, fgB), rgb.relativeLuminance2(bgR, bgG, bgB));\n    while (cr < ratio && (fgR > 0 || fgG > 0 || fgB > 0)) {\n      // Reduce by 10% until the ratio is hit\n      fgR -= Math.max(0, Math.ceil(fgR * 0.1));\n      fgG -= Math.max(0, Math.ceil(fgG * 0.1));\n      fgB -= Math.max(0, Math.ceil(fgB * 0.1));\n      cr = contrastRatio(rgb.relativeLuminance2(fgR, fgG, fgB), rgb.relativeLuminance2(bgR, bgG, bgB));\n    }\n    return (fgR << 24 | fgG << 16 | fgB << 8 | 0xFF) >>> 0;\n  }\n\n  export function increaseLuminance(bgRgba: number, fgRgba: number, ratio: number): number {\n    // This is a naive but fast approach to increasing luminance as converting to\n    // HSL and back is expensive\n    const bgR = (bgRgba >> 24) & 0xFF;\n    const bgG = (bgRgba >> 16) & 0xFF;\n    const bgB = (bgRgba >>  8) & 0xFF;\n    let fgR = (fgRgba >> 24) & 0xFF;\n    let fgG = (fgRgba >> 16) & 0xFF;\n    let fgB = (fgRgba >>  8) & 0xFF;\n    let cr = contrastRatio(rgb.relativeLuminance2(fgR, fgG, fgB), rgb.relativeLuminance2(bgR, bgG, bgB));\n    while (cr < ratio && (fgR < 0xFF || fgG < 0xFF || fgB < 0xFF)) {\n      // Increase by 10% until the ratio is hit\n      fgR = Math.min(0xFF, fgR + Math.ceil((255 - fgR) * 0.1));\n      fgG = Math.min(0xFF, fgG + Math.ceil((255 - fgG) * 0.1));\n      fgB = Math.min(0xFF, fgB + Math.ceil((255 - fgB) * 0.1));\n      cr = contrastRatio(rgb.relativeLuminance2(fgR, fgG, fgB), rgb.relativeLuminance2(bgR, bgG, bgB));\n    }\n    return (fgR << 24 | fgG << 16 | fgB << 8 | 0xFF) >>> 0;\n  }\n\n  export function toChannels(value: number): [number, number, number, number] {\n    return [(value >> 24) & 0xFF, (value >> 16) & 0xFF, (value >> 8) & 0xFF, value & 0xFF];\n  }\n}\n\nexport function toPaddedHex(c: number): string {\n  const s = c.toString(16);\n  return s.length < 2 ? '0' + s : s;\n}\n\n/**\n * Gets the contrast ratio between two relative luminance values.\n * @param l1 The first relative luminance.\n * @param l2 The first relative luminance.\n * @see https://www.w3.org/TR/WCAG20/#contrast-ratiodef\n */\nexport function contrastRatio(l1: number, l2: number): number {\n  if (l1 < l2) {\n    return (l2 + 0.05) / (l1 + 0.05);\n  }\n  return (l1 + 0.05) / (l2 + 0.05);\n}\n", "/**\n * Copyright (c) 2017 The xterm.js authors. All rights reserved.\n * @license MIT\n */\n\nimport { CharData, IColor, ICoreTerminal, ITerminalOptions } from 'common/Types';\nimport { IBuffer } from 'common/buffer/Types';\nimport { IDisposable, Terminal as ITerminalApi } from '@xterm/xterm';\nimport { channels, css } from 'common/Color';\nimport type { Event } from 'vs/base/common/event';\n\n/**\n * A portion of the public API that are implemented identially internally and simply passed through.\n */\ntype InternalPassthroughApis = Omit<ITerminalApi, 'buffer' | 'parser' | 'unicode' | 'modes' | 'writeln' | 'loadAddon'>;\n\nexport interface ITerminal extends InternalPassthroughApis, ICoreTerminal {\n  screenElement: HTMLElement | undefined;\n  browser: IBrowser;\n  buffer: IBuffer;\n  linkifier: ILinkifier2 | undefined;\n  options: Required<ITerminalOptions>;\n\n  onBlur: Event<void>;\n  onFocus: Event<void>;\n  onA11yChar: Event<string>;\n  onA11yTab: Event<number>;\n  onWillOpen: Event<HTMLElement>;\n\n  cancel(ev: MouseEvent | WheelEvent | KeyboardEvent | InputEvent, force?: boolean): boolean | void;\n}\n\nexport type CustomKeyEventHandler = (event: KeyboardEvent) => boolean;\nexport type CustomWheelEventHandler = (event: WheelEvent) => boolean;\n\nexport type LineData = CharData[];\n\nexport interface ICompositionHelper {\n  readonly isComposing: boolean;\n  compositionstart(): void;\n  compositionupdate(ev: CompositionEvent): void;\n  compositionend(): void;\n  updateCompositionElements(dontRecurse?: boolean): void;\n  keydown(ev: KeyboardEvent): boolean;\n}\n\nexport interface IBrowser {\n  isNode: boolean;\n  userAgent: string;\n  platform: string;\n  isFirefox: boolean;\n  isMac: boolean;\n  isIpad: boolean;\n  isIphone: boolean;\n  isWindows: boolean;\n}\n\nexport interface IColorSet {\n  foreground: IColor;\n  background: IColor;\n  cursor: IColor;\n  cursorAccent: IColor;\n  selectionForeground: IColor | undefined;\n  selectionBackgroundTransparent: IColor;\n  /** The selection blended on top of background. */\n  selectionBackgroundOpaque: IColor;\n  selectionInactiveBackgroundTransparent: IColor;\n  selectionInactiveBackgroundOpaque: IColor;\n  scrollbarSliderBackground: IColor;\n  scrollbarSliderHoverBackground: IColor;\n  scrollbarSliderActiveBackground: IColor;\n  overviewRulerBorder: IColor;\n  ansi: IColor[];\n  /** Maps original colors to colors that respect minimum contrast ratio. */\n  contrastCache: IColorContrastCache;\n  /** Maps original colors to colors that respect _half_ of the minimum contrast ratio. */\n  halfContrastCache: IColorContrastCache;\n}\n\nexport type ReadonlyColorSet = Readonly<Omit<IColorSet, 'ansi'>> & { ansi: Readonly<Pick<IColorSet, 'ansi'>['ansi']> };\n\nexport interface IColorContrastCache {\n  clear(): void;\n  setCss(bg: number, fg: number, value: string | null): void;\n  getCss(bg: number, fg: number): string | null | undefined;\n  setColor(bg: number, fg: number, value: IColor | null): void;\n  getColor(bg: number, fg: number): IColor | null | undefined;\n}\n\nexport interface IPartialColorSet {\n  foreground: IColor;\n  background: IColor;\n  cursor?: IColor;\n  cursorAccent?: IColor;\n  selectionBackground?: IColor;\n  ansi: IColor[];\n}\n\nexport interface IViewport extends IDisposable {\n  scrollBarWidth: number;\n  readonly onRequestScrollLines: Event<{ amount: number, suppressScrollEvent: boolean }>;\n  syncScrollArea(immediate?: boolean, force?: boolean): void;\n  getLinesScrolled(ev: WheelEvent): number;\n  getBufferElements(startLine: number, endLine?: number): { bufferElements: HTMLElement[], cursorElement?: HTMLElement };\n  handleWheel(ev: WheelEvent): boolean;\n  handleTouchStart(ev: TouchEvent): void;\n  handleTouchMove(ev: TouchEvent): boolean;\n  scrollLines(disp: number): void;  // todo api name?\n  reset(): void;\n}\n\nexport interface ILinkifierEvent {\n  x1: number;\n  y1: number;\n  x2: number;\n  y2: number;\n  cols: number;\n  fg: number | undefined;\n}\n\ninterface ILinkState {\n  decorations: ILinkDecorations;\n  isHovered: boolean;\n}\nexport interface ILinkWithState {\n  link: ILink;\n  state?: ILinkState;\n}\n\nexport interface ILinkifier2 extends IDisposable {\n  onShowLinkUnderline: Event<ILinkifierEvent>;\n  onHideLinkUnderline: Event<ILinkifierEvent>;\n  readonly currentLink: ILinkWithState | undefined;\n}\n\nexport interface ILink {\n  range: IBufferRange;\n  text: string;\n  decorations?: ILinkDecorations;\n  activate(event: MouseEvent, text: string): void;\n  hover?(event: MouseEvent, text: string): void;\n  leave?(event: MouseEvent, text: string): void;\n  dispose?(): void;\n}\n\nexport interface ILinkDecorations {\n  pointerCursor: boolean;\n  underline: boolean;\n}\n\nexport interface IBufferRange {\n  start: IBufferCellPosition;\n  end: IBufferCellPosition;\n}\n\nexport interface IBufferCellPosition {\n  x: number;\n  y: number;\n}\n\nexport type CharacterJoinerHandler = (text: string) => [number, number][];\n\nexport interface ICharacterJoiner {\n  id: number;\n  handler: CharacterJoinerHandler;\n}\n\nexport interface IRenderDebouncer extends IDisposable {\n  refresh(rowStart: number | undefined, rowEnd: number | undefined, rowCount: number): void;\n}\n\nexport interface IRenderDebouncerWithCallback extends IRenderDebouncer {\n  addRefreshCallback(callback: FrameRequestCallback): number;\n}\n\nexport interface IBufferElementProvider {\n  provideBufferElements(): DocumentFragment | HTMLElement;\n}\n\n// An IIFE to generate DEFAULT_ANSI_COLORS.\nexport const DEFAULT_ANSI_COLORS = Object.freeze((() => {\n  const colors = [\n    // dark:\n    css.toColor('#2e3436'),\n    css.toColor('#cc0000'),\n    css.toColor('#4e9a06'),\n    css.toColor('#c4a000'),\n    css.toColor('#3465a4'),\n    css.toColor('#75507b'),\n    css.toColor('#06989a'),\n    css.toColor('#d3d7cf'),\n    // bright:\n    css.toColor('#555753'),\n    css.toColor('#ef2929'),\n    css.toColor('#8ae234'),\n    css.toColor('#fce94f'),\n    css.toColor('#729fcf'),\n    css.toColor('#ad7fa8'),\n    css.toColor('#34e2e2'),\n    css.toColor('#eeeeec')\n  ];\n\n  // Fill in the remaining 240 ANSI colors.\n  // Generate colors (16-231)\n  const v = [0x00, 0x5f, 0x87, 0xaf, 0xd7, 0xff];\n  for (let i = 0; i < 216; i++) {\n    const r = v[(i / 36) % 6 | 0];\n    const g = v[(i / 6) % 6 | 0];\n    const b = v[i % 6];\n    colors.push({\n      css: channels.toCss(r, g, b),\n      rgba: channels.toRgba(r, g, b)\n    });\n  }\n\n  // Generate greys (232-255)\n  for (let i = 0; i < 24; i++) {\n    const c = 8 + i * 10;\n    colors.push({\n      css: channels.toCss(c, c, c),\n      rgba: channels.toRgba(c, c, c)\n    });\n  }\n\n  return colors;\n})());\n", "/**\n * Copyright (c) 2019 The xterm.js authors. All rights reserved.\n * @license MIT\n *\n * (EXPERIMENTAL) This Addon is still under development\n */\n\nimport type { <PERSON><PERSON><PERSON><PERSON>, IBufferCell, IBufferRange, ITerminalAddon, Terminal } from '@xterm/xterm';\nimport type { IHTMLSerializeOptions, SerializeAddon as ISerializeApi, ISerializeOptions, ISerializeRange } from '@xterm/addon-serialize';\nimport { IAttributeData, IColor } from 'common/Types';\nimport { DEFAULT_ANSI_COLORS } from 'browser/Types';\n\nfunction constrain(value: number, low: number, high: number): number {\n  return Math.max(low, Math.min(value, high));\n}\n\nfunction escapeHTMLChar(c: string): string {\n  switch (c) {\n    case '&': return '&amp;';\n    case '<': return '&lt;';\n  }\n  return c;\n}\n\n// TODO: Refine this template class later\nabstract class BaseSerializeHandler {\n  constructor(\n    protected readonly _buffer: IBuffer\n  ) {\n  }\n\n  public serialize(range: IBufferRange, excludeFinalCursorPosition?: boolean): string {\n    // we need two of them to flip between old and new cell\n    const cell1 = this._buffer.getNullCell();\n    const cell2 = this._buffer.getNullCell();\n    let oldCell = cell1;\n\n    const startRow = range.start.y;\n    const endRow = range.end.y;\n    const startColumn = range.start.x;\n    const endColumn = range.end.x;\n\n    this._beforeSerialize(endRow - startRow, startRow, endRow);\n\n    for (let row = startRow; row <= endRow; row++) {\n      const line = this._buffer.getLine(row);\n      if (line) {\n        const startLineColumn = row === range.start.y ? startColumn : 0;\n        const endLineColumn = row === range.end.y ? endColumn: line.length;\n        for (let col = startLineColumn; col < endLineColumn; col++) {\n          const c = line.getCell(col, oldCell === cell1 ? cell2 : cell1);\n          if (!c) {\n            console.warn(`Can't get cell at row=${row}, col=${col}`);\n            continue;\n          }\n          this._nextCell(c, oldCell, row, col);\n          oldCell = c;\n        }\n      }\n      this._rowEnd(row, row === endRow);\n    }\n\n    this._afterSerialize();\n\n    return this._serializeString(excludeFinalCursorPosition);\n  }\n\n  protected _nextCell(cell: IBufferCell, oldCell: IBufferCell, row: number, col: number): void { }\n  protected _rowEnd(row: number, isLastRow: boolean): void { }\n  protected _beforeSerialize(rows: number, startRow: number, endRow: number): void { }\n  protected _afterSerialize(): void { }\n  protected _serializeString(excludeFinalCursorPosition?: boolean): string { return ''; }\n}\n\nfunction equalFg(cell1: IBufferCell | IAttributeData, cell2: IBufferCell): boolean {\n  return cell1.getFgColorMode() === cell2.getFgColorMode()\n    && cell1.getFgColor() === cell2.getFgColor();\n}\n\nfunction equalBg(cell1: IBufferCell | IAttributeData, cell2: IBufferCell): boolean {\n  return cell1.getBgColorMode() === cell2.getBgColorMode()\n    && cell1.getBgColor() === cell2.getBgColor();\n}\n\nfunction equalFlags(cell1: IBufferCell | IAttributeData, cell2: IBufferCell): boolean {\n  return cell1.isInverse() === cell2.isInverse()\n    && cell1.isBold() === cell2.isBold()\n    && cell1.isUnderline() === cell2.isUnderline()\n    && cell1.isOverline() === cell2.isOverline()\n    && cell1.isBlink() === cell2.isBlink()\n    && cell1.isInvisible() === cell2.isInvisible()\n    && cell1.isItalic() === cell2.isItalic()\n    && cell1.isDim() === cell2.isDim()\n    && cell1.isStrikethrough() === cell2.isStrikethrough();\n}\n\nclass StringSerializeHandler extends BaseSerializeHandler {\n  private _rowIndex: number = 0;\n  private _allRows: string[] = new Array<string>();\n  private _allRowSeparators: string[] = new Array<string>();\n  private _currentRow: string = '';\n  private _nullCellCount: number = 0;\n\n  // we can see a full colored cell and a null cell that only have background the same style\n  // but the information isn't preserved by null cell itself\n  // so wee need to record it when required.\n  private _cursorStyle: IBufferCell = this._buffer.getNullCell();\n\n  // where exact the cursor styles comes from\n  // because we can't copy the cell directly\n  // so we remember where the content comes from instead\n  private _cursorStyleRow: number = 0;\n  private _cursorStyleCol: number = 0;\n\n  // this is a null cell for reference for checking whether background is empty or not\n  private _backgroundCell: IBufferCell = this._buffer.getNullCell();\n\n  private _firstRow: number = 0;\n  private _lastCursorRow: number = 0;\n  private _lastCursorCol: number = 0;\n  private _lastContentCursorRow: number = 0;\n  private _lastContentCursorCol: number = 0;\n\n  constructor(\n    buffer: IBuffer,\n    private readonly _terminal: Terminal\n  ) {\n    super(buffer);\n  }\n\n  protected _beforeSerialize(rows: number, start: number, end: number): void {\n    this._allRows = new Array<string>(rows);\n    this._lastContentCursorRow = start;\n    this._lastCursorRow = start;\n    this._firstRow = start;\n  }\n\n  private _thisRowLastChar: IBufferCell = this._buffer.getNullCell();\n  private _thisRowLastSecondChar: IBufferCell = this._buffer.getNullCell();\n  private _nextRowFirstChar: IBufferCell = this._buffer.getNullCell();\n  protected _rowEnd(row: number, isLastRow: boolean): void {\n    // if there is colorful empty cell at line end, whe must pad it back, or the the color block\n    // will missing\n    if (this._nullCellCount > 0 && !equalBg(this._cursorStyle, this._backgroundCell)) {\n      // use clear right to set background.\n      this._currentRow += `\\u001b[${this._nullCellCount}X`;\n    }\n\n    let rowSeparator = '';\n\n    // handle row separator\n    if (!isLastRow) {\n      // Enable BCE\n      if (row - this._firstRow >= this._terminal.rows) {\n        this._buffer.getLine(this._cursorStyleRow)?.getCell(this._cursorStyleCol, this._backgroundCell);\n      }\n\n      // Fetch current line\n      const currentLine = this._buffer.getLine(row)!;\n      // Fetch next line\n      const nextLine = this._buffer.getLine(row + 1)!;\n\n      if (!nextLine.isWrapped) {\n        // just insert the line break\n        rowSeparator = '\\r\\n';\n        // we sended the enter\n        this._lastCursorRow = row + 1;\n        this._lastCursorCol = 0;\n      } else {\n        rowSeparator = '';\n        const thisRowLastChar = currentLine.getCell(currentLine.length - 1, this._thisRowLastChar)!;\n        const thisRowLastSecondChar = currentLine.getCell(currentLine.length - 2, this._thisRowLastSecondChar)!;\n        const nextRowFirstChar = nextLine.getCell(0, this._nextRowFirstChar)!;\n        const isNextRowFirstCharDoubleWidth = nextRowFirstChar.getWidth() > 1;\n\n        // validate whether this line wrap is ever possible\n        // which mean whether cursor can placed at a overflow position (x === row) naturally\n        let isValid = false;\n\n        if (\n          // you must output character to cause overflow, control sequence can't do this\n          nextRowFirstChar.getChars() &&\n            isNextRowFirstCharDoubleWidth ? this._nullCellCount <= 1 : this._nullCellCount <= 0\n        ) {\n          if (\n            // the last character can't be null,\n            // you can't use control sequence to move cursor to (x === row)\n            (thisRowLastChar.getChars() || thisRowLastChar.getWidth() === 0) &&\n            // change background of the first wrapped cell also affects BCE\n            // so we mark it as invalid to simply the process to determine line separator\n            equalBg(thisRowLastChar, nextRowFirstChar)\n          ) {\n            isValid = true;\n          }\n\n          if (\n            // the second to last character can't be null if the next line starts with CJK,\n            // you can't use control sequence to move cursor to (x === row)\n            isNextRowFirstCharDoubleWidth &&\n            (thisRowLastSecondChar.getChars() || thisRowLastSecondChar.getWidth() === 0) &&\n            // change background of the first wrapped cell also affects BCE\n            // so we mark it as invalid to simply the process to determine line separator\n            equalBg(thisRowLastChar, nextRowFirstChar) &&\n            equalBg(thisRowLastSecondChar, nextRowFirstChar)\n          ) {\n            isValid = true;\n          }\n        }\n\n        if (!isValid) {\n          // force the wrap with magic\n          // insert enough character to force the wrap\n          rowSeparator = '-'.repeat(this._nullCellCount + 1);\n          // move back and erase next line head\n          rowSeparator += '\\u001b[1D\\u001b[1X';\n\n          if (this._nullCellCount > 0) {\n            // do these because we filled the last several null slot, which we shouldn't\n            rowSeparator += '\\u001b[A';\n            rowSeparator += `\\u001b[${currentLine.length - this._nullCellCount}C`;\n            rowSeparator += `\\u001b[${this._nullCellCount}X`;\n            rowSeparator += `\\u001b[${currentLine.length - this._nullCellCount}D`;\n            rowSeparator += '\\u001b[B';\n          }\n\n          // This is content and need the be serialized even it is invisible.\n          // without this, wrap will be missing from outputs.\n          this._lastContentCursorRow = row + 1;\n          this._lastContentCursorCol = 0;\n\n          // force commit the cursor position\n          this._lastCursorRow = row + 1;\n          this._lastCursorCol = 0;\n        }\n      }\n    }\n\n    this._allRows[this._rowIndex] = this._currentRow;\n    this._allRowSeparators[this._rowIndex++] = rowSeparator;\n    this._currentRow = '';\n    this._nullCellCount = 0;\n  }\n\n  private _diffStyle(cell: IBufferCell | IAttributeData, oldCell: IBufferCell): number[] {\n    const sgrSeq: number[] = [];\n    const fgChanged = !equalFg(cell, oldCell);\n    const bgChanged = !equalBg(cell, oldCell);\n    const flagsChanged = !equalFlags(cell, oldCell);\n\n    if (fgChanged || bgChanged || flagsChanged) {\n      if (cell.isAttributeDefault()) {\n        if (!oldCell.isAttributeDefault()) {\n          sgrSeq.push(0);\n        }\n      } else {\n        if (fgChanged) {\n          const color = cell.getFgColor();\n          if (cell.isFgRGB()) { sgrSeq.push(38, 2, (color >>> 16) & 0xFF, (color >>> 8) & 0xFF, color & 0xFF); }\n          else if (cell.isFgPalette()) {\n            if (color >= 16) { sgrSeq.push(38, 5, color); }\n            else { sgrSeq.push(color & 8 ? 90 + (color & 7) : 30 + (color & 7)); }\n          }\n          else { sgrSeq.push(39); }\n        }\n        if (bgChanged) {\n          const color = cell.getBgColor();\n          if (cell.isBgRGB()) { sgrSeq.push(48, 2, (color >>> 16) & 0xFF, (color >>> 8) & 0xFF, color & 0xFF); }\n          else if (cell.isBgPalette()) {\n            if (color >= 16) { sgrSeq.push(48, 5, color); }\n            else { sgrSeq.push(color & 8 ? 100 + (color & 7) : 40 + (color & 7)); }\n          }\n          else { sgrSeq.push(49); }\n        }\n        if (flagsChanged) {\n          if (cell.isInverse() !== oldCell.isInverse()) { sgrSeq.push(cell.isInverse() ? 7 : 27); }\n          if (cell.isBold() !== oldCell.isBold()) { sgrSeq.push(cell.isBold() ? 1 : 22); }\n          if (cell.isUnderline() !== oldCell.isUnderline()) { sgrSeq.push(cell.isUnderline() ? 4 : 24); }\n          if (cell.isOverline() !== oldCell.isOverline()) { sgrSeq.push(cell.isOverline() ? 53 : 55); }\n          if (cell.isBlink() !== oldCell.isBlink()) { sgrSeq.push(cell.isBlink() ? 5 : 25); }\n          if (cell.isInvisible() !== oldCell.isInvisible()) { sgrSeq.push(cell.isInvisible() ? 8 : 28); }\n          if (cell.isItalic() !== oldCell.isItalic()) { sgrSeq.push(cell.isItalic() ? 3 : 23); }\n          if (cell.isDim() !== oldCell.isDim()) { sgrSeq.push(cell.isDim() ? 2 : 22); }\n          if (cell.isStrikethrough() !== oldCell.isStrikethrough()) { sgrSeq.push(cell.isStrikethrough() ? 9 : 29); }\n        }\n      }\n    }\n\n    return sgrSeq;\n  }\n\n  protected _nextCell(cell: IBufferCell, oldCell: IBufferCell, row: number, col: number): void {\n    // a width 0 cell don't need to be count because it is just a placeholder after a CJK character;\n    const isPlaceHolderCell = cell.getWidth() === 0;\n\n    if (isPlaceHolderCell) {\n      return;\n    }\n\n    // this cell don't have content\n    const isEmptyCell = cell.getChars() === '';\n\n    const sgrSeq = this._diffStyle(cell, this._cursorStyle);\n\n    // the empty cell style is only assumed to be changed when background changed, because\n    // foreground is always 0.\n    const styleChanged = isEmptyCell ? !equalBg(this._cursorStyle, cell) : sgrSeq.length > 0;\n\n    /**\n     *  handles style change\n     */\n    if (styleChanged) {\n      // before update the style, we need to fill empty cell back\n      if (this._nullCellCount > 0) {\n        // use clear right to set background.\n        if (!equalBg(this._cursorStyle, this._backgroundCell)) {\n          this._currentRow += `\\u001b[${this._nullCellCount}X`;\n        }\n        // use move right to move cursor.\n        this._currentRow += `\\u001b[${this._nullCellCount}C`;\n        this._nullCellCount = 0;\n      }\n\n      this._lastContentCursorRow = this._lastCursorRow = row;\n      this._lastContentCursorCol = this._lastCursorCol = col;\n\n      this._currentRow += `\\u001b[${sgrSeq.join(';')}m`;\n\n      // update the last cursor style\n      const line = this._buffer.getLine(row);\n      if (line !== undefined) {\n        line.getCell(col, this._cursorStyle);\n        this._cursorStyleRow = row;\n        this._cursorStyleCol = col;\n      }\n    }\n\n    /**\n     *  handles actual content\n     */\n    if (isEmptyCell) {\n      this._nullCellCount += cell.getWidth();\n    } else {\n      if (this._nullCellCount > 0) {\n        // we can just assume we have same style with previous one here\n        // because style change is handled by previous stage\n        // use move right when background is empty, use clear right when there is background.\n        if (equalBg(this._cursorStyle, this._backgroundCell)) {\n          this._currentRow += `\\u001b[${this._nullCellCount}C`;\n        } else {\n          this._currentRow += `\\u001b[${this._nullCellCount}X`;\n          this._currentRow += `\\u001b[${this._nullCellCount}C`;\n        }\n        this._nullCellCount = 0;\n      }\n\n      this._currentRow += cell.getChars();\n\n      // update cursor\n      this._lastContentCursorRow = this._lastCursorRow = row;\n      this._lastContentCursorCol = this._lastCursorCol = col + cell.getWidth();\n    }\n  }\n\n  protected _serializeString(excludeFinalCursorPosition: boolean): string {\n    let rowEnd = this._allRows.length;\n\n    // the fixup is only required for data without scrollback\n    // because it will always be placed at last line otherwise\n    if (this._buffer.length - this._firstRow <= this._terminal.rows) {\n      rowEnd = this._lastContentCursorRow + 1 - this._firstRow;\n      this._lastCursorCol = this._lastContentCursorCol;\n      this._lastCursorRow = this._lastContentCursorRow;\n    }\n\n    let content = '';\n\n    for (let i = 0; i < rowEnd; i++) {\n      content += this._allRows[i];\n      if (i + 1 < rowEnd) {\n        content += this._allRowSeparators[i];\n      }\n    }\n\n    // restore the cursor\n    if (!excludeFinalCursorPosition) {\n      const realCursorRow = this._buffer.baseY + this._buffer.cursorY;\n      const realCursorCol = this._buffer.cursorX;\n\n      const cursorMoved = (realCursorRow !== this._lastCursorRow || realCursorCol !== this._lastCursorCol);\n\n      const moveRight = (offset: number): void => {\n        if (offset > 0) {\n          content += `\\u001b[${offset}C`;\n        } else if (offset < 0) {\n          content += `\\u001b[${-offset}D`;\n        }\n      };\n      const moveDown = (offset: number): void => {\n        if (offset > 0) {\n          content += `\\u001b[${offset}B`;\n        } else if (offset < 0) {\n          content += `\\u001b[${-offset}A`;\n        }\n      };\n\n      if (cursorMoved) {\n        moveDown(realCursorRow - this._lastCursorRow);\n        moveRight(realCursorCol - this._lastCursorCol);\n      }\n    }\n\n    // Restore the cursor's current style, see https://github.com/xtermjs/xterm.js/issues/3677\n    // HACK: Internal API access since it's awkward to expose this in the API and serialize will\n    // likely be the only consumer\n    const curAttrData: IAttributeData = (this._terminal as any)._core._inputHandler._curAttrData;\n    const sgrSeq = this._diffStyle(curAttrData, this._cursorStyle);\n    if (sgrSeq.length > 0) {\n      content += `\\u001b[${sgrSeq.join(';')}m`;\n    }\n\n    return content;\n  }\n}\n\nexport class SerializeAddon implements ITerminalAddon , ISerializeApi {\n  private _terminal: Terminal | undefined;\n\n  public activate(terminal: Terminal): void {\n    this._terminal = terminal;\n  }\n\n  private _serializeBufferByScrollback(terminal: Terminal, buffer: IBuffer, scrollback?: number): string {\n    const maxRows = buffer.length;\n    const correctRows = (scrollback === undefined) ? maxRows : constrain(scrollback + terminal.rows, 0, maxRows);\n    return this._serializeBufferByRange(terminal, buffer, {\n      start: maxRows - correctRows,\n      end: maxRows - 1\n    }, false);\n  }\n\n  private _serializeBufferByRange(terminal: Terminal, buffer: IBuffer, range: ISerializeRange, excludeFinalCursorPosition: boolean): string {\n    const handler = new StringSerializeHandler(buffer, terminal);\n    return handler.serialize({\n      start: { x: 0,             y: typeof range.start === 'number' ? range.start : range.start.line },\n      end:   { x: terminal.cols, y: typeof range.end   === 'number' ? range.end   : range.end.line   }\n    }, excludeFinalCursorPosition);\n  }\n\n  private _serializeBufferAsHTML(terminal: Terminal, options: Partial<IHTMLSerializeOptions>): string {\n    const buffer = terminal.buffer.active;\n    const handler = new HTMLSerializeHandler(buffer, terminal, options);\n    const onlySelection = options.onlySelection ?? false;\n    if (!onlySelection) {\n      const maxRows = buffer.length;\n      const scrollback = options.scrollback;\n      const correctRows = (scrollback === undefined) ? maxRows : constrain(scrollback + terminal.rows, 0, maxRows);\n      return handler.serialize({\n        start: { x: 0,             y: maxRows - correctRows },\n        end:   { x: terminal.cols, y: maxRows - 1           }\n      });\n    }\n\n    const selection = this._terminal?.getSelectionPosition();\n    if (selection !== undefined) {\n      return handler.serialize({\n        start: { x: selection.start.x, y: selection.start.y },\n        end:   { x: selection.end.x,   y: selection.end.y   }\n      });\n    }\n\n    return '';\n  }\n\n  private _serializeModes(terminal: Terminal): string {\n    let content = '';\n    const modes = terminal.modes;\n\n    // Default: false\n    if (modes.applicationCursorKeysMode) content += '\\x1b[?1h';\n    if (modes.applicationKeypadMode) content += '\\x1b[?66h';\n    if (modes.bracketedPasteMode) content += '\\x1b[?2004h';\n    if (modes.insertMode) content += '\\x1b[4h';\n    if (modes.originMode) content += '\\x1b[?6h';\n    if (modes.reverseWraparoundMode) content += '\\x1b[?45h';\n    if (modes.sendFocusMode) content += '\\x1b[?1004h';\n\n    // Default: true\n    if (modes.wraparoundMode === false) content += '\\x1b[?7l';\n\n    // Default: 'none'\n    if (modes.mouseTrackingMode !== 'none') {\n      switch (modes.mouseTrackingMode) {\n        case 'x10': content += '\\x1b[?9h'; break;\n        case 'vt200': content += '\\x1b[?1000h'; break;\n        case 'drag': content += '\\x1b[?1002h'; break;\n        case 'any': content += '\\x1b[?1003h'; break;\n      }\n    }\n\n    return content;\n  }\n\n  public serialize(options?: ISerializeOptions): string {\n    // TODO: Add combinedData support\n    if (!this._terminal) {\n      throw new Error('Cannot use addon until it has been loaded');\n    }\n\n    // Normal buffer\n    let content = options?.range\n      ? this._serializeBufferByRange(this._terminal, this._terminal.buffer.normal, options.range, true)\n      : this._serializeBufferByScrollback(this._terminal, this._terminal.buffer.normal, options?.scrollback);\n\n    // Alternate buffer\n    if (!options?.excludeAltBuffer) {\n      if (this._terminal.buffer.active.type === 'alternate') {\n        const alternativeScreenContent = this._serializeBufferByScrollback(this._terminal, this._terminal.buffer.alternate, undefined);\n        content += `\\u001b[?1049h\\u001b[H${alternativeScreenContent}`;\n      }\n    }\n\n    // Modes\n    if (!options?.excludeModes) {\n      content += this._serializeModes(this._terminal);\n    }\n\n    return content;\n  }\n\n  public serializeAsHTML(options?: Partial<IHTMLSerializeOptions>): string {\n    if (!this._terminal) {\n      throw new Error('Cannot use addon until it has been loaded');\n    }\n\n    return this._serializeBufferAsHTML(this._terminal, options || {});\n  }\n\n  public dispose(): void { }\n}\n\nexport class HTMLSerializeHandler extends BaseSerializeHandler {\n  private _currentRow: string = '';\n\n  private _htmlContent = '';\n\n  private _ansiColors: Readonly<IColor[]>;\n\n  constructor(\n    buffer: IBuffer,\n    private readonly _terminal: Terminal,\n    private readonly _options: Partial<IHTMLSerializeOptions>\n  ) {\n    super(buffer);\n\n    // For xterm headless: fallback to ansi colors\n    if ((_terminal as any)._core._themeService) {\n      this._ansiColors = (_terminal as any)._core._themeService.colors.ansi;\n    }\n    else {\n      this._ansiColors = DEFAULT_ANSI_COLORS;\n    }\n  }\n\n  private _padStart(target: string, targetLength: number, padString: string): string {\n    targetLength = targetLength >> 0;\n    padString = padString ?? ' ';\n    if (target.length > targetLength) {\n      return target;\n    }\n\n    targetLength -= target.length;\n    if (targetLength > padString.length) {\n      padString += padString.repeat(targetLength / padString.length);\n    }\n    return padString.slice(0, targetLength) + target;\n  }\n\n  protected _beforeSerialize(rows: number, start: number, end: number): void {\n    this._htmlContent += '<html><body><!--StartFragment--><pre>';\n\n    let foreground = '#000000';\n    let background = '#ffffff';\n    if (this._options.includeGlobalBackground ?? false) {\n      foreground = this._terminal.options.theme?.foreground ?? '#ffffff';\n      background = this._terminal.options.theme?.background ?? '#000000';\n    }\n\n    const globalStyleDefinitions = [];\n    globalStyleDefinitions.push('color: ' + foreground + ';');\n    globalStyleDefinitions.push('background-color: ' + background + ';');\n    globalStyleDefinitions.push('font-family: ' + this._terminal.options.fontFamily + ';');\n    globalStyleDefinitions.push('font-size: ' + this._terminal.options.fontSize + 'px;');\n    this._htmlContent += '<div style=\\'' + globalStyleDefinitions.join(' ') + '\\'>';\n  }\n\n  protected _afterSerialize(): void {\n    this._htmlContent += '</div>';\n    this._htmlContent += '</pre><!--EndFragment--></body></html>';\n  }\n\n  protected _rowEnd(row: number, isLastRow: boolean): void {\n    this._htmlContent += '<div><span>' + this._currentRow + '</span></div>';\n    this._currentRow = '';\n  }\n\n  private _getHexColor(cell: IBufferCell, isFg: boolean): string | undefined {\n    const color = isFg ? cell.getFgColor() : cell.getBgColor();\n    if (isFg ? cell.isFgRGB() : cell.isBgRGB()) {\n      const rgb = [\n        (color >> 16) & 255,\n        (color >>  8) & 255,\n        (color      ) & 255\n      ];\n      return '#' + rgb.map(x => this._padStart(x.toString(16), 2, '0')).join('');\n    }\n    if (isFg ? cell.isFgPalette() : cell.isBgPalette()) {\n      return this._ansiColors[color].css;\n    }\n    return undefined;\n  }\n\n  private _diffStyle(cell: IBufferCell, oldCell: IBufferCell): string[] | undefined {\n    const content: string[] = [];\n\n    const fgChanged = !equalFg(cell, oldCell);\n    const bgChanged = !equalBg(cell, oldCell);\n    const flagsChanged = !equalFlags(cell, oldCell);\n\n    if (fgChanged || bgChanged || flagsChanged) {\n      const fgHexColor = this._getHexColor(cell, true);\n      if (fgHexColor) {\n        content.push('color: ' + fgHexColor + ';');\n      }\n\n      const bgHexColor = this._getHexColor(cell, false);\n      if (bgHexColor) {\n        content.push('background-color: ' + bgHexColor + ';');\n      }\n\n      if (cell.isInverse()) { content.push('color: #000000; background-color: #BFBFBF;'); }\n      if (cell.isBold()) { content.push('font-weight: bold;'); }\n      if (cell.isUnderline() && cell.isOverline()) { content.push('text-decoration: overline underline;'); }\n      else if (cell.isUnderline()) { content.push('text-decoration: underline;'); }\n      else if (cell.isOverline()) { content.push('text-decoration: overline;'); }\n      if (cell.isBlink()) { content.push('text-decoration: blink;'); }\n      if (cell.isInvisible()) { content.push('visibility: hidden;'); }\n      if (cell.isItalic()) { content.push('font-style: italic;'); }\n      if (cell.isDim()) { content.push('opacity: 0.5;'); }\n      if (cell.isStrikethrough()) { content.push('text-decoration: line-through;'); }\n\n      return content;\n    }\n\n    return undefined;\n  }\n\n  protected _nextCell(cell: IBufferCell, oldCell: IBufferCell, row: number, col: number): void {\n    // a width 0 cell don't need to be count because it is just a placeholder after a CJK character;\n    const isPlaceHolderCell = cell.getWidth() === 0;\n    if (isPlaceHolderCell) {\n      return;\n    }\n\n    // this cell don't have content\n    const isEmptyCell = cell.getChars() === '';\n\n    const styleDefinitions = this._diffStyle(cell, oldCell);\n\n    // handles style change\n    if (styleDefinitions) {\n      this._currentRow += styleDefinitions.length === 0 ?\n        '</span><span>' :\n        '</span><span style=\\'' + styleDefinitions.join(' ') + '\\'>';\n    }\n\n    // handles actual content\n    if (isEmptyCell) {\n      this._currentRow += ' ';\n    } else {\n      this._currentRow += escapeHTMLChar(cell.getChars());\n    }\n  }\n\n  protected _serializeString(): string {\n    return this._htmlContent;\n  }\n}\n"], "mappings": ";;;;;;;;;;;;;;;;AAOA,IAAIA,EAAK,EACLC,EAAK,EACLC,EAAK,EACLC,EAAK,EAUF,IAAUC,MAAV,CACE,SAASC,EAAMC,EAAWC,EAAWC,EAAWC,EAAoB,CACzE,OAAIA,IAAM,OACD,IAAIC,EAAYJ,CAAC,CAAC,GAAGI,EAAYH,CAAC,CAAC,GAAGG,EAAYF,CAAC,CAAC,GAAGE,EAAYD,CAAC,CAAC,GAEvE,IAAIC,EAAYJ,CAAC,CAAC,GAAGI,EAAYH,CAAC,CAAC,GAAGG,EAAYF,CAAC,CAAC,EAC7D,CALOJ,EAAS,MAAAC,EAOT,SAASM,EAAOL,EAAWC,EAAWC,EAAWC,EAAY,IAAc,CAIhF,OAAQH,GAAK,GAAKC,GAAK,GAAKC,GAAK,EAAIC,KAAO,CAC9C,CALOL,EAAS,OAAAO,EAOT,SAASC,EAAQN,EAAWC,EAAWC,EAAWC,EAAoB,CAC3E,MAAO,CACL,IAAKL,EAAS,MAAME,EAAGC,EAAGC,EAAGC,CAAC,EAC9B,KAAML,EAAS,OAAOE,EAAGC,EAAGC,EAAGC,CAAC,CAClC,CACF,CALOL,EAAS,QAAAQ,IAfDR,IAAA,IA0BV,IAAUS,MAAV,CACE,SAASC,EAAMC,EAAYC,EAAoB,CAEpD,GADAC,GAAMD,EAAG,KAAO,KAAQ,IACpBC,IAAO,EACT,MAAO,CACL,IAAKD,EAAG,IACR,KAAMA,EAAG,IACX,EAEF,IAAME,EAAOF,EAAG,MAAQ,GAAM,IACxBG,EAAOH,EAAG,MAAQ,GAAM,IACxBI,EAAOJ,EAAG,MAAQ,EAAK,IACvBK,EAAON,EAAG,MAAQ,GAAM,IACxBO,EAAOP,EAAG,MAAQ,GAAM,IACxBQ,EAAOR,EAAG,MAAQ,EAAK,IAC7BS,EAAKH,EAAM,KAAK,OAAOH,EAAMG,GAAOJ,CAAE,EACtCQ,EAAKH,EAAM,KAAK,OAAOH,EAAMG,GAAOL,CAAE,EACtCS,EAAKH,EAAM,KAAK,OAAOH,EAAMG,GAAON,CAAE,EACtC,IAAMU,EAAMvB,EAAS,MAAMoB,EAAIC,EAAIC,CAAE,EAC/BE,EAAOxB,EAAS,OAAOoB,EAAIC,EAAIC,CAAE,EACvC,MAAO,CAAE,IAAAC,EAAK,KAAAC,CAAK,CACrB,CApBOf,EAAS,MAAAC,EAsBT,SAASe,EAAShB,EAAwB,CAC/C,OAAQA,EAAM,KAAO,OAAU,GACjC,CAFOA,EAAS,SAAAgB,EAIT,SAASC,EAAoBf,EAAYC,EAAYe,EAAmC,CAC7F,IAAMC,EAASJ,EAAK,oBAAoBb,EAAG,KAAMC,EAAG,KAAMe,CAAK,EAC/D,GAAKC,EAGL,OAAO5B,EAAS,QACb4B,GAAU,GAAK,IACfA,GAAU,GAAK,IACfA,GAAU,EAAK,GAClB,CACF,CAVOnB,EAAS,oBAAAiB,EAYT,SAASG,EAAOpB,EAAuB,CAC5C,IAAMqB,GAAarB,EAAM,KAAO,OAAU,EAC1C,OAACW,EAAIC,EAAIC,CAAE,EAAIE,EAAK,WAAWM,CAAS,EACjC,CACL,IAAK9B,EAAS,MAAMoB,EAAIC,EAAIC,CAAE,EAC9B,KAAMQ,CACR,CACF,CAPOrB,EAAS,OAAAoB,EAST,SAASE,EAAQtB,EAAesB,EAAyB,CAC9D,OAAAlB,EAAK,KAAK,MAAMkB,EAAU,GAAI,EAC9B,CAACX,EAAIC,EAAIC,CAAE,EAAIE,EAAK,WAAWf,EAAM,IAAI,EAClC,CACL,IAAKT,EAAS,MAAMoB,EAAIC,EAAIC,EAAIT,CAAE,EAClC,KAAMb,EAAS,OAAOoB,EAAIC,EAAIC,EAAIT,CAAE,CACtC,CACF,CAPOJ,EAAS,QAAAsB,EAST,SAASC,EAAgBvB,EAAewB,EAAwB,CACrE,OAAApB,EAAKJ,EAAM,KAAO,IACXsB,EAAQtB,EAAQI,EAAKoB,EAAU,GAAI,CAC5C,CAHOxB,EAAS,gBAAAuB,EAKT,SAASE,EAAWzB,EAA0B,CACnD,MAAO,CAAEA,EAAM,MAAQ,GAAM,IAAOA,EAAM,MAAQ,GAAM,IAAOA,EAAM,MAAQ,EAAK,GAAI,CACxF,CAFOA,EAAS,WAAAyB,IA9DDzB,IAAA,IAuEV,IAAUc,MAAV,CAEL,IAAIY,EACAC,EACJ,GAAI,CAEF,IAAMC,EAAS,SAAS,cAAc,QAAQ,EAC9CA,EAAO,MAAQ,EACfA,EAAO,OAAS,EAChB,IAAMC,EAAMD,EAAO,WAAW,KAAM,CAClC,mBAAoB,EACtB,CAAC,EACGC,IACFH,EAAOG,EACPH,EAAK,yBAA2B,OAChCC,EAAeD,EAAK,qBAAqB,EAAG,EAAG,EAAG,CAAC,EAEvD,MACM,CAEN,CASO,SAAS3B,EAAQe,EAAqB,CAE3C,GAAIA,EAAI,MAAM,gBAAgB,EAC5B,OAAQA,EAAI,OAAQ,CAClB,IAAK,GACH,OAAAH,EAAK,SAASG,EAAI,MAAM,EAAG,CAAC,EAAE,OAAO,CAAC,EAAG,EAAE,EAC3CF,EAAK,SAASE,EAAI,MAAM,EAAG,CAAC,EAAE,OAAO,CAAC,EAAG,EAAE,EAC3CD,EAAK,SAASC,EAAI,MAAM,EAAG,CAAC,EAAE,OAAO,CAAC,EAAG,EAAE,EACpCvB,EAAS,QAAQoB,EAAIC,EAAIC,CAAE,EAEpC,IAAK,GACH,OAAAF,EAAK,SAASG,EAAI,MAAM,EAAG,CAAC,EAAE,OAAO,CAAC,EAAG,EAAE,EAC3CF,EAAK,SAASE,EAAI,MAAM,EAAG,CAAC,EAAE,OAAO,CAAC,EAAG,EAAE,EAC3CD,EAAK,SAASC,EAAI,MAAM,EAAG,CAAC,EAAE,OAAO,CAAC,EAAG,EAAE,EAC3CV,EAAK,SAASU,EAAI,MAAM,EAAG,CAAC,EAAE,OAAO,CAAC,EAAG,EAAE,EACpCvB,EAAS,QAAQoB,EAAIC,EAAIC,EAAIT,CAAE,EAExC,IAAK,GACH,MAAO,CACL,IAAAU,EACA,MAAO,SAASA,EAAI,MAAM,CAAC,EAAG,EAAE,GAAK,EAAI,OAAU,CACrD,EACF,IAAK,GACH,MAAO,CACL,IAAAA,EACA,KAAM,SAASA,EAAI,MAAM,CAAC,EAAG,EAAE,IAAM,CACvC,CACJ,CAIF,IAAMgB,EAAYhB,EAAI,MAAM,oFAAoF,EAChH,GAAIgB,EACF,OAAAnB,EAAK,SAASmB,EAAU,CAAC,CAAC,EAC1BlB,EAAK,SAASkB,EAAU,CAAC,CAAC,EAC1BjB,EAAK,SAASiB,EAAU,CAAC,CAAC,EAC1B1B,EAAK,KAAK,OAAO0B,EAAU,CAAC,IAAM,OAAY,EAAI,WAAWA,EAAU,CAAC,CAAC,GAAK,GAAI,EAC3EvC,EAAS,QAAQoB,EAAIC,EAAIC,EAAIT,CAAE,EAIxC,GAAI,CAACsB,GAAQ,CAACC,EACZ,MAAM,IAAI,MAAM,qCAAqC,EAOvD,GAFAD,EAAK,UAAYC,EACjBD,EAAK,UAAYZ,EACb,OAAOY,EAAK,WAAc,SAC5B,MAAM,IAAI,MAAM,qCAAqC,EAOvD,GAJAA,EAAK,SAAS,EAAG,EAAG,EAAG,CAAC,EACxB,CAACf,EAAIC,EAAIC,EAAIT,CAAE,EAAIsB,EAAK,aAAa,EAAG,EAAG,EAAG,CAAC,EAAE,KAG7CtB,IAAO,IACT,MAAM,IAAI,MAAM,qCAAqC,EAMvD,MAAO,CACL,KAAMb,EAAS,OAAOoB,EAAIC,EAAIC,EAAIT,CAAE,EACpC,IAAAU,CACF,CACF,CApEOA,EAAS,QAAAf,IA7BDe,IAAA,IAuGV,IAAUiB,MAAV,CAOE,SAASC,EAAkBD,EAAqB,CACrD,OAAOE,EACJF,GAAO,GAAM,IACbA,GAAO,EAAM,IACbA,EAAa,GAAI,CACtB,CALOA,EAAS,kBAAAC,EAeT,SAASC,EAAmB,EAAWvC,EAAWC,EAAmB,CAC1E,IAAMuC,EAAK,EAAI,IACTC,EAAKzC,EAAI,IACT0C,EAAKzC,EAAI,IACT0C,EAAKH,GAAM,OAAUA,EAAK,MAAQ,KAAK,KAAKA,EAAK,MAAS,MAAO,GAAG,EACpEI,EAAKH,GAAM,OAAUA,EAAK,MAAQ,KAAK,KAAKA,EAAK,MAAS,MAAO,GAAG,EACpEI,EAAKH,GAAM,OAAUA,EAAK,MAAQ,KAAK,KAAKA,EAAK,MAAS,MAAO,GAAG,EAC1E,OAAOC,EAAK,MAASC,EAAK,MAASC,EAAK,KAC1C,CAROR,EAAS,mBAAAE,IAtBDF,IAAA,IAoCV,IAAUhB,MAAV,CACE,SAASd,EAAMC,EAAYC,EAAoB,CAEpD,GADAC,GAAMD,EAAK,KAAQ,IACfC,IAAO,EACT,OAAOD,EAET,IAAME,EAAOF,GAAM,GAAM,IACnBG,EAAOH,GAAM,GAAM,IACnBI,EAAOJ,GAAM,EAAK,IAClBK,EAAON,GAAM,GAAM,IACnBO,EAAOP,GAAM,GAAM,IACnBQ,EAAOR,GAAM,EAAK,IACxB,OAAAS,EAAKH,EAAM,KAAK,OAAOH,EAAMG,GAAOJ,CAAE,EACtCQ,EAAKH,EAAM,KAAK,OAAOH,EAAMG,GAAOL,CAAE,EACtCS,EAAKH,EAAM,KAAK,OAAOH,EAAMG,GAAON,CAAE,EAC/Bb,EAAS,OAAOoB,EAAIC,EAAIC,CAAE,CACnC,CAfOE,EAAS,MAAAd,EA8BT,SAASgB,EAAoBuB,EAAgBC,EAAgBvB,EAAmC,CACrG,IAAMwB,EAAMX,EAAI,kBAAkBS,GAAU,CAAC,EACvCG,EAAMZ,EAAI,kBAAkBU,GAAU,CAAC,EAE7C,GADWG,EAAcF,EAAKC,CAAG,EACxBzB,EAAO,CACd,GAAIyB,EAAMD,EAAK,CACb,IAAMG,EAAUC,EAAgBN,EAAQC,EAAQvB,CAAK,EAC/C6B,EAAeH,EAAcF,EAAKX,EAAI,kBAAkBc,GAAW,CAAC,CAAC,EAC3E,GAAIE,EAAe7B,EAAO,CACxB,IAAM8B,EAAUC,EAAkBT,EAAQC,EAAQvB,CAAK,EACjDgC,EAAeN,EAAcF,EAAKX,EAAI,kBAAkBiB,GAAW,CAAC,CAAC,EAC3E,OAAOD,EAAeG,EAAeL,EAAUG,CACjD,CACA,OAAOH,CACT,CACA,IAAMA,EAAUI,EAAkBT,EAAQC,EAAQvB,CAAK,EACjD6B,EAAeH,EAAcF,EAAKX,EAAI,kBAAkBc,GAAW,CAAC,CAAC,EAC3E,GAAIE,EAAe7B,EAAO,CACxB,IAAM8B,EAAUF,EAAgBN,EAAQC,EAAQvB,CAAK,EAC/CgC,EAAeN,EAAcF,EAAKX,EAAI,kBAAkBiB,GAAW,CAAC,CAAC,EAC3E,OAAOD,EAAeG,EAAeL,EAAUG,CACjD,CACA,OAAOH,CACT,CAEF,CAzBO9B,EAAS,oBAAAE,EA2BT,SAAS6B,EAAgBN,EAAgBC,EAAgBvB,EAAuB,CAGrF,IAAMV,EAAOgC,GAAU,GAAM,IACvB/B,EAAO+B,GAAU,GAAM,IACvB9B,EAAO8B,GAAW,EAAK,IACzBnC,EAAOoC,GAAU,GAAM,IACvBnC,EAAOmC,GAAU,GAAM,IACvBlC,EAAOkC,GAAW,EAAK,IACvBU,EAAKP,EAAcb,EAAI,mBAAmB1B,EAAKC,EAAKC,CAAG,EAAGwB,EAAI,mBAAmBvB,EAAKC,EAAKC,CAAG,CAAC,EACnG,KAAOyC,EAAKjC,IAAUb,EAAM,GAAKC,EAAM,GAAKC,EAAM,IAEhDF,GAAO,KAAK,IAAI,EAAG,KAAK,KAAKA,EAAM,EAAG,CAAC,EACvCC,GAAO,KAAK,IAAI,EAAG,KAAK,KAAKA,EAAM,EAAG,CAAC,EACvCC,GAAO,KAAK,IAAI,EAAG,KAAK,KAAKA,EAAM,EAAG,CAAC,EACvC4C,EAAKP,EAAcb,EAAI,mBAAmB1B,EAAKC,EAAKC,CAAG,EAAGwB,EAAI,mBAAmBvB,EAAKC,EAAKC,CAAG,CAAC,EAEjG,OAAQL,GAAO,GAAKC,GAAO,GAAKC,GAAO,EAAI,OAAU,CACvD,CAlBOQ,EAAS,gBAAA+B,EAoBT,SAASG,EAAkBT,EAAgBC,EAAgBvB,EAAuB,CAGvF,IAAMV,EAAOgC,GAAU,GAAM,IACvB/B,EAAO+B,GAAU,GAAM,IACvB9B,EAAO8B,GAAW,EAAK,IACzBnC,EAAOoC,GAAU,GAAM,IACvBnC,EAAOmC,GAAU,GAAM,IACvBlC,EAAOkC,GAAW,EAAK,IACvBU,EAAKP,EAAcb,EAAI,mBAAmB1B,EAAKC,EAAKC,CAAG,EAAGwB,EAAI,mBAAmBvB,EAAKC,EAAKC,CAAG,CAAC,EACnG,KAAOyC,EAAKjC,IAAUb,EAAM,KAAQC,EAAM,KAAQC,EAAM,MAEtDF,EAAM,KAAK,IAAI,IAAMA,EAAM,KAAK,MAAM,IAAMA,GAAO,EAAG,CAAC,EACvDC,EAAM,KAAK,IAAI,IAAMA,EAAM,KAAK,MAAM,IAAMA,GAAO,EAAG,CAAC,EACvDC,EAAM,KAAK,IAAI,IAAMA,EAAM,KAAK,MAAM,IAAMA,GAAO,EAAG,CAAC,EACvD4C,EAAKP,EAAcb,EAAI,mBAAmB1B,EAAKC,EAAKC,CAAG,EAAGwB,EAAI,mBAAmBvB,EAAKC,EAAKC,CAAG,CAAC,EAEjG,OAAQL,GAAO,GAAKC,GAAO,GAAKC,GAAO,EAAI,OAAU,CACvD,CAlBOQ,EAAS,kBAAAkC,EAoBT,SAASG,EAAWC,EAAiD,CAC1E,MAAO,CAAEA,GAAS,GAAM,IAAOA,GAAS,GAAM,IAAOA,GAAS,EAAK,IAAMA,EAAQ,GAAI,CACvF,CAFOtC,EAAS,WAAAqC,IAlGDrC,IAAA,IAuGV,SAASlB,EAAYyD,EAAmB,CAC7C,IAAMC,EAAID,EAAE,SAAS,EAAE,EACvB,OAAOC,EAAE,OAAS,EAAI,IAAMA,EAAIA,CAClC,CAQO,SAASX,EAAcY,EAAYC,EAAoB,CAC5D,OAAID,EAAKC,GACCA,EAAK,MAASD,EAAK,MAErBA,EAAK,MAASC,EAAK,IAC7B,CCnMO,IAAMC,EAAsB,OAAO,QAAQ,IAAM,CACtD,IAAMC,EAAS,CAEbC,EAAI,QAAQ,SAAS,EACrBA,EAAI,QAAQ,SAAS,EACrBA,EAAI,QAAQ,SAAS,EACrBA,EAAI,QAAQ,SAAS,EACrBA,EAAI,QAAQ,SAAS,EACrBA,EAAI,QAAQ,SAAS,EACrBA,EAAI,QAAQ,SAAS,EACrBA,EAAI,QAAQ,SAAS,EAErBA,EAAI,QAAQ,SAAS,EACrBA,EAAI,QAAQ,SAAS,EACrBA,EAAI,QAAQ,SAAS,EACrBA,EAAI,QAAQ,SAAS,EACrBA,EAAI,QAAQ,SAAS,EACrBA,EAAI,QAAQ,SAAS,EACrBA,EAAI,QAAQ,SAAS,EACrBA,EAAI,QAAQ,SAAS,CACvB,EAIMC,EAAI,CAAC,EAAM,GAAM,IAAM,IAAM,IAAM,GAAI,EAC7C,QAASC,EAAI,EAAGA,EAAI,IAAKA,IAAK,CAC5B,IAAM,EAAID,EAAGC,EAAI,GAAM,EAAI,CAAC,EACtBC,EAAIF,EAAGC,EAAI,EAAK,EAAI,CAAC,EACrBE,EAAIH,EAAEC,EAAI,CAAC,EACjBH,EAAO,KAAK,CACV,IAAKM,EAAS,MAAM,EAAGF,EAAGC,CAAC,EAC3B,KAAMC,EAAS,OAAO,EAAGF,EAAGC,CAAC,CAC/B,CAAC,CACH,CAGA,QAASF,EAAI,EAAGA,EAAI,GAAIA,IAAK,CAC3B,IAAMI,EAAI,EAAIJ,EAAI,GAClBH,EAAO,KAAK,CACV,IAAKM,EAAS,MAAMC,EAAGA,EAAGA,CAAC,EAC3B,KAAMD,EAAS,OAAOC,EAAGA,EAAGA,CAAC,CAC/B,CAAC,CACH,CAEA,OAAOP,CACT,GAAG,CAAC,ECrNJ,SAASQ,EAAUC,EAAeC,EAAaC,EAAsB,CACnE,OAAO,KAAK,IAAID,EAAK,KAAK,IAAID,EAAOE,CAAI,CAAC,CAC5C,CAEA,SAASC,EAAeC,EAAmB,CACzC,OAAQA,EAAG,CACT,IAAK,IAAK,MAAO,QACjB,IAAK,IAAK,MAAO,MACnB,CACA,OAAOA,CACT,CAGA,IAAeC,EAAf,KAAoC,CAClC,YACqBC,EACnB,CADmB,aAAAA,CAErB,CAEO,UAAUC,EAAqBC,EAA8C,CAElF,IAAMC,EAAQ,KAAK,QAAQ,YAAY,EACjCC,EAAQ,KAAK,QAAQ,YAAY,EACnCC,EAAUF,EAERG,EAAWL,EAAM,MAAM,EACvBM,EAASN,EAAM,IAAI,EACnBO,EAAcP,EAAM,MAAM,EAC1BQ,EAAYR,EAAM,IAAI,EAE5B,KAAK,iBAAiBM,EAASD,EAAUA,EAAUC,CAAM,EAEzD,QAASG,EAAMJ,EAAUI,GAAOH,EAAQG,IAAO,CAC7C,IAAMC,EAAO,KAAK,QAAQ,QAAQD,CAAG,EACrC,GAAIC,EAAM,CACR,IAAMC,EAAkBF,IAAQT,EAAM,MAAM,EAAIO,EAAc,EACxDK,EAAgBH,IAAQT,EAAM,IAAI,EAAIQ,EAAWE,EAAK,OAC5D,QAASG,EAAMF,EAAiBE,EAAMD,EAAeC,IAAO,CAC1D,IAAMhB,EAAIa,EAAK,QAAQG,EAAKT,IAAYF,EAAQC,EAAQD,CAAK,EAC7D,GAAI,CAACL,EAAG,CACN,QAAQ,KAAK,yBAAyBY,CAAG,SAASI,CAAG,EAAE,EACvD,QACF,CACA,KAAK,UAAUhB,EAAGO,EAASK,EAAKI,CAAG,EACnCT,EAAUP,CACZ,CACF,CACA,KAAK,QAAQY,EAAKA,IAAQH,CAAM,CAClC,CAEA,YAAK,gBAAgB,EAEd,KAAK,iBAAiBL,CAA0B,CACzD,CAEU,UAAUa,EAAmBV,EAAsBK,EAAaI,EAAmB,CAAE,CACrF,QAAQJ,EAAaM,EAA0B,CAAE,CACjD,iBAAiBC,EAAcX,EAAkBC,EAAsB,CAAE,CACzE,iBAAwB,CAAE,CAC1B,iBAAiBL,EAA8C,CAAE,MAAO,EAAI,CACxF,EAEA,SAASgB,EAAQf,EAAqCC,EAA6B,CACjF,OAAOD,EAAM,eAAe,IAAMC,EAAM,eAAe,GAClDD,EAAM,WAAW,IAAMC,EAAM,WAAW,CAC/C,CAEA,SAASe,EAAQhB,EAAqCC,EAA6B,CACjF,OAAOD,EAAM,eAAe,IAAMC,EAAM,eAAe,GAClDD,EAAM,WAAW,IAAMC,EAAM,WAAW,CAC/C,CAEA,SAASgB,EAAWjB,EAAqCC,EAA6B,CACpF,OAAOD,EAAM,UAAU,IAAMC,EAAM,UAAU,GACxCD,EAAM,OAAO,IAAMC,EAAM,OAAO,GAChCD,EAAM,YAAY,IAAMC,EAAM,YAAY,GAC1CD,EAAM,WAAW,IAAMC,EAAM,WAAW,GACxCD,EAAM,QAAQ,IAAMC,EAAM,QAAQ,GAClCD,EAAM,YAAY,IAAMC,EAAM,YAAY,GAC1CD,EAAM,SAAS,IAAMC,EAAM,SAAS,GACpCD,EAAM,MAAM,IAAMC,EAAM,MAAM,GAC9BD,EAAM,gBAAgB,IAAMC,EAAM,gBAAgB,CACzD,CAEA,IAAMiB,EAAN,cAAqCtB,CAAqB,CA2BxD,YACEuB,EACiBC,EACjB,CACA,MAAMD,CAAM,EAFK,eAAAC,EA5BnB,KAAQ,UAAoB,EAC5B,KAAQ,SAAqB,IAAI,MACjC,KAAQ,kBAA8B,IAAI,MAC1C,KAAQ,YAAsB,GAC9B,KAAQ,eAAyB,EAKjC,KAAQ,aAA4B,KAAK,QAAQ,YAAY,EAK7D,KAAQ,gBAA0B,EAClC,KAAQ,gBAA0B,EAGlC,KAAQ,gBAA+B,KAAK,QAAQ,YAAY,EAEhE,KAAQ,UAAoB,EAC5B,KAAQ,eAAyB,EACjC,KAAQ,eAAyB,EACjC,KAAQ,sBAAgC,EACxC,KAAQ,sBAAgC,EAgBxC,KAAQ,iBAAgC,KAAK,QAAQ,YAAY,EACjE,KAAQ,uBAAsC,KAAK,QAAQ,YAAY,EACvE,KAAQ,kBAAiC,KAAK,QAAQ,YAAY,CAXlE,CAEU,iBAAiBN,EAAcO,EAAeC,EAAmB,CACzE,KAAK,SAAW,IAAI,MAAcR,CAAI,EACtC,KAAK,sBAAwBO,EAC7B,KAAK,eAAiBA,EACtB,KAAK,UAAYA,CACnB,CAKU,QAAQd,EAAaM,EAA0B,CAGnD,KAAK,eAAiB,GAAK,CAACG,EAAQ,KAAK,aAAc,KAAK,eAAe,IAE7E,KAAK,aAAe,QAAU,KAAK,cAAc,KAGnD,IAAIO,EAAe,GAGnB,GAAI,CAACV,EAAW,CAEVN,EAAM,KAAK,WAAa,KAAK,UAAU,MACzC,KAAK,QAAQ,QAAQ,KAAK,eAAe,GAAG,QAAQ,KAAK,gBAAiB,KAAK,eAAe,EAIhG,IAAMiB,EAAc,KAAK,QAAQ,QAAQjB,CAAG,EAEtCkB,EAAW,KAAK,QAAQ,QAAQlB,EAAM,CAAC,EAE7C,GAAI,CAACkB,EAAS,UAEZF,EAAe;AAAA,EAEf,KAAK,eAAiBhB,EAAM,EAC5B,KAAK,eAAiB,MACjB,CACLgB,EAAe,GACf,IAAMG,EAAkBF,EAAY,QAAQA,EAAY,OAAS,EAAG,KAAK,gBAAgB,EACnFG,EAAwBH,EAAY,QAAQA,EAAY,OAAS,EAAG,KAAK,sBAAsB,EAC/FI,EAAmBH,EAAS,QAAQ,EAAG,KAAK,iBAAiB,EAC7DI,EAAgCD,EAAiB,SAAS,EAAI,EAIhEE,EAAU,IAIZF,EAAiB,SAAS,GACxBC,EAAgC,KAAK,gBAAkB,EAAI,KAAK,gBAAkB,MAKjFH,EAAgB,SAAS,GAAKA,EAAgB,SAAS,IAAM,IAG9DV,EAAQU,EAAiBE,CAAgB,IAEzCE,EAAU,IAMVD,IACCF,EAAsB,SAAS,GAAKA,EAAsB,SAAS,IAAM,IAG1EX,EAAQU,EAAiBE,CAAgB,GACzCZ,EAAQW,EAAuBC,CAAgB,IAE/CE,EAAU,KAITA,IAGHP,EAAe,IAAI,OAAO,KAAK,eAAiB,CAAC,EAEjDA,GAAgB,iBAEZ,KAAK,eAAiB,IAExBA,GAAgB,SAChBA,GAAgB,QAAUC,EAAY,OAAS,KAAK,cAAc,IAClED,GAAgB,QAAU,KAAK,cAAc,IAC7CA,GAAgB,QAAUC,EAAY,OAAS,KAAK,cAAc,IAClED,GAAgB,UAKlB,KAAK,sBAAwBhB,EAAM,EACnC,KAAK,sBAAwB,EAG7B,KAAK,eAAiBA,EAAM,EAC5B,KAAK,eAAiB,EAE1B,CACF,CAEA,KAAK,SAAS,KAAK,SAAS,EAAI,KAAK,YACrC,KAAK,kBAAkB,KAAK,WAAW,EAAIgB,EAC3C,KAAK,YAAc,GACnB,KAAK,eAAiB,CACxB,CAEQ,WAAWX,EAAoCV,EAAgC,CACrF,IAAM6B,EAAmB,CAAC,EACpBC,EAAY,CAACjB,EAAQH,EAAMV,CAAO,EAClC+B,EAAY,CAACjB,EAAQJ,EAAMV,CAAO,EAClCgC,EAAe,CAACjB,EAAWL,EAAMV,CAAO,EAE9C,GAAI8B,GAAaC,GAAaC,EAC5B,GAAItB,EAAK,mBAAmB,EACrBV,EAAQ,mBAAmB,GAC9B6B,EAAO,KAAK,CAAC,MAEV,CACL,GAAIC,EAAW,CACb,IAAMG,EAAQvB,EAAK,WAAW,EAC1BA,EAAK,QAAQ,EAAKmB,EAAO,KAAK,GAAI,EAAII,IAAU,GAAM,IAAOA,IAAU,EAAK,IAAMA,EAAQ,GAAI,EACzFvB,EAAK,YAAY,EACpBuB,GAAS,GAAMJ,EAAO,KAAK,GAAI,EAAGI,CAAK,EACpCJ,EAAO,KAAKI,EAAQ,EAAI,IAAMA,EAAQ,GAAK,IAAMA,EAAQ,EAAE,EAE7DJ,EAAO,KAAK,EAAE,CACvB,CACA,GAAIE,EAAW,CACb,IAAME,EAAQvB,EAAK,WAAW,EAC1BA,EAAK,QAAQ,EAAKmB,EAAO,KAAK,GAAI,EAAII,IAAU,GAAM,IAAOA,IAAU,EAAK,IAAMA,EAAQ,GAAI,EACzFvB,EAAK,YAAY,EACpBuB,GAAS,GAAMJ,EAAO,KAAK,GAAI,EAAGI,CAAK,EACpCJ,EAAO,KAAKI,EAAQ,EAAI,KAAOA,EAAQ,GAAK,IAAMA,EAAQ,EAAE,EAE9DJ,EAAO,KAAK,EAAE,CACvB,CACIG,IACEtB,EAAK,UAAU,IAAMV,EAAQ,UAAU,GAAK6B,EAAO,KAAKnB,EAAK,UAAU,EAAI,EAAI,EAAE,EACjFA,EAAK,OAAO,IAAMV,EAAQ,OAAO,GAAK6B,EAAO,KAAKnB,EAAK,OAAO,EAAI,EAAI,EAAE,EACxEA,EAAK,YAAY,IAAMV,EAAQ,YAAY,GAAK6B,EAAO,KAAKnB,EAAK,YAAY,EAAI,EAAI,EAAE,EACvFA,EAAK,WAAW,IAAMV,EAAQ,WAAW,GAAK6B,EAAO,KAAKnB,EAAK,WAAW,EAAI,GAAK,EAAE,EACrFA,EAAK,QAAQ,IAAMV,EAAQ,QAAQ,GAAK6B,EAAO,KAAKnB,EAAK,QAAQ,EAAI,EAAI,EAAE,EAC3EA,EAAK,YAAY,IAAMV,EAAQ,YAAY,GAAK6B,EAAO,KAAKnB,EAAK,YAAY,EAAI,EAAI,EAAE,EACvFA,EAAK,SAAS,IAAMV,EAAQ,SAAS,GAAK6B,EAAO,KAAKnB,EAAK,SAAS,EAAI,EAAI,EAAE,EAC9EA,EAAK,MAAM,IAAMV,EAAQ,MAAM,GAAK6B,EAAO,KAAKnB,EAAK,MAAM,EAAI,EAAI,EAAE,EACrEA,EAAK,gBAAgB,IAAMV,EAAQ,gBAAgB,GAAK6B,EAAO,KAAKnB,EAAK,gBAAgB,EAAI,EAAI,EAAE,EAE3G,CAGF,OAAOmB,CACT,CAEU,UAAUnB,EAAmBV,EAAsBK,EAAaI,EAAmB,CAI3F,GAF0BC,EAAK,SAAS,IAAM,EAG5C,OAIF,IAAMwB,EAAcxB,EAAK,SAAS,IAAM,GAElCmB,EAAS,KAAK,WAAWnB,EAAM,KAAK,YAAY,EAStD,GALqBwB,EAAc,CAACpB,EAAQ,KAAK,aAAcJ,CAAI,EAAImB,EAAO,OAAS,EAKrE,CAEZ,KAAK,eAAiB,IAEnBf,EAAQ,KAAK,aAAc,KAAK,eAAe,IAClD,KAAK,aAAe,QAAU,KAAK,cAAc,KAGnD,KAAK,aAAe,QAAU,KAAK,cAAc,IACjD,KAAK,eAAiB,GAGxB,KAAK,sBAAwB,KAAK,eAAiBT,EACnD,KAAK,sBAAwB,KAAK,eAAiBI,EAEnD,KAAK,aAAe,QAAUoB,EAAO,KAAK,GAAG,CAAC,IAG9C,IAAMvB,EAAO,KAAK,QAAQ,QAAQD,CAAG,EACjCC,IAAS,SACXA,EAAK,QAAQG,EAAK,KAAK,YAAY,EACnC,KAAK,gBAAkBJ,EACvB,KAAK,gBAAkBI,EAE3B,CAKIyB,EACF,KAAK,gBAAkBxB,EAAK,SAAS,GAEjC,KAAK,eAAiB,IAIpBI,EAAQ,KAAK,aAAc,KAAK,eAAe,EACjD,KAAK,aAAe,QAAU,KAAK,cAAc,KAEjD,KAAK,aAAe,QAAU,KAAK,cAAc,IACjD,KAAK,aAAe,QAAU,KAAK,cAAc,KAEnD,KAAK,eAAiB,GAGxB,KAAK,aAAeJ,EAAK,SAAS,EAGlC,KAAK,sBAAwB,KAAK,eAAiBL,EACnD,KAAK,sBAAwB,KAAK,eAAiBI,EAAMC,EAAK,SAAS,EAE3E,CAEU,iBAAiBb,EAA6C,CACtE,IAAIsC,EAAS,KAAK,SAAS,OAIvB,KAAK,QAAQ,OAAS,KAAK,WAAa,KAAK,UAAU,OACzDA,EAAS,KAAK,sBAAwB,EAAI,KAAK,UAC/C,KAAK,eAAiB,KAAK,sBAC3B,KAAK,eAAiB,KAAK,uBAG7B,IAAIC,EAAU,GAEd,QAAS,EAAI,EAAG,EAAID,EAAQ,IAC1BC,GAAW,KAAK,SAAS,CAAC,EACtB,EAAI,EAAID,IACVC,GAAW,KAAK,kBAAkB,CAAC,GAKvC,GAAI,CAACvC,EAA4B,CAC/B,IAAMwC,EAAgB,KAAK,QAAQ,MAAQ,KAAK,QAAQ,QAClDC,EAAgB,KAAK,QAAQ,QAE7BC,EAAeF,IAAkB,KAAK,gBAAkBC,IAAkB,KAAK,eAE/EE,EAAaC,GAAyB,CACtCA,EAAS,EACXL,GAAW,QAAUK,CAAM,IAClBA,EAAS,IAClBL,GAAW,QAAU,CAACK,CAAM,IAEhC,EASIF,KARcE,GAAyB,CACrCA,EAAS,EACXL,GAAW,QAAUK,CAAM,IAClBA,EAAS,IAClBL,GAAW,QAAU,CAACK,CAAM,IAEhC,GAGWJ,EAAgB,KAAK,cAAc,EAC5CG,EAAUF,EAAgB,KAAK,cAAc,EAEjD,CAKA,IAAMI,EAA+B,KAAK,UAAkB,MAAM,cAAc,aAC1Eb,EAAS,KAAK,WAAWa,EAAa,KAAK,YAAY,EAC7D,OAAIb,EAAO,OAAS,IAClBO,GAAW,QAAUP,EAAO,KAAK,GAAG,CAAC,KAGhCO,CACT,CACF,EAEaO,EAAN,KAA+D,CAG7D,SAASC,EAA0B,CACxC,KAAK,UAAYA,CACnB,CAEQ,6BAA6BA,EAAoB3B,EAAiB4B,EAA6B,CACrG,IAAMC,EAAU7B,EAAO,OACjB8B,EAAeF,IAAe,OAAaC,EAAU1D,EAAUyD,EAAaD,EAAS,KAAM,EAAGE,CAAO,EAC3G,OAAO,KAAK,wBAAwBF,EAAU3B,EAAQ,CACpD,MAAO6B,EAAUC,EACjB,IAAKD,EAAU,CACjB,EAAG,EAAK,CACV,CAEQ,wBAAwBF,EAAoB3B,EAAiBrB,EAAwBC,EAA6C,CAExI,OADgB,IAAImB,EAAuBC,EAAQ2B,CAAQ,EAC5C,UAAU,CACvB,MAAO,CAAE,EAAG,EAAe,EAAG,OAAOhD,EAAM,OAAU,SAAWA,EAAM,MAAQA,EAAM,MAAM,IAAK,EAC/F,IAAO,CAAE,EAAGgD,EAAS,KAAM,EAAG,OAAOhD,EAAM,KAAU,SAAWA,EAAM,IAAQA,EAAM,IAAI,IAAO,CACjG,EAAGC,CAA0B,CAC/B,CAEQ,uBAAuB+C,EAAoBI,EAAiD,CAClG,IAAM/B,EAAS2B,EAAS,OAAO,OACzBK,EAAU,IAAIC,EAAqBjC,EAAQ2B,EAAUI,CAAO,EAElE,GAAI,EADkBA,EAAQ,eAAiB,IAC3B,CAClB,IAAMF,EAAU7B,EAAO,OACjB4B,EAAaG,EAAQ,WACrBD,EAAeF,IAAe,OAAaC,EAAU1D,EAAUyD,EAAaD,EAAS,KAAM,EAAGE,CAAO,EAC3G,OAAOG,EAAQ,UAAU,CACvB,MAAO,CAAE,EAAG,EAAe,EAAGH,EAAUC,CAAY,EACpD,IAAO,CAAE,EAAGH,EAAS,KAAM,EAAGE,EAAU,CAAY,CACtD,CAAC,CACH,CAEA,IAAMK,EAAY,KAAK,WAAW,qBAAqB,EACvD,OAAIA,IAAc,OACTF,EAAQ,UAAU,CACvB,MAAO,CAAE,EAAGE,EAAU,MAAM,EAAG,EAAGA,EAAU,MAAM,CAAE,EACpD,IAAO,CAAE,EAAGA,EAAU,IAAI,EAAK,EAAGA,EAAU,IAAI,CAAI,CACtD,CAAC,EAGI,EACT,CAEQ,gBAAgBP,EAA4B,CAClD,IAAIR,EAAU,GACRgB,EAAQR,EAAS,MAevB,GAZIQ,EAAM,4BAA2BhB,GAAW,YAC5CgB,EAAM,wBAAuBhB,GAAW,aACxCgB,EAAM,qBAAoBhB,GAAW,eACrCgB,EAAM,aAAYhB,GAAW,WAC7BgB,EAAM,aAAYhB,GAAW,YAC7BgB,EAAM,wBAAuBhB,GAAW,aACxCgB,EAAM,gBAAehB,GAAW,eAGhCgB,EAAM,iBAAmB,KAAOhB,GAAW,YAG3CgB,EAAM,oBAAsB,OAC9B,OAAQA,EAAM,kBAAmB,CAC/B,IAAK,MAAOhB,GAAW,WAAY,MACnC,IAAK,QAASA,GAAW,cAAe,MACxC,IAAK,OAAQA,GAAW,cAAe,MACvC,IAAK,MAAOA,GAAW,cAAe,KACxC,CAGF,OAAOA,CACT,CAEO,UAAUY,EAAqC,CAEpD,GAAI,CAAC,KAAK,UACR,MAAM,IAAI,MAAM,2CAA2C,EAI7D,IAAIZ,EAAUY,GAAS,MACnB,KAAK,wBAAwB,KAAK,UAAW,KAAK,UAAU,OAAO,OAAQA,EAAQ,MAAO,EAAI,EAC9F,KAAK,6BAA6B,KAAK,UAAW,KAAK,UAAU,OAAO,OAAQA,GAAS,UAAU,EAGvG,GAAI,CAACA,GAAS,kBACR,KAAK,UAAU,OAAO,OAAO,OAAS,YAAa,CACrD,IAAMK,EAA2B,KAAK,6BAA6B,KAAK,UAAW,KAAK,UAAU,OAAO,UAAW,MAAS,EAC7HjB,GAAW,oBAAwBiB,CAAwB,EAC7D,CAIF,OAAKL,GAAS,eACZZ,GAAW,KAAK,gBAAgB,KAAK,SAAS,GAGzCA,CACT,CAEO,gBAAgBY,EAAkD,CACvE,GAAI,CAAC,KAAK,UACR,MAAM,IAAI,MAAM,2CAA2C,EAG7D,OAAO,KAAK,uBAAuB,KAAK,UAAWA,GAAW,CAAC,CAAC,CAClE,CAEO,SAAgB,CAAE,CAC3B,EAEaE,EAAN,cAAmCxD,CAAqB,CAO7D,YACEuB,EACiBC,EACAoC,EACjB,CACA,MAAMrC,CAAM,EAHK,eAAAC,EACA,cAAAoC,EATnB,KAAQ,YAAsB,GAE9B,KAAQ,aAAe,GAYhBpC,EAAkB,MAAM,cAC3B,KAAK,YAAeA,EAAkB,MAAM,cAAc,OAAO,KAGjE,KAAK,YAAcqC,CAEvB,CAEQ,UAAUC,EAAgBC,EAAsBC,EAA2B,CAGjF,OAFAD,EAAeA,GAAgB,EAC/BC,EAAYA,GAAa,IACrBF,EAAO,OAASC,EACXD,GAGTC,GAAgBD,EAAO,OACnBC,EAAeC,EAAU,SAC3BA,GAAaA,EAAU,OAAOD,EAAeC,EAAU,MAAM,GAExDA,EAAU,MAAM,EAAGD,CAAY,EAAID,EAC5C,CAEU,iBAAiB5C,EAAcO,EAAeC,EAAmB,CACzE,KAAK,cAAgB,wCAErB,IAAIuC,EAAa,UACbC,EAAa,WACb,KAAK,SAAS,yBAA2B,MAC3CD,EAAa,KAAK,UAAU,QAAQ,OAAO,YAAc,UACzDC,EAAa,KAAK,UAAU,QAAQ,OAAO,YAAc,WAG3D,IAAMC,EAAyB,CAAC,EAChCA,EAAuB,KAAK,UAAYF,EAAa,GAAG,EACxDE,EAAuB,KAAK,qBAAuBD,EAAa,GAAG,EACnEC,EAAuB,KAAK,gBAAkB,KAAK,UAAU,QAAQ,WAAa,GAAG,EACrFA,EAAuB,KAAK,cAAgB,KAAK,UAAU,QAAQ,SAAW,KAAK,EACnF,KAAK,cAAgB,eAAkBA,EAAuB,KAAK,GAAG,EAAI,IAC5E,CAEU,iBAAwB,CAChC,KAAK,cAAgB,SACrB,KAAK,cAAgB,wCACvB,CAEU,QAAQxD,EAAaM,EAA0B,CACvD,KAAK,cAAgB,cAAgB,KAAK,YAAc,gBACxD,KAAK,YAAc,EACrB,CAEQ,aAAaD,EAAmBoD,EAAmC,CACzE,IAAM7B,EAAQ6B,EAAOpD,EAAK,WAAW,EAAIA,EAAK,WAAW,EACzD,GAAIoD,EAAOpD,EAAK,QAAQ,EAAIA,EAAK,QAAQ,EAMvC,MAAO,IALK,CACTuB,GAAS,GAAM,IACfA,GAAU,EAAK,IACfA,EAAe,GAClB,EACiB,IAAI8B,GAAK,KAAK,UAAUA,EAAE,SAAS,EAAE,EAAG,EAAG,GAAG,CAAC,EAAE,KAAK,EAAE,EAE3E,GAAID,EAAOpD,EAAK,YAAY,EAAIA,EAAK,YAAY,EAC/C,OAAO,KAAK,YAAYuB,CAAK,EAAE,GAGnC,CAEQ,WAAWvB,EAAmBV,EAA4C,CAChF,IAAMoC,EAAoB,CAAC,EAErBN,EAAY,CAACjB,EAAQH,EAAMV,CAAO,EAClC+B,EAAY,CAACjB,EAAQJ,EAAMV,CAAO,EAClCgC,EAAe,CAACjB,EAAWL,EAAMV,CAAO,EAE9C,GAAI8B,GAAaC,GAAaC,EAAc,CAC1C,IAAMgC,EAAa,KAAK,aAAatD,EAAM,EAAI,EAC3CsD,GACF5B,EAAQ,KAAK,UAAY4B,EAAa,GAAG,EAG3C,IAAMC,EAAa,KAAK,aAAavD,EAAM,EAAK,EAChD,OAAIuD,GACF7B,EAAQ,KAAK,qBAAuB6B,EAAa,GAAG,EAGlDvD,EAAK,UAAU,GAAK0B,EAAQ,KAAK,4CAA4C,EAC7E1B,EAAK,OAAO,GAAK0B,EAAQ,KAAK,oBAAoB,EAClD1B,EAAK,YAAY,GAAKA,EAAK,WAAW,EAAK0B,EAAQ,KAAK,sCAAsC,EACzF1B,EAAK,YAAY,EAAK0B,EAAQ,KAAK,6BAA6B,EAChE1B,EAAK,WAAW,GAAK0B,EAAQ,KAAK,4BAA4B,EACnE1B,EAAK,QAAQ,GAAK0B,EAAQ,KAAK,yBAAyB,EACxD1B,EAAK,YAAY,GAAK0B,EAAQ,KAAK,qBAAqB,EACxD1B,EAAK,SAAS,GAAK0B,EAAQ,KAAK,qBAAqB,EACrD1B,EAAK,MAAM,GAAK0B,EAAQ,KAAK,eAAe,EAC5C1B,EAAK,gBAAgB,GAAK0B,EAAQ,KAAK,gCAAgC,EAEpEA,CACT,CAGF,CAEU,UAAU1B,EAAmBV,EAAsBK,EAAaI,EAAmB,CAG3F,GAD0BC,EAAK,SAAS,IAAM,EAE5C,OAIF,IAAMwB,EAAcxB,EAAK,SAAS,IAAM,GAElCwD,EAAmB,KAAK,WAAWxD,EAAMV,CAAO,EAGlDkE,IACF,KAAK,aAAeA,EAAiB,SAAW,EAC9C,gBACA,uBAA0BA,EAAiB,KAAK,GAAG,EAAI,MAIvDhC,EACF,KAAK,aAAe,IAEpB,KAAK,aAAe1C,EAAekB,EAAK,SAAS,CAAC,CAEtD,CAEU,kBAA2B,CACnC,OAAO,KAAK,YACd,CACF", "names": ["$r", "$g", "$b", "$a", "channels", "to<PERSON>s", "r", "g", "b", "a", "toPaddedHex", "toRgba", "toColor", "color", "blend", "bg", "fg", "$a", "fgR", "fgG", "fgB", "bgR", "bgG", "bgB", "$r", "$g", "$b", "css", "rgba", "isOpaque", "ensureContrastRatio", "ratio", "result", "opaque", "rgbaColor", "opacity", "multiplyOpacity", "factor", "toColorRGB", "$ctx", "$litmusColor", "canvas", "ctx", "rgbaMatch", "rgb", "relativeLuminance", "relativeLuminance2", "rs", "gs", "bs", "rr", "rg", "rb", "bgRgba", "fgRgba", "bgL", "fgL", "contrastRatio", "resultA", "reduceLuminance", "resultARatio", "resultB", "increaseLuminance", "resultBRatio", "cr", "toChannels", "value", "c", "s", "l1", "l2", "DEFAULT_ANSI_COLORS", "colors", "css", "v", "i", "g", "b", "channels", "c", "constrain", "value", "low", "high", "escapeHTMLChar", "c", "BaseSerializeHandler", "_buffer", "range", "excludeFinalCursorPosition", "cell1", "cell2", "oldCell", "startRow", "endRow", "startColumn", "endColumn", "row", "line", "startLineColumn", "endLineColumn", "col", "cell", "isLastRow", "rows", "equalFg", "equalBg", "equalFlags", "StringSerializeHandler", "buffer", "_terminal", "start", "end", "rowSeparator", "currentLine", "nextLine", "thisRowLastChar", "thisRowLastSecondChar", "nextRowFirstChar", "isNextRowFirstCharDoubleWidth", "<PERSON><PERSON><PERSON><PERSON>", "sgrSeq", "fgChanged", "bgChanged", "flagsChanged", "color", "isEmptyCell", "rowEnd", "content", "realCursorRow", "realCursorCol", "cursorMoved", "moveRight", "offset", "curAttrData", "SerializeAddon", "terminal", "scrollback", "maxRows", "correctRows", "options", "handler", "HTMLSerializeHandler", "selection", "modes", "alternativeScreenContent", "_options", "DEFAULT_ANSI_COLORS", "target", "targetLength", "padString", "foreground", "background", "globalStyleDefinitions", "isFg", "x", "fgHexColor", "bgHexColor", "styleDefinitions"]}