{"registrations": [{"component": {"type": "git", "git": {"repositoryUrl": "https://github.com/withfig/autocomplete", "commitHash": "1cc34dc1ba530bb620bd380c25cfb9eb2264be89"}}, "version": "2.684.0", "license": {"type": "MIT", "url": "https://github.com/withfig/autocomplete/blob/main/LICENSE.md"}, "description": "IDE-style autocomplete for your existing terminal & shell from withfig/autocomplete."}, {"component": {"type": "git", "git": {"name": "amazon-q-developer-cli", "repositoryUrl": "https://github.com/aws/amazon-q-developer-cli", "commitHash": "f66e0b0e917ab185eef528dc36eca56b78ca8b5d"}}, "licenseDetail": ["MIT License", "", "Copyright (c) 2024 Amazon.com, Inc. or its affiliates.", "", "Permission is hereby granted, free of charge, to any person obtaining a copy", "of this software and associated documentation files (the \"Software\"), to deal", "in the Software without restriction, including without limitation the rights", "to use, copy, modify, merge, publish, distribute, sublicense, and/or sell", "copies of the Software, and to permit persons to whom the Software is", "furnished to do so, subject to the following conditions:", "", "The above copyright notice and this permission notice shall be included in all", "copies or substantial portions of the Software.", "", "THE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR", "IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,", "FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE", "AUTHORS OR <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> <PERSON><PERSON><PERSON>RS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER", "LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,", "OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE", "SOFTWARE."], "version": "f66e0b0e917ab185eef528dc36eca56b78ca8b5d"}, {"component": {"type": "git", "git": {"name": "@fig/autocomplete-shared", "repositoryUrl": "https://github.com/withfig/autocomplete-tools", "commitHash": "104377c19a91ca8a312cb38c115a74468f6227cb"}}, "licenseDetail": ["MIT License", "", "Copyright (c) 2021 Hercules Labs Inc. (Fig)", "", "Permission is hereby granted, free of charge, to any person obtaining a copy", "of this software and associated documentation files (the \"Software\"), to deal", "in the Software without restriction, including without limitation the rights", "to use, copy, modify, merge, publish, distribute, sublicense, and/or sell", "copies of the Software, and to permit persons to whom the Software is", "furnished to do so, subject to the following conditions:", "", "The above copyright notice and this permission notice shall be included in all", "copies or substantial portions of the Software.", "", "THE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR", "IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,", "FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE", "AUTHORS OR <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> <PERSON><PERSON><PERSON>RS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER", "LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,", "OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE", "SOFTWARE."], "version": "1.1.2"}, {"component": {"type": "git", "git": {"repositoryUrl": "https://github.com/zsh-users/zsh", "commitHash": "435cb1b748ce1f2f5c38edc1d64f4ee2424f9b3a"}}, "version": "5.9", "licenseDetail": ["Unless otherwise noted in the header of specific files, files in this distribution have the licence shown below.", "", "However, note that certain shell functions are licensed under versions of the GNU General Public Licence.  Anyone distributing the shell as a binary including those files needs to take account of this.  Search shell functions for \"Copyright\" for specific copyright information. None of the core functions are affected by this, so those files may simply be omitted.", "", "--", "", "The Z Shell is copyright (c) 1992-2017 <PERSON>, <PERSON>, <PERSON><PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, and others.  All rights reserved.  Individual authors, whether or not specifically named, retain copyright in all changes; in what follows, they are referred to as `the Zsh Development Group'.  This is for convenience only and this body has no legal status.  The Z shell is distributed under the following licence; any provisions made in individual files take precedence.", "", "Permission is hereby granted, without written agreement and without licence or royalty fees, to use, copy, modify, and distribute this software and to distribute modified versions of this software for any purpose, provided that the above copyright notice and the following two paragraphs appear in all copies of this software.", "", "In no event shall the Zsh Development Group be liable to any party for direct, indirect, special, incidental, or consequential damages arising out of the use of this software and its documentation, even if the Zsh Development Group have been advised of the possibility of such damage.", "", "The Zsh Development Group specifically disclaim any warranties, including, but not limited to, the implied warranties of merchantability and fitness for a particular purpose.  The software provided hereunder is on an \"as is\" basis, and the Zsh Development Group have no obligation to provide maintenance, support, updates, enhancements, or modifications."]}, {"component": {"type": "git", "git": {"repositoryUrl": "https://github.com/fish-shell/fish-shell", "commitHash": "6627d403d33b4e74b49aa4db2a4f17709628cdc8"}}, "version": "3.7.1", "licenseDetail": ["Fish is a smart and user-friendly command line shell.", "", "Copyright (C) 2005-2009 <PERSON>", "Copyright (C) 2009- fish-shell contributors", "", "fish is free software.", "", "Most of fish is licensed under the GNU General Public License version 2, and", "you can redistribute it and/or modify it under the terms of the GNU GPL as", "published by the Free Software Foundation.", "", "fish also includes software licensed under the Python Software Foundation License version 2, the MIT", "license, and the GNU Library General Public License version 2.", "", "Full licensing information is contained in doc_src/license.rst.", "", "This program is distributed in the hope that it will be useful, but WITHOUT", "ANY WARRANTY; without even the implied warranty of MERCHANTABILITY or", "FITNESS FOR A PARTICULAR PURPOSE.  See the GNU General Public License for", "more details."]}], "version": 1}